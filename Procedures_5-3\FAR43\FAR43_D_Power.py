# -*- coding: utf-8 -*-
"""
Created on Tue Jan  5 08:58:20 2021

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the FAR43 D Peak Output Power requiremensts.
             
     (1) Verify that the transponder RF output power is within specifications for the 
     class of transponder. Use the same conditions as described in (c)(1)(i), (ii), 
     and (iii) above.

         (i) For Class 1A and 2A ATCRBS transponders, verify that the minimum RF peak 
         output power is at least 21.0 dbw (125 watts).

         (ii) For Class 1B and 2B ATCRBS Transponders, verify that the minimum RF peak 
         output power is at least 18.5 dbw (70 watts).

         (iii) For Class 1A, 2A, 3A, and 4 and those Class 1B, 2B, and 3B Mode S 
         transponders that include the optional high RF peak output power, verify 
         that the minimum RF peak output power is at least 21.0 dbw (125 watts).

         (iv) For Classes 1B, 2B, and 3B Mode S transponders, verify that the minimum
         RF peak output power is at least 18.5 dbw (70 watts).

         (v) For any class of ATCRBS or any class of Mode S transponders, verify 
         that the maximum RF peak output power does not exceed 27.0 dbw (500 watts).    
             
INPUTS:      ate_rm,ATC5000NG,B4500CPwerMeter,RFBOB,DGBOB,PathLossTop,PathLossBot
OUTPUTS:     Power_ModeA_Bot -  Power Level in dBw, Bottom Antenna
             Power_ModeS_Bot -  Power Level in dBw, Bottom Antenna
             Power_ModeA_Top -  Power Level in dBw, Top Antenna
             Power_ModeS_Bot -  Power Level in dBw, Top Antenna
             
             NOTE: 
                1) PathLoss includes RFBOB loss and Cable Loss, this module uses
                both Prim Top and Prim Bottom RFBOB(50dBm) and Top and Bottom Cable
                (2.75,2.69 dBm). Power Values are adjusted per this values (should be around 52dBm)
                2) Conversion from dBm to dBw: P (dBw) = P (dBm) - 30.
             

HISTORY:
01/05/2021   MRS    Initial Release.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import RFBOB
from TXDLib.Handlers import DigitalBOB
from TXDLib.Handlers import B4500CPwrMeter


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def init_RFBOB_Bot(rfbob):
    """ initializes the RF BOB to Prim & Sec to BOTTOM port --- should be default. """
    #rfbob.connect()
    rfbob.setSwitch(0,0)   #Primary Bottom Port
    rfbob.setSwitch(1,0)   #Secondary Bottom Port
    rfbob.setSwitch(10,1)
    #rfbob.disconnect()

def Configure_DigitalBOB(dgbob):
    """ Configures Digital BOB for Power Meter Trigger. """
    dgbob.setTriggerSignal(2,15)
    dgbob.setTriggerSignal(8,11)



def init_RFBOB_Top(rfbob):
    """ initializes the RF BOB to Prim & Sec to Top port """
    #rfbob.connect()
    rfbob.setSwitch(0,1)   #Primary Top Port
    rfbob.setSwitch(1,1)   #Secondary Top Port
    rfbob.setSwitch(10,1)
    #rfbob.disconnect()

def pw_init(pw,mode):
    """ Initializes Power Meter to pulse mode, sets timebase, sets trigger""" 
    
    ### PUT METER IN PULSE MODE
    pw.autoset()         #Initialize to defaults
    time.sleep(5)
    #Mode and Input Channel
    pw.setCalculateMode('PULSE')
    pw.setCalculateUnits('dBm')
    pw.setCalculate1_on('ON')
    
    #Trigger
    pw.basicWrite("TRIGger:POSition LEFT")
    pw.basicWrite("TRIGger:SOURce TRIG1")   ######Ext Video Out from ATC
    pw.basicWrite("TRIGger:SLOPe POS")
    pw.basicWrite("TRIGger:MODE NORMAL")  #Importantae!
    pw.basicWrite("TRIGger:LEV 0.500")    #Importantae!
    
    #TimeBase
    pw.basicWrite("DISPlay:MODE GRAPH")
    if (mode == 'ModeA'):
        TimeBase = '10e-6'                 #Importantae!
        pw.setTimeBase(TimeBase)
    else:
        TimeBase = '20e-6'                 #Importantae!
        pw.setTimeBase(TimeBase)
       
    print("TimeBase-Pulse: ",pw.getTimeBase())
    print("TSPAN: ",pw.basicQuery("DISPlay:TSPAN?"))
    pw.setFrequency1('1.090e9')
      

def find_pulses(pw,title="PowerMeter (dBm vs time)"):
    """ using Power Meter, finds peaks, gets stats for each pulse, and plots. Returns string with
    pulse parameters and integer with number of pulses. """

    nPulse = pw.findpeaks('-25')      #actual level for max in dBm
    pwr = ""
    for i in range(nPulse):
        print("\n\nPULSE : ",i)
        pw.setpulsepositions(i)        # set markers for pulse i
        time.sleep(1)
        pwr = pw.getpwrmeasuremet()
    #plots the results
    #pw.plotpeaks('-20',title)            #comment this statment when running from TestStand
    return pwr, nPulse
    


##############################################################################
################# MAIN     ##################################################
##############################################################################
        
def FAR43_D(rm,atc,pwrmeter,rfbob,dgbob,PathLossTop,PathLossBot):
    """ FAR43, D - Peak Output Power """
    rm.logMessage(2,"*** FAR43 D, Peak Output Power ***")
    
    #initialize rfbob to Bottom
    init_RFBOB_Bot(rfbob)
    #configure DGBOB for PowerMeter trigger.
    Configure_DigitalBOB(dgbob)

    #Set the ATC Scope 1 Output Synch
    atc.gwrite(":ATC:SET:SCO:CH1 24")     #used as trigger for power meter
    rm.logMessage(1,"Step1 SetUp for Bottom Ant")

    #Initialize Power Levels and MTL Results
    Power_ModeA_Bot = 0.0                      #Values read by TestStand
    Power_ModeS_Bot = 0.0                      #Values read by TestStand
    Power_ModeA_Top = 0.0                      #Values read by TestStand
    Power_ModeS_Top = 0.0                      #Values read by TestStand

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set Up Transponder -- MODE A
    atc.transponderModeA()  
    atc.gwrite(":ATC:SET:GENA:SIG OFF") #Shut Off Two BOTTOM Generators
    atc.gwrite(":ATC:SET:GENB:SIG OFF") #Shut Off Two BOTTOM Generators
    atc.gwrite(":ATC:SET:GENC:SIG ON") #Turn ON Two BOTTOM Generators
    atc.gwrite(":ATC:SET:GEND:SIG ON") #Turn ON Two BOTTOM Generators
    atc.gwrite(":ATC:MEA:SET:TRIG:ANT BOTTOM") #Bottom Antenna
    rm.logMessage(2,"SetUp for Bot Ant")

        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(5)
    
    atc.waitforstatus()    
   
    #Step1:Measure Pulse Power -- Mode A  
    #initialize powermeter
    pw_init(pwrmeter,'ModeA')
    val = 0.0
    p_str, npulse = find_pulses(pwrmeter,'PowerMeter ModeA, Bot Antenna') 
    if npulse > 0:
        #get first pulse power
        p_str = p_str.split(',')
        print(p_str)
        val = float(p_str[3])
    else:
        rm.logMessage(3,"Step1 ModeA Pwr, Error, No Pulses")   
    
    #Step 1 Power Level    
    Power_ModeA_Bot = (val)    #dBm --see Notes
       
    #Turn OFF RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Set Up Transponder -- MODE S
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            
        
    #Step2:Measure Pulse Power -- Mode S  
    #initialize powermeter
    pw_init(pwrmeter,'ModeS')
    val = 0.0
    p_str, npulse = find_pulses(pwrmeter,'PowerMeter ModeS, Bot Antenna') 
    if npulse > 0:
        #get first pulse power
        p_str = p_str.split(',')
        print(p_str)
        val = float(p_str[3])
    else:
        print("Step1:Error, No Pulses Found")
        rm.logMessage(3,"Step2 ModeS Pwr, Error, No Pulses")   
    
    #Step 2 Power Level    
    Power_ModeS_Bot = (val)   #dBm --see Notes
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

   
    #Set RFBOB to Top Antenna
    init_RFBOB_Top(rfbob)
    #Set the ATC Scope 1 Output to Synch
    atc.gwrite(":ATC:SET:SCO:CH1 24")     #used as trigger for power meter
    rm.logMessage(1,"Step2 SetUp for Top Ant")

    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    atc.gwrite(":ATC:MEA:SET:TRIG:ANT TOP") #Top Antenna
    atc.gwrite(":ATC:SET:GENA:SIG ON") #Turn On Top Generators
    atc.gwrite(":ATC:SET:GENB:SIG ON") #Turn On Top Generators
    atc.gwrite(":ATC:SET:GENC:SIG OFF") #Shut Off Two BOTTOM Generators
    atc.gwrite(":ATC:SET:GEND:SIG OFF") #Shut Off Two BOTTOM Generators
    atc.gwrite(":ATC:MEA:SET:TRIG:ANT TOP") #Top Antenna
    rm.logMessage(2,"SetUp for Top Ant")
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(5)
    
    atc.waitforstatus()    

   
    #Step3:Measure Pulse Power -- Mode A  
    #initialize powermeter
    pw_init(pwrmeter,'ModeA')
    val = 0.0
    p_str, npulse = find_pulses(pwrmeter,'PowerMeter ModeA, Top Antenna') 
    if npulse > 0:
        #get first pulse power
        p_str = p_str.split(',')
        print(p_str)
        val = float(p_str[3])
    else:
        rm.logMessage(3,"Step1 ModeA Pwr, Error, No Pulses")   
    
    #Step 3 Power Level    
    Power_ModeA_Top = (val)   #dBw --see Notes

       
    #Turn OFF RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Set Up Transponder -- MODE S
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF

    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            
        
    #Step4:Measure Pulse Power -- Mode S  
    #initialize powermeter
    pw_init(pwrmeter,'ModeS')
    val = 0.0
    p_str, npulse = find_pulses(pwrmeter,'PowerMeter ModeS, Top Antenna') 
    if npulse > 0:
        #get first pulse power
        p_str = p_str.split(',')
        print(p_str)
        val = float(p_str[3])
    else:
        print("Step1:Error, No Pulses Found")
        rm.logMessage(3,"Step2 ModeS Pwr, Error, No Pulses")   
    
    #Step 2 Power Level    
    Power_ModeS_Top = (val)    #dBm --see Notes
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Adjust Power Levels by PathLoss, convert to dBw
    Power_ModeA_Bot = Power_ModeA_Bot + PathLossTop - 30.0
    Power_ModeS_Bot = Power_ModeS_Bot + PathLossTop - 30.0
    Power_ModeA_Top = Power_ModeA_Top + PathLossBot - 30.0
    Power_ModeS_Top = Power_ModeS_Top + PathLossBot - 30.0

    rm.logMessage(2,"Power's: %f,%f,%f,%f" % (Power_ModeA_Bot,Power_ModeS_Bot,Power_ModeA_Top,Power_ModeS_Top))   
    rm.logMessage(2,"Done, closing session")

    
    return [Power_ModeA_Bot,Power_ModeS_Bot,Power_ModeA_Top,Power_ModeS_Top]

##############################################################################
#run as main from command line
if __name__ == "__main__":
    
    rm = ate_rm()

    #Initialize the RFBOB
    rf_obj = RFBOB(rm)
    rf_obj.connect()
    
    #initialize the DigitalBOB
    dg_obj = DigitalBOB(rm)
    dg_obj.connect()

 
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

    pwr_obj = B4500CPwrMeter(rm)
    pwr_obj.Reset()
    
    #Power for Both Antennas
    res = FAR43_D(rm,atc_obj,pwr_obj,rf_obj,dg_obj,52.75,52.69)
    
    pwr_obj.close()
    atc_obj.close()
    rf_obj.disconnect()
    dg_obj.disconnect
 
