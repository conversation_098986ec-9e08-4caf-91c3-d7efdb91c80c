# twos comp conversion functions
"""
    converts input decimal integer to twos comp and returns converted
    integer. Determines whether integer is signed or not based on MSB.
    "bits" parameter determines size input binary number.
"""
def convert(val, bits): ## returns an int
    """compute the 2's complement of int value val"""
    if (val & (1 << (bits - 1))) != 0: # if sign bit is set e.g., 8bit: 128-255
        val = val - (1 << bits)        # compute negative value
    return val                         # return positive value as is


"""
    Because python formats negative binary numbers as just the binary 
    representation with a negative sign, this function takes that negative 
    binary string and returns the true twos comp conversion binary.
"""
def convert_return_binary_string(val,bits):
    if val[0] == '-':
        val = val[3:]
        val = val.zfill(bits)
        new_val = ""
        for bit in val:       
            if bit == '1':
                new_val += '0'
            else:
                new_val += '1'
        new_val = int(new_val,2)
        new_val = new_val + 1
        new_val = bin(new_val)
        return new_val
    else:
        return val


