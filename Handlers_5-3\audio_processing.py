# -*- coding: utf-8 -*-
"""
Created on Thu May 14 11:45:43 2020

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP

DESCRIPTON:
These clases provide methods to use the PXI Scope (5110) to acquire audio data (at 44100 Hz) and save the data to a Wave File.
There are class/methods to Plot or Play the WAV file(over the PC Speakers).  Methods are provided to filter and square_law wave data and detect pulses in
the Wave file.  The pulse can then decryped into a Morse Code message.

There are four Classes defined in this file:
	PXI_Scope() --        provides methods to use the PXI Scope to acquire data and save it to a wave file.  This also include loading and playing 
	                      wave files.
	Morse_Code() --       provides methods to encrypt and decrypt Morse Code data
	Pulses() --           provides methods to detect pulses in an audio stream and build a Morse Code message.
	Audio_Processing() -- provides common signal processing routines, high/low/bandpass filters, square law detector, and plotting functions 
	                      for time,frequency and cross-correlation plots

Two examples are provided: 
	See 'myMainDME' for the steps involved in processing DME Tones and converting the Morse Code pulses into alpha text,
	See 'myMainTCASGPWS' for the steps involved in processing audio messages from TCAS or GPWS.  Basically compare
	a stored wave file against the input wave file by windowing the time-series and then doing a cross-correlation.


Required:   NISCOPE - python version of National Instruments data acquistion routines,
            SIMPLEAUDIO - python library for playing WAV files. 
            
INPUTS:  None
OUTPUT:  Audio WAV file (mysound.wav), audio output on PC speakers.
         Morse Code Message data

HISTORY:
05/15/2020 MRS Initial Release
10/21/2020 MRS Added Methods for Spectral and Cross-Correlation plots, created TCAS/DME example processing.

"""
"""************************************************************************************************************************************"""


import niscope                     #routines for the NI PXI Scope
import numpy as np
from scipy.signal import butter, lfilter
import scipy.io.wavfile
import scipy.fftpack
import matplotlib.pyplot as plt
import simpleaudio as sa          #rouines for playing audio on PC
import datetime


"""************************************************************************************************************************************"""
class Pxi_Scope():
    """Methods to acquire data, read/write/play waveform files."""

    def pxiscope_acquire(self,channel,total_acquisition_time_in_seconds,sample_rate_in_hz,voltage,samples_per_fetch):
        """ This routine reads the PXI Scope (5110) waveform data.  Inputs are the channel(s) (as a list), acquisition time in seconds
        sample rate in Hz, voltage scale (volts), and the number of samples per fetch (don't make this too big or you get timeouts).
        Data is fetched in chunks, 'samples_per_fetch' until the total acquition time is reached. Returns the the WFM data    """
        
        total_samples = int(total_acquisition_time_in_seconds * sample_rate_in_hz)
        # Opening session
        with niscope.Session("PXI1Slot4") as session:
            #Acquire on channels of the device
            #channel_list = [c for c in range(session.channel_count)]  # Need an actual list and not a range
            channel_list = channel
    
            #Creating numpy arrays
            waveforms = [np.ndarray(total_samples, dtype=np.int16) for c in channel_list]
    
            #Configuring 
            session.configure_horizontal_timing(min_sample_rate=sample_rate_in_hz, min_num_pts=1, ref_position=0.0, num_records=1, enforce_realtime=True)
            session.channels[channel_list].configure_vertical(voltage, coupling=niscope.VerticalCoupling.DC, enabled=True)
            # Configure software trigger, but never send the trigger.
            # This starts an infinite acquisition, until you call session.abort() or session.close()
            session.configure_trigger_software()
            current_pos = 0
            #Initiating
            k=0
            with session.initiate():
                while current_pos < total_samples:
                    # We fetch each channel at a time so we don't have to de-interleave afterwards
                    # We do not keep the wfm_info returned from fetch_into
                    for channel, waveform in zip(channel_list, waveforms):
                        #Fetching - we return the slice of the waveform array that we want to "fetch into"
                        session.channels[channel].fetch_into(waveform[current_pos:current_pos + samples_per_fetch], relative_to=niscope.FetchRelativeTo.READ_POINTER,
                                                             offset=0, record_number=0, num_records=1, timeout=datetime.timedelta(seconds=5.0))
                    current_pos += samples_per_fetch
                    #print("%d: Channel: %d, Pos: %d" % (k,channel,current_pos))
                    k=k+1
                    
        return waveforms
    
    def save_wavefile(self,fname,sample_rate,data):
        """ Saves a WaveFile, give the filename, sample_rate(Hz) and input data (as numpy int16 array)."""
        scipy.io.wavfile.write(fname,sample_rate,data)
        print ("Wrote %d samples to sound.wav"%len(data))
    
    def read_wavefile(self,fname):
        """Reads in a Wavefile, of fname, returns sample_rate and
        data (as a numpy int16 array)."""
        sample_rate,data = scipy.io.wavfile.read(fname)
        print ("WaveFile Sample Rate: ",sample_rate)
        return sample_rate,data
    
    def play_waveform(self,fname):
        """Uses 'simple_audio' to play the waveform file over PC speakers. """
        wave_obj = sa.WaveObject.from_wave_file(fname)
        play_obj = wave_obj.play()
        play_obj.wait_done()
"""************************************************************************************************************************************"""        
class Morse_Code():
    """Methods to encrypt/decrypt morse code messages."""

    def __init__(self):
        # Dictionary representing the morse code chart 
        self.MORSE_CODE_DICT = { 'A':'.-', 'B':'-...', 
                            'C':'-.-.', 'D':'-..', 'E':'.', 
                            'F':'..-.', 'G':'--.', 'H':'....', 
                            'I':'..', 'J':'.---', 'K':'-.-', 
                            'L':'.-..', 'M':'--', 'N':'-.', 
                            'O':'---', 'P':'.--.', 'Q':'--.-', 
                            'R':'.-.', 'S':'...', 'T':'-', 
                            'U':'..-', 'V':'...-', 'W':'.--', 
                            'X':'-..-', 'Y':'-.--', 'Z':'--..', 
                            '1':'.----', '2':'..---', '3':'...--', 
                            '4':'....-', '5':'.....', '6':'-....', 
                            '7':'--...', '8':'---..', '9':'----.', 
                            '0':'-----', ', ':'--..--', '.':'.-.-.-', 
                            '?':'..--..', '/':'-..-.', '-':'-....-', 
                            '(':'-.--.', ')':'-.--.-'} 
        
        
    def encrypt(self,message): 
        """ Function to encrypt the string according to the morse code chart """
        cipher = '' 
        for letter in message: 
            if letter != ' ': 
      
                # Looks up the dictionary and adds the 
                # correspponding morse code 
                # along with a space to separate 
                # morse codes for different characters 
                cipher += self.MORSE_CODE_DICT[letter] + ' '
            else: 
                # 1 space indicates different characters 
                # and 2 indicates different words 
                cipher += ' '
      
        return cipher 
      
    def decrypt(self,message): 
        """ Function to decrypt the string, from morse to english""" 
      
        # extra space added at the end to access the last morse code 
        message += ' '
      
        decipher = '' 
        citext = '' 
        #i=0
        for letter in message: 
      
            # checks for space 
            if (letter != ' '):   
                # counter to keep track of space 
                i = 0 
                # storing morse code of a single character 
                citext += letter 
      
            # in case of space 
            else: 
                # if i = 1 that indicates a new character 
                i += 1
                # if i = 2 that indicates a new word 
                if i >= 2 : 
                     # adding space to separate words 
                    decipher += ' '
                else: 
                    # accessing the keys using their values (reverse of encryption) 
                    decipher += list(self.MORSE_CODE_DICT.keys())[list(self.MORSE_CODE_DICT 
                    .values()).index(citext)] 
                    citext = '' 
        return decipher     
"""************************************************************************************************************************************"""        
class Pulses():
    """Methods to detect pulses in data and build a morse code message."""
    
    def detect_pulses(self,data):
        """Detects pulse in 'data' stream. Returns Npulse, number of pulses; rEdges, array of rising Edge times; 
        fEdges, array of falling Edge times."""
        #detect the pulses
        mmax = np.max(data)
        mmin = np.min(data)
        thr = (mmax - mmin)/2.0
        print('Threshold: ',thr)
        #rising  and falling edges
        rEdges = np.ndarray(128,dtype=float)   #array of Edge Times
        fEdges = np.ndarray(128,dtype=float)
        rEdge = True
        fEdge = True
        NPulse=0
        nn = len(data) - 2

        #Find Pulses, detect Rising Edge first, then look for falling edge.
        for i in range(nn):
            x = data[i]

            #Check for Errors (No Pulses or just Noise)
            if i == nn:
                print('No Pulses Found')
                return 0,0,0
            if NPulse > 64:
                print('Too Many Pulses, Noise')
                return 0,0,0

            
            #Look for Rising Edge
            if ((x > thr) and (rEdge)):
                print ('RisingEdge: idx: %d, t: %d, x: %f'%(NPulse,i,x))
                rEdges[NPulse] = i
                rEdge = False

            #Look for Falling Edge
            if ((x < thr) and not(rEdge) and (fEdge)):
                #print ('FalingEdge: idx: %d, t: %d, x: %f'%(NPulse,i,x))
                fEdges[NPulse] = i
                NPulse=NPulse+1
                fEdge = False
                
            if (not(rEdge) and not(fEdge)):
                rEdge = True
                fEdge = True

        #Check for just Noise
        if NPulse > 40:
            print('Just Noise')
            return 0,0,0

        return NPulse,rEdges,fEdges
        
    def build_morse_message(self,NPulse,rEdges,fEdges):
        """Builds a Morse Code Message, based on number of pulses (NPulse), array of rising edge times (rEdges),
        and array of falling edge times (fEdges).  Returns a string 'msg' of dots and dashes '--- ... -.- etc'. """       
        #get dash/dots
        char = ''
        msg = ''
        for x in range(NPulse):
            dur = fEdges[x] - rEdges[x]
            #print("Dash/Dot Durations: k %d, dur: %d"%(x,dur))
            if (dur > 15000):
                char = char + '-'
            else :
                char = char + '.'
            #get spaces between chars
            sp = rEdges[x+1] - fEdges[x]
            #print("Space %d, sp: %d"%(x,sp))
            if sp > 15000:
                msg = msg + char + ' '
                char = ''
        
            if sp > 100000:
                msg = msg + '  '
        return msg
"""************************************************************************************************************************************"""        
class AudioProcess():
    """Methods to signal process the audio waveform. """


    def butter_bandpass(self,lowcut, highcut, fs, order=5):
        """ Butterworth Filter Coefficients, BandPass.  LowCut/HighCut are the 
        high/low cuttof frequencies(Hz), Fs is the sample rate, order is the Order
        of the filter."""
        nyq = 0.5 * fs
        low = lowcut / nyq
        high = highcut / nyq
        b, a = butter(order, [low, high], btype='band')
        return b, a
    
    def butter_lowpass(self,cutoff,fs, order=5):
        """ Butterworth Filter Coefficients, LowPass.  Cutoff is the 
        rolloff frequencie(Hz), Fs is the sample rate, order is the Order
        of the filter."""
        nyq = 0.5 *fs
        cut = cutoff/nyq
        b,a = butter(order,cut,btype = 'low')
        return b,a
    
    def butter_bandpass_filter(self,data, lowcut, highcut, fs, order=5):
        """Implement BandPass, data is the input data,LowCut/HighCut are the 
        high/low cuttof frequencies(Hz), Fs is the sample rate, order is the Order
        of the filter. Returns Y, the filtered data.""" 
        b, a = self.butter_bandpass(lowcut, highcut, fs, order=order)
        y = lfilter(b, a, data)
        return y
    
    def butter_lowpass_filter(self,data,cutoff,fs,order=5):
        """Implement LowPass, data is the input data,Cutoff is the 
        rolloff frequencie(Hz), Fs is the sample rate, order is the Order
        of the filter.  Returns Y, the filtered data."""
        b,a = self.butter_lowpass(cutoff,fs,order=order)
        y = lfilter(b,a,data)
        return y
    
    def square_law(self,data,cuttoff,sample_rate):
        """Implements a square law detector.  Returns a filtered waveform which
        should clearly show pulses. Input data (as a numpy array), 'cuttoff' in Hz,
        sample_rate (Hz) and         returns the squarelaw results (as a numpy array). """
        y2 = data * data              #square the data
        ysq= np.sqrt(y2)             #take the square root
        #lowpass filter
        y = self.butter_lowpass_filter(ysq,cuttoff,sample_rate,order=6)
        return y

    
    def plot_time_series(self,data,sample_rate,title):
        """Plots the time series (using matplotlib) given the data, samplerate and title for the plot. """
        total_ts_sec = len(data)/sample_rate
        print("The total time series length = {} sec (N points = {}) ".format(total_ts_sec, len(data)))
        plt.figure(figsize=(20,3))
        plt.plot(data)
        plt.xticks(np.arange(0,len(data),sample_rate),
                   np.arange(0,len(data)/sample_rate,1))
        plt.ylabel("Amplitude")
        plt.xlabel("Time (second)")
        plt.title(title)
        plt.show()
        
    def plot_freq_spectrum(self,data,sample_rate,title):
        """Averages FFT over 1 second intervals, plots the result. """
        total_samples = len(data)
        d_array = np.arange(sample_rate, dtype=float)
        ck = int(total_samples/sample_rate)
        print("TotalSamples, SampleRate, Chunks: ",total_samples,sample_rate,ck)
        
        #get data chunks and perform FFTs
        chunk = 0
        offset = 0
        y = np.zeros(sample_rate,dtype=float)
        yf = np.zeros(sample_rate,dtype=float)
        while chunk < ck:
            #read in 44100 samples (sample_rate)
            for x in range(sample_rate):              
                d_array[x] = data[x + offset]

            #perform FFT on d_array
            y = scipy.fftpack.fft(d_array)
            yf = yf + y
            chunk = chunk + 1
            offset = chunk*sample_rate
            
            
        plt.figure(figsize=(20,3))
        #plt.plot(yf)
        N = sample_rate
        T = 1.0/sample_rate
        xf = np.linspace(0.0, 1.0/(2.0*T), int(N/2))
        plt.plot(xf,2/N*np.abs(yf[:N//2]))
        plt.ylabel("Amplitude")
        plt.xlabel("Frequency")
        plt.title(title)
        plt.show()
        
        
    def plot_cross_correlation(self,data1,data2,title):
        """ Plots the cross-correlation of two waveforms, kk is the number of samples."""
        kk1 = len(data1)

        plt.figure(figsize=(20,3))
        lags,c,line,b = plt.xcorr(data1,data2,maxlags = int(kk1/100))
        plt.ylabel("Correlation")
        plt.xlabel("Lag")
        plt.title(title)
        plt.show()
        return np.max(c)
        
    
        
    def window_data(self,data1,data2,rate1,rate2,nkk, thresh):
        """creates two time series of equal duration (nkk), where the start of each series
        data depends on when the series crosses a threshold (thresh).  Data1, Data2 are
        the original time series, rate1, rate2 the corresponding sample rates.  The routine
        returns two new series (data1w,data2w) which are normalized and set to the same length. """
        
        kk = nkk         #data length (usually a multipe of the sample rate)
        data1w = np.zeros(kk,dtype=float)
        data2w = np.zeros(kk,dtype=float)

            
        l1 = len(data1)
        l2 = len(data2)
        #find start of the audio
        for x in range(l1):
            if np.abs(data1[x]) > thresh:
                break
        k1 = x
        for x  in range(l2):
            if np.abs(data2[x]) > thresh:
                break
        k2 = x
        print("starting indexes, k1,k2: ",k1,k2)
        #time shift waveform and scale by max
        mx1 = np.max(np.abs(data1))
        mx2 = np.max(np.abs(data2))
        k=0
        for x in range(kk):
            data1w[k] = data1[(k1 + k)]/mx1
            data2w[k] = data2[(k2 + k)]/mx2
            k=k+1
            
        return data1w, data2w
            
            
        
"""************************************************************************************************************************************"""        
"""************************************************************************************************************************************"""        
"""************************************************************************************************************************************"""        
def myMainDME(rm):
    """ Typical DME tone Audio Processing. """

    """ EXAMPLE of a simple tone
    #Create the Wav File
    rate = 44100         #sample rate
    period = 1.0/rate
    duration = 5.0       #duration 5.0 seconds
    freq = 1330.0        #1.33 KHz Tone
    amp = 2**15-1
    time = np.arange(duration*rate)*period - duration/2.0
    wave = amp * np.sin(2*np.pi*time*freq) #/ (2*np.pi*time*freq)
    scipy.io.wavfile.write("mytone.wav",rate,wave.astype(np.int16))
    print ("Wrote %d samples to mytone.wav"%len(wave))
    """
    
    #initialize classes, so we can use the methods
    scope = Pxi_Scope()
    audio = AudioProcess()
    pulse = Pulses()
    morse = Morse_Code()
    
    #Step 1: Acquire the PXI SCOPE data
    channel = [1]                         #audio input to PXI scope
    acquisition_time = 60                 #seconds of wave data, 60 seconds
    sample_rate = 44100                   #typical audio sample rate
    samples_per_fetch = 22050             #this is the bigest chunk size that works
    voltage = 0.50                        #0.5 volts (or 500mV)
    
    Npulses = 100.0
    count = 0
    while (Npulses == 100.0 or Npulses > 18 ) and count < 4:
        #Get the data from the PXI Scope
        rm.logMessage(1, "Aquiring data from pxiscope...............")
        wfm = scope.pxiscope_acquire(channel,acquisition_time, sample_rate, voltage, samples_per_fetch)
        rm.logMessage(1, "Data Len (aquired form pxiscope): " + str(len(wfm[0])))
        #print("Data Len (aquired form pxiscope): ",len(wfm[0]))   
        #Plot the PXI Acquired Results
        #title = "The WAVE File time series = {} sec, sample_rate = {}".format(len(wfm[0])/sample_rate, sample_rate)
        #audio.plot_time_series(wfm[0],sample_rate,title)    #comment this out if running from TestStand
    
        #Step 2: Create the Wave File, save it to file, 'mysound.wav'
        rm.logMessage(1, "Saving wavefile to sound.wav .................")
        scope.save_wavefile("mysound.wav",sample_rate,wfm[0])
        #print ("Wrote %d samples to sound.wav"%len(wfm[0]))
        rm.logMessage(1, "Wrote " +str(len(wfm[0]))+ " samples to sound.wav")
    
        #Step3: Play the Saved Wav file
        #rm.logMessage(1, "Playing mysound.wav ..................")                                     
        #scope.play_waveform("mysound.wav")    
        #rm.logMessage(1, "mysound.wav is finished playing")                                     
                              
        #Step4: Read In the Wav File     
        rm.logMessage(1, "Reading sample rate and data from mysound.wav ........................")
        sample_rate, data = scope.read_wavefile("mysound.wav")
        #print ("WaveFile Sample Rate: ",sample_rate)
        rm.logMessage(1, "WaveFile Sample Rate: " + str(sample_rate))
        #Plot Time Series
        #title = "The Input Raw time series = {} sec, sample_rate = {}".format(len(data)/sample_rate, sample_rate)
        #audio.plot_time_series(data,sample_rate,title)   #comment this out if running from TestStand
    
        #Step 5: Bandpass filter the signal
        lowcut = 400              #low cuttoff Hz
        highcut = 1400            #high cuttoff Hz
        rm.logMessage(1, "Filtering the signal ...............................")
        y = audio.butter_bandpass_filter(data, lowcut, highcut, sample_rate, order=6)
        rm.logMessage(1, "Audio Bandpass Filter Output: " + str(y))

        #Plot Filtered Time Series
        #title = "The Filtered time series = {} sec, sample_rate = {}".format(len(data)/sample_rate, sample_rate)
        #audio.plot_time_series(y,sample_rate,title)     #comment this out if running from TestStand
        
        #Step 6: Square Law Detector, square the signal, then lowpass filter
        cuttoff = 400.0          #cuttoff for low pas filter
        rm.logMessage(1, "Performing Square Law Detector .........")
        outp = audio.square_law(y, cuttoff, sample_rate)
        rm.logMessage(1, "Square Law Detector Output: " + str(len(outp)))

        #Plot Square Law Time Series
        #title = "The Squared Law time series = {} sec, sample_rate = {}".format(len(data)/sample_rate, sample_rate)
        #audio.plot_time_series(outp,sample_rate,title)   #comment this out if running from TestStand
    
        #Step 7: Detect Pulses and Decode Morse Code
        NPulses,rEdges,fEdges = pulse.detect_pulses(outp)
        count = count + 1
    
    if NPulses > 4:
            rm.logMessage(1, "Number of Pulses: " + str(NPulses))
            rm.logMessage(1, "Number of Rising Edges : " + str(len(rEdges)))
            rm.logMessage(1, "Number of Falling Edges: " + str(len(fEdges)))
            msg = pulse.build_morse_message(NPulses, rEdges, fEdges)       # Morse code message
            D_msg = morse.decrypt(msg)                                   # Decrypted message
            #print("\r\nMORSE CODE MESSAGE: ",msg)
            #print ('DECRYPTED MESSAGE: ',morse.decrypt(msg))
            rm.logMessage(1, "\rMORSE CODE MESSAGE: " + str(msg))
            rm.logMessage(1, "DECRYPTED MESSAGE: " + str(D_msg) + "\n")
            return D_msg
    else:
            rm.logMessage(1, "\r\n***NO MESSAGE***\n")
            #print("\r\n ***No Message***")
            return("----")
        

"""************************************************************************************************************************************"""        
"""************************************************************************************************************************************"""        
"""************************************************************************************************************************************"""        
    
def myMainTCASGPWS():
    """ Typical TCAS/GPWS audio processing. """
	
    #initialize classes, so we can use the methods
    scope = Pxi_Scope()
    audio = AudioProcess()



    #Step1: Play the Saved Wav file 
    print("*AUDIO Playback*")
    scope.play_waveform("2020_10_20_10_02_29.wav")

    ###GET DATA FROM the SCOPE or used a stored waveform
    ###use step 1 above to get data from the scope, this example just compares stored waveforms.
    #Step2: Read In the Wav File     
    sample_rate1,data1 = scope.read_wavefile("2020_10_20_10_02_29.wav")
    #sample_rate2,data2 = scope.read_wavefile("2020_10_20_10_02_48.wav")   
	
	#for this example, I use the original wave file but distort it.
    sample_rate2 = sample_rate1
    #change time skew and scale
    data2 = data1[22050:]*0.85
    #add some random noise
    noise = np.random.normal(0,150,(len(data2)))
    data2 = data2 + noise
    
    
    #Plot Time Original Series
    title = "The Input Raw time series1 = {} sec, sample_rate = {}".format(len(data1)/sample_rate1, sample_rate1)
    audio.plot_time_series(data1,sample_rate1,title)
    title = "The Input Raw time series2 = {} sec, sample_rate = {}".format(len(data2)/sample_rate2, sample_rate2)
    audio.plot_time_series(data2,sample_rate2,title)
    
    #Step2: Window the data, result is two arrays each 3 seconds long   
    data1w, data2w = audio.window_data(data1,data2,sample_rate1,sample_rate2,3*sample_rate1,1000)
  
    #Plot Time Windowed Series
    title = "The Input Windowed time series1 = {} sec, sample_rate = {}".format(len(data1w)/sample_rate1, sample_rate1)
    audio.plot_time_series(data1w,sample_rate1,title)
    title = "The Input Windowed time series2 = {} sec, sample_rate = {}".format(len(data2w)/sample_rate2, sample_rate2)
    audio.plot_time_series(data2w,sample_rate2,title)
   
    
    #Plot SpectroGraph
    Fs = int(sample_rate1)
    plt.specgram(data1w,Fs = Fs)
    plt.show()
    Fs = int(sample_rate2)
    plt.specgram(data2w,Fs = Fs)
    plt.show()
    
    #Step3: Plot the frequency spectrum of the windowed data       
    audio.plot_freq_spectrum(data1w,sample_rate1,"Spectrunm of Data1")
    audio.plot_freq_spectrum(data2w,sample_rate2,"Spectrum of Data2")
    
    #Step4: Get the cross-correlation
    cxx = audio.plot_cross_correlation(data1w,data2w,"Cross-Correlation")
    print("Max Correlation: ",cxx)
    
"""************************************************************************************************************************************"""        
"""************************************************************************************************************************************"""        
"""************************************************************************************************************************************"""        

if __name__ == "__main__":
    #Execute one script or the other (myMainDME or myMainTCASGPWS)
    #myMainDME()
    myMainTCASGPWS()    