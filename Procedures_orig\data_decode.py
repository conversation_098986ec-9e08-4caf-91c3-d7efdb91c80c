# -*- coding: utf-8 -*-
"""
Created on Thu Apr 30 06:59:45 2020

@author: E282068
"""

"""###########################################################################
#  TTG raw message format (INPUT)
#  ------------------------------
#
# 01 00000000000000 5D000004 64DF24 00000004 000000A295D6
#
# 03 00000000000000 80000004 B0ADE5 03FFFFFF 000000001441
#  |  |             |           |       |         |
#  |  |             |           |       |         + Time in 1/4 msec
#  |  |             |           |       |        
#  |  |             |           |       + Decoded Address         
#  |  |             |           |
#  |  |             |           + AP | PI (Address & Parity)
#  |  |             |
#  |  |             + Start of short messages
#  |  |      
#  |  +  If block != All 0s => Long message (data starts @ 3rd )
#  |     If block == All 0s => Short Message (data starts @ 17th char)
#  | 
#  + Type: 1 => DF from UUT (Mode S Reply)
#          2 => DF from UUT (ATCRBS Reply) 
#          3 => UF from UUT (Mode S Inter)
#          4 => UF from UUT (ATCRBS Inter) 
#          5 => DF from Intruder (Mode S Reply)
#          6 => DF from Intruder (ATCRBS Reply) 
#          7 => UF from Intruder (Mode S Inter)
#          8 => UF from Intruder (ATCRBS Inter)
#
#  The message is a HEX string representation of the message with the first byte:
#  starting at the 3rd character (short message - 56 bits) or at the 17th character 
#  (long message - 112 bits).
#  The first byte is encoded 1000 0xxx:  5 MSB => UF|DF number (example 16)
"""
"""
# Unpack the pieces based on the sample format
#  ty     long         short  parity  ? address   time 
#  03 00000000000000 80000004 B0ADE5 03 FFFFFF 000000001441
#  2        14           8      6     2   6         12
"""
def decodeDFShort(DF, data, add_par):
    """ Returns data for short messages given DF code"""
    if (DF == "00000"):
        # Short Air-Air Surveillance (ACAS)
        return ": Short Air to Air VS : " + str(int(data[5:6], 2)) + " CC " + str(int(data[6:7], 2)) + " : SL " + str(int(data[8:11],2)) + " : RI " + str(int(data[13:17], 2)) + " : AC " + str(int(data[19:32], 2)) + " : AP " + add_par
    elif (DF == "00001"):
        # TODO
        return ": DF1 "
    elif (DF == "00010"):
        # TODO
        return ": DF2 "
    elif (DF == "00011"):
        # TODO
        return ": DF3 "
    elif (DF == "00100"):
        # Surveillance, Altitude Reply
        return ": Surveillance, Altitude Reply : FS " + str(int(data[5:8]), 2) + " : DR " + str(int(data[8:13]), 2) + " : UM " + str(int(data[13:19]), 2) + " : AC " + str(int(data[19:32]), 2) + " : AP " + add_par
    elif (DF == "00101"):
        # Surveillance, Identity Reply
        return ": Surveillance, Identity Reply : FS " + str(int(data[5:8], 2)) + " : DR " + str(int(data[8:13], 2)) + " : UM " + str(int(data[13:19], 2)) + " : ID " + str(int(data[19:32], 2)) + " : AP " + add_par
    elif (DF == "00110"):
        # TODO
        return ": DF6"
    elif (DF == "00111"):
        # TODO
        return ": DF7"
    elif (DF == "01000"):
        # TODO
        return ": DF8"
    elif (DF == "01001"):
        # TODO
        return ": DF9"
    elif (DF == "01010"):
        # TODO
        return ": DF10"
    elif (DF == "01011"):
        # All-Call Reply 
        return ": DF11: Mode S All Call : CA " + str(int(data[5:8], 2)) + " : AA " + str(int(data[8:32], 2)) + " : PI " + add_par
    elif (DF == "01100"):
        # TODO
        return ": DF12"
    elif (DF == "01101"):
        # TODO
        return ": DF13"
    elif (DF == "01110"):
        # TODO
        return ": DF14"
    elif (DF == "01111"):
        # TODO
        return ": DF15"
    elif (DF == "10110"):
        # Reserved for Miliary Use
        return ": Long Air to Air ACAS"
    elif (DF == "10111"):
        # Comm-D (ELM)
        return ": 1090 Extended Squitter"

def decodeDFLong(DF, data, add_par):
    """ Returns data for long messages given DF code """
    if (DF == "00001"):
        # TODO
        return ": DF1"
    elif (DF == "00010"):
        # TODO
        return ": DF2"
    elif (DF == "00011"):
        # TODO
        return ": DF3"
    elif (DF == "00110"):
        # TODO
        return ": DF6"
    elif (DF == "00111"):
        # TODO
        return ": DF7"
    elif (DF == "01000"):
        # TODO
        return ": DF8"
    elif (DF == "01001"):
        # TODO
        return ": DF9"
    elif (DF == "01010"):
        # TODO
        return ": DF10"
    elif (DF == "01100"):
        # TODO
        return ": DF12"
    elif (DF == "01101"):
        # TODO
        return ": DF13"
    elif (DF == "01110"):
        # TODO
        return ": DF14"
    elif (DF == "01111"):
        # TODO
        return ": DF15"
    elif (DF == "10000"):
        # Long Air-Air Surveillance (TCAS)
        return ": Long Air-Air Surveillance (TCAS) : VS " + str(int(data[5:6])) + " : SL " + str(int(data[8:11])) + " : RI " + str(int(data[13:17])) + " : AC " + str(int(data[19:32])) + " : MV " + str(int(data[32:88])) + " : AP " + add_par
    elif (DF == "10001"):
        # Extended Squitter
        return ": Extended Squitter : CA " + str(int(data[5:8])) + " : AA " + str(int(data[8:32])) + " : ME " + str(int(data[32:88])) + " : PI " + add_par
    elif (DF == "10010"):
        # Extended Squitter/Non-Transponder
        return ": CF " + str(int(data[5:8])) + ": AA " + str(int(data[8:32])) + " : ME " + str(int(data[32:88])) + " : PI " + add_par
    elif (DF == "10011"):
        # Miliarty Application
        return ": Military Extended Squitter AF : " + str(int(data[5:8])) + " : Military Application: " + str(int(data[8:112])) + " ME: " + str(int(data[32:88])) + " PI: " + add_par
    elif (DF == "10100"):
        # Comm-B, Altitude Reply
        return ": Comm-B, Altitude Reply : FS " + str(int(data[5:8])) + " : DR " + str(int(data[8:13])) + " : UM " + str(int(data[13:19])) + " : AC " + str(int(data[19:32])) + " : MB " + str(int(data[32:88])) + " : AP " + add_par
    elif (DF == "10101"):
        # Comm-B, Identity Reply
        return ": Comm-B, Identity Reply : FS " + str(int(data[5:8])) + " : DR " + str(int(data[8:13])) + " UM: " + str(int(data[13:19])) + " ID: " + str(int(data[19:32])) + " MB: " + str(int(data[32:88])) + " AP: " + add_par
    elif (DF == "10110"):
        # Reserved for Miliary Use
        return ": Reserved for Military Use"
    elif (DF == "10111"):
        return ": DF23 "
    return ": ERROR, Couldn't Interpret Data"

def decodeATCRBS(dataBin):
        generator = int(dataBin[0:4], 2)          # 0 = start of Byte 2
        generator = chr(generator + 65)           # Convert to ASCII
        SPI = dataBin[10:11]                      # 10 = Byte 3, bit 5
        X = dataBin[11:12]                        # 11 = Byte 3, bit 4
        # C1A1 = dataBin[12:16]
        return ": SPI " + SPI + " : Generator "+ generator + " : X " + X

def getLocStatusUUT(data):
    line = ""
    if (data[0:1] == 1):                       # Bit 7
        line += " : Top Receiver"
    else :
        line += " : Bottom Receiver"
    return line

def getLocStatusATC(data):
    generator_int = int(data[1:4], 2)            # get generator code number
    generator_char = chr(generator_int + 65)     # convert code to ascii letter
    return " : Generator " + generator_char


def hex2bin(data, charCount):
    """ Convert Ascii Hex to Binary.  Input Ascii Hex string 'data'
    and charCount as integer for the number of characters in the 
    string. """

    padded = "{d:0<{c}}".format(d = data, c = charCount)
    binary = "".join(("{0:0>4b}".format(int(x, 16)) for x in padded))
    return binary

def data_log_decode(line):
    """ Partially Decodes The Data Line. 
    Temp routine for now"""

    #fields for ModeS
    ty = line[0:2]                #type
    longPart = line[2:16]             #long ModeS
    shortPart = line[16:24]           #short ModeS
    parity = line[24:30]          #Mode S Parity
    loc = line[30:32]             #Gen/Rcv Locations
    address = line[32:38]         #ModeS Adrs
    time = line[38:50]            #time
    #fields for ATCRBS Replies
    Generator = line[2:4]
    SPIC1A1C2A2 = line[4:6]
    C4A4B1D1B2D2B4D4 = line[6:8]


    ###################################################
    # Step through each reply and generate data message

    ##-------------------ModeS Reply-------------------## 
    if (ty == '01'):
        line = line + " : " + 'ModeS Reply'
        
        if longPart == '00000000000000':        #short reply
            shortBin = hex2bin(shortPart, 8)
            parityBin = hex2bin(parity, 6)
            DF = shortBin[0:5]
            line = line + " : Msg " + decodeDFShort(DF, shortBin, parityBin)
        else:                    #long reply
            shortBin = hex2bin(shortPart, 8)
            longBin = hex2bin(longPart, 14)
            dataBin = longBin + shortBin
            parityBin = hex2bin(parity, 6)
            line = line + " : Msg " + decodeDFLong(DF, dataBin, parityBin)
        
        line = line + getLocStatusUUT(hex2bin(loc, 2))
        line = line + " : Adrs " + address
    ##-------------------------------------------------##

    ##------------------ATCRBS Reply-------------------##        
    if (ty == '02'):
        line = line + " : " + 'ATCRBS Reply'

        dataBin = hex2bin((longPart + shortPart), 22)
        line = line + " : Msg " + decodeATCRBS(dataBin)
        line = line + getLocStatusUUT(hex2bin(loc, 2))
    ##-------------------------------------------------##
        
    if (ty == '03'):
        line = line + " : " + 'ModeS Inter'
    
    if (ty == '04'):
        line = line + " : " + 'ATCRBS Inter'
    
    ##--------------ATC/RGS ModeS Reply---------------##
    if (ty == '05'):
        line = line + " : " + 'ATC/RGS ModeS Reply'

        if longPart == '00000000000000':        #short reply
            shortBin = hex2bin(shortPart, 8)
            parityBin = hex2bin(parity, 6)
            DF = shortBin[0:5]
            line = line + " : Msg " + decodeDFShort(DF, shortBin, parityBin)
        else:                    #long reply
            shortBin = hex2bin(shortPart, 8)
            longBin = hex2bin(longPart, 14)
            dataBin = longBin + shortBin
            parityBin = hex2bin(parity, 6)
            line = line + " : Msg " + decodeDFLong(DF, dataBin, parityBin)
        line = line + getLocStatusATC(hex2bin(loc, 2))
    ##-------------------------------------------------##
    
    ##--------------ATC/RGS ATCRBS REPLY---------------##
    if (ty == '06'):
        line = line + " : " + 'ATC/RGS ATCRBS Reply'

        dataBin = hex2bin((longPart + shortPart), 22)
        line = line + " : Msg " + decodeATCRBS(dataBin)
        line = line + getLocStatusATC(hex2bin(loc, 2))
    ##-------------------------------------------------##

    if (ty == '07'):
        line = line + " : " + 'ATC Mode S Inter'
    
    if (ty == '08'):
        line = line + " : " + 'ATC ATCRBS Inter'
    
    ######################################################

    #last 5 bytes, timestamp
    
    i = int(time,16)      #base 16 not 16 chars
    tt = i*(25.0 / 1000000000)
    line = line + ' : Time ' + str(tt)
    line = line + " : "
    return line

def main():
    f_in_name = "FAR43_J.log"
    f_out_name = f_in_name[:-4] + "out.log"
    fin = open(f_in_name, 'r')
    fout = open(f_out_name, 'w')

    for line in fin:
        line_decode = data_log_decode(line)

        fout.write(line_decode)

    fin.close()
    fout.close()


if __name__ == "__main__":
    main()

    
