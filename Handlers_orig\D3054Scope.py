# -*- coding: utf-8 -*-
"""
Created on 3/12/2020

@author: H118396
         <PERSON>
         CNS QUALIFICATION TEST GROUP
         
DESCRIPTION:
Keysight DSO3054 OScope function and attribute class.

INPUTS: VISA Resource Manager obj (rm)
OUTPUTS: N/A

HISTORY:
4/28/2020   MR  Fixed waveSource() bug and spelling mistake. posEdgePosition() and
                negEdgePosition now allow parameter to suppress data filter
09/21/2020  MRS Merged Scope.py to create this new module for new Resource Manager (ate_rm.py) 
03/01/2021  MRS Added methods for InvertChan and SetImpedance.

"""

import time

import matplotlib.pyplot as plt
from scipy.signal import savgol_filter

class D3054Scope():
    def __init__(self, rm):

        # Add conneciton to Resource Manager, ignore linting errors
        self.resourceManager = rm
        
        # Attempt connection to resource
        self.oscopeIP = "TCPIP0::************::INSTR"
        try:
            self.oscope = self.resourceManager.rm.open_resource(self.oscopeIP)
            self.oscope.write("")
            self.resourceManager.logMessage(0, "OScope Connection Success")
        except:
            self.resourceManager.logMessage("0",3,"Oscope, Connection","Failed to connect to resource")
            raise

##################################################
# Basic Commands
##################################################

    def basicWrite(self, cmd):
        """ Performs basic visa write command, logging command. """      
        tvlTxt = "> %s" % cmd
        self.basicTvlMsg(tvlTxt)
        resp = self.oscope.write("%s" % cmd)
        return resp

    def basicQuery(self, cmd, logEnable=False):
        """ Perfomrs basic visa query command, logging command. """
        tvlTxt = "> %s" % cmd
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        resp = self.oscope.query("%s" % cmd).strip()
        tvlTxt = "< %s" % resp
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        return resp

    def basicTvlMsg(self, tvlTxt):
        """ Sends Message to Client/Observer log. """
        self.resourceManager.logMessage(1, tvlTxt)
    
    def Ident(self):
        """ Returns OScope IDENT. """
        return self.basicQuery('*IDN?')

    def Reset(self):
        """ Resets the OScope. """
        self.basicWrite('*RST')
        self.resourceManager.logMessage(1, "Oscope Resetting...")
        time.sleep(5)
        self.resourceManager.logMessage(1, "Oscope complete\n")
        
    def close(self):
        """ Closes the OScope """
        self.oscope.close()

##################################################
#Channel Commands
##################################################
    
    def autoScale(self):
        """ Equivalent to hitting the autoscale button on the scope """
        self.basicWrite("AUToscale")

    def timeScale(self, time, unit):
        """ Sets the x-scale time base given <time><unit>/div """
        self.basicWrite("TIMebase:SCALe {}".format(self.timeAdjust(time, unit)))
          

    def invertChan(self,chan,inv):
        """ Invertes the selected channel. 
        inv = 0 for Off, =1 for On"""
        self.basicWrite("CHANnel{0}:INVert {1}".format(str(chan),str(inv)))

    def setChanImpdance(self,chan,imp):
        """ Sets the selected channel Impedance (1 MOhm, or 50 Ohms). 
        imp = 1 for 1MOhm, 5 for 50Ohms. """
        if imp == 1:
            self.basicWrite("CHANnel{0}:IMPedance ONEMeg".format(str(chan)))
        elif imp == 5:
            self.basicWrite("CHANnel{0}:IMPedance FIFTy".format(str(chan)))
   
    def voltDiv(self, chan, volts, unit):
        """ Sets the y-scale voltage base given <chan#>, <volts><unit>/div. Ex inputs: 1, 10, "mV" """
        volt_units = ["V", "mV", "uV"]
        try:
            float(volts)
            if (unit in volt_units) and (int(chan) in range(1,5)):
                self.basicWrite("CHANnel{0}:SCALe {1}".format(str(chan), str(volts) + unit))
            else:
                self.resourceManager.logMessage("0",3,"Oscope, voltDiv","Invalid Volt unit or Chan")
        except ValueError as e:
            self.resourceManager.logMessage("0",3,"Oscope, voltDiv","Invalid Volts {}".format(e.args))
 
    def getVoltDiv(self, chan):
        """ Returns the y-scale voltage base given <chan#> as a string. Ex inputs: 2 """
        if int(chan) in range(1,5):
            return self.basicQuery("CHANnel{}:SCAL?".format(str(chan)))
        else:
            self.resourceManager.logMessage("0",3,"Oscope, getVoltDiv","Invalid Chan")
 
    def setTimePosition(self, timePos):
        """ Sets the x-scale time position relative to the trigger point given <seconds>. """
        try:
            float(timePos)
            self.basicWrite("TIMebase:POSition {}".format(str(timePos)))
        except ValueError as e:
            self.resourceManager.logMessage("0",3,"Oscope, setTimePosition","Invalid time in seconds {}".format(e.args))

    def chanDisplay(self, chan, state):
        """ Turns on or off the displayed chan given <int chan# 1-4> and <state: 1[on], 0[off]> """
        if ((int(chan) in range(1,5)) and (int(state)==1 or int(state)==0)):
            self.basicWrite("CHANnel{0}:DISPlay {1}".format(str(chan), str(state)))
        else:
            self.resourceManager.logMessage("0",3,"Oscope, chanDisplay","Invalid Channel or state {0} {1}".format(str(chan), str(state)))

##################################################
# Search Commands
##################################################

    def getPwSearch(self, setting_type, setting):
        """ Returns the result Pulse width search given the <searchType> and <setting>. Search types include:
        "GREaterthan", "LESSthan", "POLarity", "QUALifier", "RANGe", "SOURce". Example input for search: 'GREaterthan', 2 """
        typeList = ["GREaterthan", "LESSthan", "POLarity", "QUALifier", "RANGe", "SOURce"]
        if setting_type in typeList:
            self.basicWrite("SEARch:GLITch:{0} {1}".format(setting_type, str(setting)))
            count = self.basicQuery("SEARch:COUNt?")
            if count == 0:
                print("No Search Found\n")
            return count
        else:
            self.resourceManager.logMessage("0",3,"Oscope, getPwSearch","Invalid search setting, avaliable settings: GREaterthan, LESSthan, POLarity, QUALifier, RANGe, SOURce")
            print("ERROR: Oscope, getPwSearch, Invalid search setting, avaliable settings: GREaterthan, LESSthan, POLarity, QUALifier, RANGe, SOURce")

##################################################
# Trigger Commands
##################################################
    def trigSource(self, chan):
        """ Sets the triggerchannel to the user input. Channels include: 1, 2, 3, 4. """
        try:
            if int(chan) in range(1,5):
                self.basicWrite("TRIGger:SOURce CHANnel{0}".format(str(chan)))
            else:
                self.resourceManager.logMessage("0",3,"Oscope, trigSource","Source channel not avaliable (1-4)")
                print("ERROR: Oscope, trigSource, Source channel not avaliable (1-4)")
        except ValueError as e:
            self.resourceManager.logMessage("0",3,"Oscope, trigSource","Invalid channel select {}".format(e.args))

    def trigType(self, mode):
        """Sets the trigger type on the current channel. Modes include: "EDGE", "GLITch", "PATTern", 
        "TV", "DELay", "EBURst", "OR", "RUNT", "SHOLd", "TRANsition". """
        modeList = ["EDGE", "GLITch", "PATTern", "TV", "DELay", "EBURst", "OR", "RUNT", "SHOLd", "TRANsition"]
        if mode in modeList:
            self.basicWrite("TRIGger:MODE {}".format(str(mode)))
        else:
            self.resourceManager.logMessage("0",3,"Oscope, trigType","Mode not set, avaliable Modes are: EDGE, GLITch, PATTern, TV, DELay, EBURst, OR, RUNT, SHOLd, TRANsition")
 
    def setEdgeTrigger(self, chan, value, unit):
        """Sets the edge trigger level given <chan#> and <value><unit>"""
        volt_units = ["V", "mV", "uV"]
        try:
            float(value)
            if (unit in volt_units) and (int(chan) in range(1,5)):
                self.basicWrite("TRIGger:EDGE:LEVel {0}, CHAN{1}".format(str(value) + unit, str(chan)))
            else:
                self.resourceManager.logMessage("0",3,"Oscope, setEdgeTrigger","Invalid Volt unit or Chan")
        except ValueError as e:
            self.resourceManager.logMessage("0",3,"Oscope, setEdgeTrigger","Invalid Volts {}".format(e.args))
 
    def trigSingle(self, timeout):
        """Attempts to capture a single trigger event given a timeout in <seconds>"""
        #self.basicWrite("STOP")
        #time.sleep(1.0)
        #self.basicWrite("RUN")
        # Clear trigger registry and capture single
        st1 = self.basicQuery("TER?")
        self.resourceManager.logMessage(0,st1)
        self.basicWrite("SINGle")
        time.sleep(.01)
        tryAttempt = 0
        while(tryAttempt < timeout):
            if "+1" in self.basicQuery("TER?"):
                return True
            else:
                tryAttempt += .01
                time.sleep(.01)

        # Trigger event does not occur
        self.resourceManager.logMessage(3,"Oscope, trigSingle - Trigger Event did not occur")
        self.trigRun()
        return False
    
    def trigRun(self):
        """Equivalent to hitting the 'RUN' key on the scope"""
        self.basicWrite("RUN")

##################################################
# Measure Commands
##################################################

    def setMeasureSource(self, chan):
        """Sets the measurement source <chan#>"""
        self.basicWrite("MEASure:SOURce CHANnel{}".format(str(chan)))
    
    def measSetThreshold(self, high, mid, low):
        """Sets the measurement threshold % values given <high%>, <mid%>, <low%>. Ex. default scope values are: 90, 50, 10"""
        self.basicWrite("MEASure:DEFine THResholds,PERCent,{0},{1},{2}".format(str(high), str(mid), str(low)))

    def getMeasure(self, measureTypeList):
        """Returns a list of measurements given input list of requested measurements. Supported measurements include: 
        "AREa", "BRATe", "BWIDth", "COUNTer", "DELay", "DUTYcycle", "FALLtime", "FREQuency",
        "NDUTy", "NEDGes", "NPULses", "NWIDth", "OVERshoot", "PEDGes", "PERiod", "PHASe", "PPULses", "PREShoot",
        "PWIDth", "RISetime", "SDEViation", "SOURce", "STATistics", "VAMPlitude", "VBASe", "VMAX", "VMIN", "VPP",
        "VTOP", "XMAX", "XMIN". """

        allowableInputs = ["AREa", "BRATe", "BWIDth", "COUNTer", "DELay", "DUTYcycle", "FALLtime", "FREQuency",
        "NDUTy", "NEDGes", "NPULses", "NWIDth", "OVERshoot", "PEDGes", "PERiod", "PHASe", "PPULses", "PREShoot",
        "PWIDth", "RISetime", "SDEViation", "SOURce", "STATistics", "VAMPlitude", "VBASe", "VMAX", "VMIN", "VPP",
        "VTOP", "XMAX", "XMIN"]

        results = []
        for measureType in measureTypeList:
            if measureType in allowableInputs:
                results.append(self.basicQuery("MEASure:{}?".format(str(measureType))))
                time.sleep(.01)
            else:
                self.resourceManager.logMessage("0",3,"Oscope, getMeasure", measureType + " is not included in function allowable inputs.")
        return results

    def measRiseTime(self):
        """Returns float value of current channel rise time measurement in seconds"""
        return float(self.basicQuery("MEASure:RISetime?"))

    def measFallTime(self):
        """Returns float value of current channel fall time measurement in seconds"""
        return float(self.basicQuery("MEASure:FALLtime?"))
    
    def measPWidth(self):
        """Returns float value of current channel Pulse Width measurement in seconds"""
        return float(self.basicQuery("MEASure:PWIDth?"))
    
    def measAmp(self):
        """Returns float value of current channel amplitude measurement in volts"""
        return float(self.basicQuery("MEASure:VAMPlitude?"))

    def measP2P(self):
        """Returns Peak-to-Peak measurements value of current channel in volts"""
        return float(self.basicQuery("MEASure:VPP?"))

    def measVMax(self):
        """Returns float value of Voltage max on current measurement channel in volts"""
        return float(self.basicQuery("MEASure:VMAX?"))
    
    def measVMin(self):
        """Returns float value of Voltage min on current measurement channel in volts"""
        return float(self.basicQuery("MEASure:VMIN?"))
    
    def measBurstWidth(self):
        """Returns float value of Burst-Width on current measurement channel in seconds"""
        return float(self.basicQuery("MEASure:BWIDth?"))

    def measPSpace(self):
        """Returns float value of Pulse Spacing on current measurement channel in seconds"""
        return float(self.basicQuery("MEASure:NWIDth?"))

    def pulseEdgePosition(self, occurance):
        """Returns float value of tigger edge position on current measurement channel on screen in seconds
        given a requested <str: occurance> where occurance '0' would be the first occurance on screen."""
        return float(self.basicQuery("MEASure:TEDGE? {}".format(occurance)))

    def voltCrossPosition(self, volt, occurance):
        """Returns float value of voltage crossing position of current measurement channel in seconds given 
        <volts>, <str: occurance>."""
        return float(self.basicQuery("MEASure:TVALue? {0}, {1}".format(str(volt), occurance)))

    def getEdgeCnt(self):
        """Returns the onscreen rising edge count"""
        return int(float(self.basicQuery("MEASure:PEDGes?")))

##################################################
# Marker Commands
##################################################

    def setX1_Marker(self, x1pos, unit):
        """Sets the X1 marker given position <seconds>"""
        self.basicWrite("MARKer:X1Position {}s".format(self.timeAdjust(x1pos, unit)))

##################################################
# Acquisition Commands
##################################################

    def acqSetType(self, acq_type):
        """ Sets acquire <acq_type> given "NORMal", "AVERage", "HRESolution", "PEAK". """
        type_list = ["NORMal", "AVERage", "HRESolution", "PEAK"]
        if acq_type in type_list:
            self.basicWrite("ACQuire:TYPE {}".format(acq_type))
        else:
            self.resourceManager.logMessage("0",3,"Oscope, acqSetType", acq_type + " isn't an avaliable Acquire Type")
    
    def acqSetCount(self, count):
        """ Sets acquire averaging <count> given an integer 2 to 65535 """
        int_count = int(count)
        if (2 <= int_count <= 65535):
            self.basicWrite("ACQuire:COUNt {}".format(int_count))
        else:
            self.resourceManager.logMessage("0",3,"Oscope, acqSetCount", count + " out of 2-65535 range")
        
    def Digitize(self):
        """ Digitizes the acquired waveform on all channels """
        self.basicWrite("DIGitize")

    def digitizeChans(self, chan_list):
        """ Digitizes the acquired waveform on <chan#_list>. Ex [1, 3, 4] 
        If the input list is empty, all channels are digitized. """
        if len(chan_list) == 0:
            self.basicWrite("DIGitize")
        else:
            try:
                cmd = "DIGitize "
                for chan in chan_list:
                    if int(chan) in range(1,5):
                        cmd += "CHANnel{}".format(str(chan))
                self.basicWrite(cmd)
            
            except ValueError as e:
                self.resourceManager.logMessage("0",3,"Oscope, digitizeChans","Invalid Channel {}".format(e.args))

##################################################
# Waveform Commands
##################################################

    def waveSetFormat(self, wav_format):
        """ Sets the format for which the waveform data will be transfered to
        the PC controller. <wav_format> include WORD, BYTE, ASCII. """
        if wav_format.lower() in ["word", "byte", "ascii"]:
            self.basicWrite("WAVeform:FORMat {}".format(wav_format))
        else:
            self.resourceManager.logMessage("0",3,"Oscope, waveSetFormat","Invalid format {}".format(wav_format))
 
    def waveSource(self, chan):
        """ Set Wave source <chan> """
        self.basicWrite("WAVeform:SOURCe CHANnel{}".format(str(chan)))

    def waveRawData(self):
        """ Returns acquired waveform data """
        waveform = self.basicQuery("WAVeform:DATA?")
        return waveform
    
    def wavePreamble(self):
        """ Returns acquired waveform data preamble information """
        preamble = self.basicQuery("WAVeform:PREamble?")
        return preamble

    def wavePoints(self, points):
        """ Sets the maximum number of points returned to the controller """
        try:
            int(points)
            self.basicWrite("WAVeform:POINts {}".format(str(points)))
        except ValueError as e:
            self.resourceManager.logMessage("0",3,"Oscope, wavePoints","Invalid Points {}".format(e.args))
    
    def wavePointsMode(self, mode):
        """ Sets the Wave Acquisition Points <mode>. Avaliable modes are NORMal, MAXimum, RAW. """
        mode_list = ["NORMal", "MAXimum", "RAW"]
        if mode in mode_list:
            self.basicWrite("WAVeform:POINts:MODE {}".format(mode))
        else:
            self.resourceManager.logMessage("0", 3, "Oscope, wavePointsModes", "Invalid wave points mode {}".format(mode))

    def waveData(self,chan):
        """ Returns parsed time, and data for selected channel (chan) digitized waveform. """
        self.acqSetType("NORMal")
        self.waveSetFormat("ASCII")
        self.waveSource(chan)
        
        # Capture relevant pulse data and parse, generate a timelist as well.
        data = self.parseASCII(self.waveRawData())
        preamble = self.parsePreamble(self.wavePreamble())
        t = [preamble[4]*element for element in range(0, int(preamble[2]))]

        return t, data

    def waveSmoothData(self,chan):
        """ Returns savgol filtered wave data to reduce data noise """
        self.acqSetType("NORMal")
        self.waveSetFormat("ASCII")
        self.waveSource(chan)
        
        # Capture relevant pulse data and parse, generate a timelist as well.
        data = self.parseASCII(self.waveRawData())
        preamble = self.parsePreamble(self.wavePreamble())
        t = [preamble[4]*element for element in range(0, int(preamble[2]))]
        
        filtdata = savgol_filter(data, 101, 2)

        return t, filtdata

##################################################
# User plotting and support functions
##################################################

    def parseASCII(self, wav_ascii_data):
        """ Returns parsed waveform data as floating point list given the waveData in ASCII format """
        data = wav_ascii_data.strip().split(",")
        if " " in data[0]:
            data[0] = data[0].split(" ", 1)[1]
        elif "-" in data[0]:
            data[0] = data[0].split("-", 1)[1]
        else:
            print("Initial datablock not recognized")
        data = [float(element.strip(" ")) for element in data]
        return data

    def plotWave(self,chan):
        """ Plots wave. """
        t, d = self.waveData(chan)
        plt.plot(t, d)
        plt.show()

    def parsePreamble(self, preamble):
        """ Returns parsed preamble data for capture data to format a plot given scope preamble ASCII format.
        [0] = format, [1] = type, [2] = points, [3] = count, [4] = xincrement, [5] = xorigin, [6] = xref, 
        [7] = yincrement, [8] = yorigin, [9] = yreference. """
        parsed = preamble.split(",")
        parsed = [float(element) for element in parsed]
        return parsed

    def digiEdgeCnt(self, threshold, **kwargs):
        """ Returns rising edge count, more accurate than getEdgeCount. <threshold> in volts to detect rising edge.
        default wave <source=1> and <filt=1> on """
        # Default use filter and source 1
        source = 1
        filt = 1

        # unpack kwargs and apply effects
        for key, value in kwargs.items():
            if ((key == 'source') and (value in range(1,5))):
                source = value
            if ((key == 'filt') and (value==1 or value==0)):
                filt = value
        
        self.acqSetType("NORMal")
        self.waveSetFormat("ASCII")
        self.waveSource(source)
        
        # Capture relevant pulse data and parse, generate a timelist as well.
        data = self.parseASCII(self.waveRawData())
        preamble = self.parsePreamble(self.wavePreamble())
        t = [preamble[4]*element for element in range(0, int(preamble[2]))]
        
        if filt == 1:
            data = savgol_filter(data, 101, 2)

        pEdge = []
        nEdge = []
        thresh = threshold

        for i in range(1, len(data)):
            if (data[i-1] < thresh) and (data[i] > thresh):
                pEdge.append(t[i])
            elif (data[i-1] > thresh) and (data[i] < thresh):
                nEdge.append(t[i])
            else:
                pass
        
        return len(pEdge)

    def digiEdgePos(self, threshold, **kwargs):
        """ Returns rising edge positions, more accurate than pulseEdgePosition. <threshold> in volts to detect rising edge. 
        default wave <source=1> and <filt=1> on """
        # Default use filter and source 1
        source = 1
        filt = 1

        # unpack kwargs and apply effects
        for key, value in kwargs.items():
            if ((key == 'source') and (value in range(1,5))):
                source = value
            if ((key == 'filt') and (value==1 or value==0)):
                filt = value
        
        self.acqSetType("NORMal")
        self.waveSetFormat("ASCII")
        self.waveSource(source)
        
        # Capture relevant pulse data and parse, generate a timelist as well.
        data = self.parseASCII(self.waveRawData())
        preamble = self.parsePreamble(self.wavePreamble())
        t = [preamble[4]*element for element in range(0, int(preamble[2]))]
        
        if filt == 1:
            data = savgol_filter(data, 101, 2)

        pEdge = []
        nEdge = []
        thresh = threshold

        for i in range(1, len(data)):
            if (data[i-1] < thresh) and (data[i] > thresh):
                pEdge.append(t[i])
            elif (data[i-1] > thresh) and (data[i] < thresh):
                nEdge.append(t[i])
            else:
                pass

        pEdgeTime = map(lambda x: x + preamble[5], pEdge)
        nEdgeTime = map(lambda x: x + preamble[5], nEdge)

        return list(pEdgeTime), list(nEdgeTime)

    def posEdgePosition(self, preamble, data, threshold, **kwargs):
        """ Returns rising edge positions, more accurate than pulseEdgePosition. Input parsed <preamble>, 
        <data> as parsed ASCII, <threshold> in volts to detect rising edge. Default wave <source=1> and <filt=1> on """
        # Default use filter
        filt = 1

        # unpack kwargs and apply effects
        for key, value in kwargs.items():
            if ((key == 'filt') and (value==1 or value==0)):
                filt = value

        t = [preamble[4]*element for element in range(0, int(preamble[2]))]
        if filt == 1:
            data = savgol_filter(data, 101, 2)

        pEdge = []
        thresh = threshold
        for i in range(1, len(data)):
            if (data[i-1] < thresh) and (data[i] > thresh):
                pEdge.append(t[i])
            else:
                pass
        pEdgeTime = map(lambda x: x + preamble[5], pEdge)

        return list(pEdgeTime)

    def negEdgePosition(self, preamble, data, threshold, **kwargs):
        """ Returns falling edge positions, more accurate than pulseEdgePosition. Input parsed <preamble>, 
        <data> as parsed ASCII, <threshold> in volts to detect rising edge. Default wave <source=1> and <filt=1> on """
        # Default use filter
        filt = 1

        # unpack kwargs and apply effects
        for key, value in kwargs.items():
            if ((key == 'filt') and (value==1 or value==0)):
                filt = value

        t = [preamble[4]*element for element in range(0, int(preamble[2]))]
        if filt == 1:
            data = savgol_filter(data, 101, 2)
        
        nEdge = []
        thresh = threshold
        for i in range(1, len(data)):
            if (data[i-1] > thresh) and (data[i] < thresh):
                nEdge.append(t[i])
            else:
                pass
        nEdgeTime = map(lambda x: x + preamble[5], nEdge)

        return list(nEdgeTime)

    def plotSmoothWave(self,chan):
        """ Plots smoothed waveform """
        t, d = self.waveSmoothData(chan)
        plt.plot(t, d)
        plt.show()

    def timeAdjust(self, value, unit):
        """ time scales "s", "ms", "us", "ns", "ps" """
        unit = unit.lower()
        try:
            float(value)
            if unit == "s":
                return (str(value))
            elif unit == "ms":
                return (str(value) + "e-3")
            elif unit == "us":
                return (str(value) + "e-6")
            elif unit == "ns":
                return (str(value) + "e-9")
            elif unit == "ps":
                return (str(value) + "e-12")
            else:
                self.resourceManager.logMessage("0",3,"Oscope, timeAdjust","Invalid timescale option ({})".format(unit))
                return 0
        except ValueError as e:
            self.resourceManager.logMessage("0",3,"Oscope, timeAdjust","Invalid time value {}".format(e.args))
