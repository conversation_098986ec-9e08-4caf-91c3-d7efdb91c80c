# -*- coding: utf-8 -*-
"""
Copyright:   All source code, and data contained in this document is
Proprietary and Confidential to Honeywell International Inc. and must
not be reproduced, transmitted, or disclosed; in whole or in part,
without the express written permission of Honeywell International Inc.

Created on Tue Mar 10 15:32:53 2020

@author: H157797, E669660

 The module is provided an interface to calculate the FEC for UAT Basic/Long ADSB
 and one block of Ground Uplink Message by using reedsolo package.

 Note:

Available Subroutines:
 fec_parity(msgType, msgIn):  Calculate the FEC based on msgType and msgIn.
   Input Parameters:
              msgType:  BASIC_ADSB_MSG_TYPE | LONG_ADSB_MSG_TYPE | GNDUPLINK_MSG_TYPE
              msgIn:    the message data block per msgType
   Output Parameter:  return FEC data block per msgType 
 Example:
    fecData = fec_parity(LONG_ADSB_MSG_TYPE,"88C14FE677A75B489603580CAA606FD81EE91B64CBABA0563FC0CE1C2DA3016B2BA3")
"""

import reedsolo

BASIC_ADSB_MSG_TYPE = 1
BASIC_ADSB_MSG_LEN = 30
BASIC_ADSB_DATA_LEN = 18
BASIC_ADSB_FEC_LEN = 12

LONG_ADSB_MSG_TYPE = 2
LONG_ADSB_MSG_LEN = 48
LONG_ADSB_DATA_lEN = 34
LONG_ADSB_FEC_LEN = 14

GNDUPLINK_MSG_TYPE = 3
GNDUPLINK_MSG_LEN = 92
GNDUPLINK_DATA_LEN =72
GNDUPLINK_FEC = 20

# A function that will calculate the FEC based on msgType and msgIn
# msgType: uat message type which can only be BASIC_ADSB_MSG_TYPE | LONG_ADSB_MSG_TYPE | GNDUPLINK_MSG_TYPE.
#   1 presents Basic Adsb message 
#   2 presents Long Adsb message 
#   3 presents one block of ground uplink message 
# msgIn: The message data block which is used for calculating the FEC.
#
def fec_parity(msgType, msgIn):
    
    if msgType == GNDUPLINK_MSG_TYPE:
        dataLen = GNDUPLINK_MSG_LEN
        payloadLen = GNDUPLINK_DATA_LEN
        fecLend = GNDUPLINK_FEC
    elif msgType == LONG_ADSB_MSG_TYPE:
        dataLen = LONG_ADSB_MSG_LEN
        payloadLen = LONG_ADSB_DATA_lEN
        fecLend = LONG_ADSB_FEC_LEN
    elif msgType == BASIC_ADSB_MSG_TYPE:
        dataLen = BASIC_ADSB_MSG_LEN
        payloadLen = BASIC_ADSB_DATA_LEN
        fecLend = BASIC_ADSB_FEC_LEN
    else:
        print("The msgType should be BASIC_ADSB_MSG_TYPE | LONG_ADSB_MSG_TYPE | GNDUPLINK_MSG_TYPE")

    if len(msgIn) != payloadLen*2:
        print("The length of the input message should be "+ str(payloadLen*2))
        return

    dataArray = bytearray.fromhex("{value:0<{w}}".format(value = msgIn ,w = payloadLen*2))
    
    #
    #Per DO-282B page 24 - 25 (Physical page 54-55), the primitive polynomial of the code is as follows:
    #       p(x) = x^8 + x^7 + x^2 + x + 1  where prim is 110000111 (0x187)
    # The generator polynomial is 
    #      (X-a^120)(X-a^121)(X-a^123) ... (X-a^P) where first consecutive root (fcr) is 120
    #        a is a primitive element of a Galois field of size 256 GF(2^8)
    #        P=131 for RS(30,18) code of BASIC_ADSB_MSG_TYPE, 
    #        P=133 for RS(48,34) code of LONG_ADSB_MSG_TYPE
    #        P=139 for RS(92,72) code of GNDUPLINK_MSG_TYPE (one of six blocks)
    #     
    rs =reedsolo.RSCodec(nsym =fecLend, nsize=payloadLen+fecLend,fcr=120,prim=0x187,generator=2,c_exp=8)
    
    checksummed =rs.encode(dataArray)
    return checksummed.hex().upper()[-2*fecLend:]
      
