"""
Created on Mon March 30 3:02:30 2020

@author: E589493
         <PERSON><PERSON><PERSON>NS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the instantion of power supply class.


HISTORY:

03/30/2020   KF    Initial Release.
04/23/2020   MR    Added various error handling statements and debug to ObserverClient
0X/XX/2020   TED   TED team made changes
10/0X/2020   NEZ   

"""
import time

class N6700DCPS():
    def __init__(self,ate_rm):
        N6700DCPSIP = "TCPIP0::***********::INSTR"
        self.resourceManager = ate_rm
        #self.N6700DCPS = self.resourceManager.rm.open_resource(N6700DCPSIP)
        # self.tvl=ate_rm.tvl
        tvlTxt = "Resource Opened."
        self.basicTvlMsg(tvlTxt)

##################################################
# Basic Commands
##################################################

    def Ident(self):
        cmd = "*IDN?"
        return "N6700C"

    def Reset(self):
        cmd = "*RST"
        return self.basicWrite(cmd)
    
    def close(self):
        #self.N6700DCPS.close()
        tvlTxt = "Resource Closed."
        self.basicTvlMsg(tvlTxt)

    def basicWrite(self, cmd):
        tvlTxt = "> %s" % cmd
        self.basicTvlMsg(tvlTxt)
        #resp = self.N6700DCPS.write("%s" % cmd)
        return "pass"

    def basicQuery(self, cmd,logEnable=True):
        tvlTxt = "> %s" % cmd
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        resp = self.N6700DCPS.query("%s" % cmd).strip()
        tvlTxt = "< %s" % resp
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        return resp

    def basicTvlMsg(self, tvlTxt):
        #self.tvl.SendMessageToObserver("0",1,"TXD Python Lib: N6700DCPS.py", tvlTxt)
        self.resourceManager.logMessage("0", 1, "TXD Python Lib: N6700DCPS.py", tvlTxt)

    def getVolt(self, chnl):
        #[SOURCE:]VOLTAGE[:LEVEL][:IMMEDIATE][:AMPLITUDE] <value>, (@chanlist)
        cmd = "VOLT? (@%d)" % chnl
        return self.basicQuery(cmd)

    def setVolt(self, lvl, chnl):
        #[SOURCE:]VOLTAGE[:LEVEL][:IMMEDIATE][:AMPLITUDE] <value>, (@chanlist)
        cmd = "VOLT %f, (@%d)" % (lvl, chnl)
        return self.basicWrite(cmd)

    def getSlew(self, chnl):
        #[SOURCE:]VOLTAGE:SLEW[:POSITIVE][:IMMEDIATE] <value>, (@chanlist)
        cmd = "VOLT:SLEW? (@%d)" % chnl
        return self.basicQuery(cmd)

    def setSlew(self, rate, chnl):
        #[SOURCE:]VOLTAGE:SLEW[:POSITIVE][:IMMEDIATE] <value>, (@chanlist)
        cmd = "VOLT:SLEW %f, (@%d)" % (rate, chnl)
        return self.basicWrite(cmd)

    def getCurrLim(self, chnl):
        #[SOURCE:]CURRENT[:LEVEL][:IMMEDIATE][:AMPLITUDE] <value>, (@chanlist)
        cmd = "CURR? (@%d)" % chnl
        return self.basicQuery(cmd)

    def setCurrLim(self, lvl, chnl):
        #[SOURCE:]CURRENT[:LEVEL][:IMMEDIATE][:AMPLITUDE] <value>, (@chanlist)
        cmd = "CURR %f, (@%d)" % (lvl, chnl)
        return self.basicWrite(cmd)

    def measCurr(self, chnl,logEnable=True):
        #MEASURE[:SCALAR]:CURRENT[:DC]? (@chanlist)
        cmd = "MEAS:CURR? (@%d)" % chnl
        return 0.54
    

    def measVolt(self, chnl,logEnable=True):
        #MEASURE[:SCALAR]:VOLTAGE[:DC]? (@chanlist)
        cmd = "MEAS:VOLT? (@%d)" % chnl
        return self.basicQuery(cmd,logEnable)

    def getVoltLim(self, chnl):
        #[SOURCE:]VOLTAGE:PROTECTION[:LOCAL][:LEVEL] <value>, (@chanlist)
        cmd = "VOLT:PROT? (@%d)" % chnl
        return self.basicQuery(cmd)

    def setVoltLim(self, lvl, chnl):
        #[SOURCE:]VOLTAGE:PROTECTION[:LOCAL][:LEVEL] <value>, (@chanlist)
        cmd = "VOLT:PROT %f, (@%d)" % (lvl, chnl)
        return self.basicWrite(cmd)

    def setOutputState(self, state, chnl):
        #OUTPUT[:STATE] <state> (@chanlist)
        cmd = "OUTPUT %s, (@%d)" % (state, chnl)
        return self.basicWrite(cmd)

    def getOutputState(self, chnl):
        #OUTPUT[:STATE] <state> (@chanlist)
        cmd = "OUTPUT? (@%s)" % (chnl)
        return self.basicQuery(cmd)