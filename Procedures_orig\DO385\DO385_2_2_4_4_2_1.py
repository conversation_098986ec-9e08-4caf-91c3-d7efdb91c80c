# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-185E/385 MOPs requirement for
             Mode C Reply Reception, Section *******.2.1.
             
             The following procedures verify the ability of the Mode C reply processor
             to properly detect and decode reply signals, to resolve overlapping replies 
             and to reject narrow pulses.
             
             Step1: This step tests *******.4.1 Bracket Detection and Reply Decoding.             
             There are six scenario files defined and loaded on the RGS.  These scenarios are
             set up as defined in the MOPs, section *******.4.1.  The reply pulse 
             characteristics are already defined for each scenario (A,B,C,,D,E and F).
             1) ModeC_Rply_A, track established within 10 seconds
             2) ModeC_Rply_B, track established within 10 seconds
             3) ModeC_Rply_C, track established within 10 seconds
             4) ModeC_Rply_D, track established within 10 seconds
             5) ModeC_Rply_E, track Not established within 10 seconds
             6) ModeC_Rply_F, track Not established within 10 seconds
             In these scenarios the target should/shouldnot be detected within 10 seconds
             of the start of the secnario.
             
             Step2: This step tests *******.4.2 Wide Pulse Detection and Pulse Position
             discrimination.  This step verfies the ability of the Mode C reply processor to
             detect the presence of all bracket or code pulses within three plies that have 
             merged toformwide pulses and to correctly associate each pulse with the appropriate
             reply.  There are two scenarios used for this step:
             1) ModeC_Pulse_Det_A
             2) ModeC_Pulse_Det_B
    
             Step3: This step tests *******.4.3 Narrow Pulse Rejection.  This test will verify
             the ability of the Mode C reply processor to reject narrow pulses. The following
             scenarios are used for this test:
             1) ModeC_Narw_A
             2) ModeC_Narw_B
    
             Step4: This step test *******.4.4 Detection of Garbled Replies.  This test will 
             verify the ability of the Mode C reply processor to achieve a minimum level of 
             overall performance in the detection of reply brackets and codes in which the
             reply pulses have been detected to varying degrees of garble.  The following
             scenario is used for this test:
             1) ModeC_Garb.
             
             
             
INPUTS:      RGS,ARINC,Top_Cable_Loss,Bottom_Cable_Loss
OUTPUTS:     Pass/Fail for each scenario, arrays of elements:
             -- Step1_Results[6]
             -- Step2_Results[2]
             -- Step3_Results[3]
             -- Step4_Result

HISTORY:

08/25/2020   MRS    Initial Release.
10/09/2020   MRS    Updated for new ATE_RM.
03/12/2021   MRS    Updates for Lobster                     
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers.RGS2000NG import RGS2000NG  
from TXDLib.Handlers.ARINC_Client import ARINC_Client



##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def readTCASData(ARINC):
    """ Reads data via ARINC client, returns 4 lists containing data
        in the order intruders, range, altitude, bearing"""
    #Read the Number of TCAS Intruders
    intr = ARINC.TCAS_Read(b"READ,TCAS_OUT,INT")
    print("Number of Intruders",intr)

    #Get a List of All TCAS Intruders
    data = ARINC.TCAS_Read(b"READ,TCAS_OUT,ALL")
    print("Intruder Return String: ",data)
    #Decode the Return string into separate lists
    itr,rng,alt,brg = ARINC.TCAS_Decode(data)
    return itr, rng, alt, brg

def testIntruders(ARINC, timeInterval):
    """ Record intruder data end of time interval and 
    tests that data matches. Returns 4 booleans in the order 
    intruders, range, altitude, bearing """

    time.sleep(timeInterval)
    
    
    # get intruder data after one timeInterval
    itr1, rng1, alt1, brg1 = readTCASData(ARINC)

    print ("Intruders: ",itr1)
    print ("Range: ",rng1)
    print ("Altitude: ",alt1)
    print ("Bearing: ",brg1)


    return itr1,rng1,alt1,brg1


##############################################################################
################# MAIN     ##################################################
##############################################################################
def Test_2_4_2_1_4_1(rm, rgs,ARINC):
    """ DO-185E/385, Mode C Reply Reception, Sect *******.4.1 """
    
    print ("*** DO-185E/385, Mode C Reply Reception, Sect *******.2.1 ***\r\n")
    
    rm.logMessage(0,"*Test_*******.2.1 Start")  
    
    #Initialize results:
    Step1_Results = [0,0,0,0,0,0]
    Step2_Results = [0,0]
    Step3_Results = [0,0]
    Step4_Results = [0]    
    
    #Initialize Aircraft Position for scenarios
    rgs.stopScen() #stop scenario if already running.
    rgs.init_own_aircraft_pos('9600')
    time.sleep(2)
    
    # Step1:Load and Start Scenario A
    rm.logMessage(0,"Step1:Test_385_Mode C Reply Reception - Scenario A") 
    print("Step1:Scenario A")
    #rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODEC_RPLY_A.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 1 :
        Step1_Results[0] = 1
      
    # Step1:Load and Start Scenario B
    rm.logMessage(0,"Step1:Test_385_Mode C Reply Reception - Scenario B") 
    print("Step1:Scenario B")
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODEC_RPLY_B.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 1 :
        Step1_Results[1] = 1
                   
    # Step1:Load and Start Scenario C
    rm.logMessage(0,"Step1:Test_385_Mode C Reply Reception - Scenario C") 
    print("Step1:Scenario C")
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODEC_RPLY_C.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 1 :
        Step1_Results[2] = 1

    # Step1:Load and Start Scenario D
    rm.logMessage(0,"Step1:Test_385_Mode C Reply Reception - Scenario D") 
    print("Step1:Scenario D")
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODEC_RPLY_D.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 1 :
        Step1_Results[3] = 1

    # Step1:Load and Start Scenario E
    rm.logMessage(0,"Step1:Test_385_Mode C Reply Reception - Scenario E") 
    print("Step1:Scenario E")
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODEC_RPLY_E.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 0 :
        Step1_Results[4] = 1
    
    # Step1:Load and Start Scenario F
    rm.logMessage(0,"Step1:Test_385_Mode C Reply Reception - Scenario F") 
    print("Step1:Scenario F")
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODEC_RPLY_F.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 0 :
        Step1_Results[5] = 1
        
    
        
    #Initialize Aircraft Position for scenarios
    rgs.stopScen() #stop scenario if already running.
    rgs.init_own_aircraft_pos('33400')
    time.sleep(2)
    
    
    # Step2:Load and Start Scenario A
    rm.logMessage(0,"Step2:Test_385_Mode C Reply Reception - Scenario A") 
    print("Step2:Scenario A")
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('ModeC_Pulse_Det_A.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 1 :
        Step2_Results[0] = 1
    
    # Step2:Load and Start Scenario B
    rm.logMessage(0,"Step2:Test_385_Mode C Reply Reception - Scenario B") 
    print("Step2:Scenario B")
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('ModeC_Pulse_Det_B.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 1 :
        Step2_Results[1] = 1
        
        
        
    #Initialize Aircraft Position for scenarios
    rgs.stopScen() #stop scenario if already running.
    rgs.init_own_aircraft_pos('8600')
    time.sleep(2)

     # Step3:Load and Start Scenario A
    rm.logMessage(0,"Step3:Test_385_Mode C Reply Reception - Scenario A") 
    print("Step3:Scenario A")
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('ModeC_Narw_A.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 2 :
        Step3_Results[0] = 1
    
    # Step3:Load and Start Scenario B
    rm.logMessage(0,"Step3:Test_385_Mode C Reply Reception - Scenario B") 
    print("Step3:Scenario B")
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('ModeC_Narw_B.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 2 :
        Step3_Results[1] = 1

    
    #Initialize Aircraft Position for scenarios
    rgs.stopScen() #stop scenario if already running.
    rgs.init_own_aircraft_pos('6000')
    time.sleep(2)

     # Step4:Load and Start Scenario A
    rm.logMessage(0,"Step4:Test_385_Mode C Reply Reception - Scenario A") 
    print("Step4:Scenario A")
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('ModeC_Garb.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 15)
    if len(intr) == 3 :
        Step4_Results[0] = 1

    rgs.stopScen() #stop scenario if already running.
    
    rm.logMessage(0,"Step1_Results: " + str(Step1_Results))
    rm.logMessage(0,"Step2_Results: " + str(Step2_Results))
    rm.logMessage(0,"Step3_Results: " + str(Step3_Results))
    rm.logMessage(0,"Step4_Results: " + str(Step4_Results))
    rm.logMessage(2,"Done,Closing Session")    
    
    return Step1_Results + Step2_Results + Step3_Results + Step4_Results

##############################################################################
#run as main from command line
if __name__ == "__main__":
    

    #SetUP Resource Manager
    rm = ate_rm()
   
    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")

    #Initiazlie the RGS
    rgs = RGS2000NG(rm)
    res = Test_2_4_2_1_4_1(rm,rgs, ARINC)
    
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()
    
    #Close RGS
    rgs.close()
