# -*- coding: utf-8 -*-
"""
Created on Tue Jan  6 08:58:20 2021

@author: E282068
         M<PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the FAR43 I ATCRBS All Call requiremensts.
             
             ATCRBS-Only All-Call Interrogation: Interrogate the Mode S transponder
             with the ATCRBS-only all-call interrogation (0.8 microsecond P4 pulse)
             and verify that no reply is generated.  
             
INPUTS:      ate_rm,ATC5000NG,RFBOB
OUTPUTS:     ModeA_Msg    = % Reply
             ModeAOnly_Msg  = % Reply
             NOTEs: 
                 1) Interrogations are ModeA and ModeA Only messages
                              

HISTORY:
01/06/2021   MRS    Initial Release.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import RFBOB


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def init_RFBOB(rfbob):
    """ initializes the RF BOB to Prim & Sec to BOTTOM port --- should be default. """
    #rfbob.connect()
    rfbob.setSwitch(0,0)   #Primary Bottom Port
    rfbob.setSwitch(1,0)   #Secondary Bottom Port
    rfbob.setSwitch(10,1)
    #rfbob.disconnect()


##############################################################################
################# MAIN     ##################################################
##############################################################################
        
def FAR43_I(rm,atc,rfbob):
    """ FAR43, I ATCRBS-Only """
    rm.logMessage(2,"*** FAR43 I, ATCRBS-Only ***")
    
    #initialize rfbob
    init_RFBOB(rfbob)

    #Initialize Results
    ModeA_Msg = 0                      #Values read by TestStand
    ModeAOnly_Msg = 0                      #Values read by TestStand

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set Up Transponder -- MODE A  
    atc.transponderModeA()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna

        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            
                           
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    ModeA_Msg = replyrate[1]                                     #ModeS Bottom
            
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Set Up Transponder -- MODE A Only 
    atc.transponderModeA_Only()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   

    atc.waitforstatus()            
                           
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    ModeAOnly_Msg = replyrate[1]                                     
            
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    rm.logMessage(2,("Validities: %f,%f" % (ModeA_Msg,ModeAOnly_Msg)))   
    rm.logMessage(2,"Done, closing session")

    
    return [ModeA_Msg,ModeAOnly_Msg]

##############################################################################
#run as main from command line
if __name__ == "__main__":
    
    rm = ate_rm()

    #Initialize the RFBOB
    rf_obj = RFBOB(rm)
    rf_obj.connect()
 
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

    
    res = FAR43_I(rm,atc_obj,rf_obj)
    

    atc_obj.close()
    rf_obj.disconnect()






