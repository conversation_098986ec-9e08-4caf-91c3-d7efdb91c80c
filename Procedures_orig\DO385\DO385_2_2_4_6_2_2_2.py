# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-185E/385 MOPs requirement for
             Mode S Reply Initiation, Section *******.2.2.2
             
             The following procedures verify the ability of the Mode S reply processor
             to properly detect and decode reply signals, to resolve overlapping replies 
             and to reject narrow pulses.
             
             Step1: This step tests *******.5.1 Mode S Preamble Reception. 
             These tests verify that the ModeS reply processor correclty detects the presence
             of valid ModeS preambles whose pulse characteristics are within the allowable limits
             and rejects preambles whose puls spacings, positions and leading edge characteristics
             are outside the allowable limits.
             There are ten scenario files defined and loaded on the RGS.  These scenarios are
             set up as defined in the MOPs, section *******.5.1.  The reply pulse 
             characteristics are already defined for each scenario (A,B,C,,D,E,F G,H,I and J).
             1) ModeS_Pre_A, track established within 10 seconds
             2) ModeS_Pre_B, track established within 10 seconds
             3) ModeS_Pre_C, track established within 10 seconds
             4) ModeS_Pre_D, track established within 10 seconds
             5) ModeS_Pre_E, track Not established within 10 seconds
             6) ModeS_Pre_F, track Not established within 10 seconds
             7) ModeS_Pre_G, track Not established within 10 seconds
             9) ModeS_Pre_H, track Not established within 10 seconds
             9) ModeS_Pre_I, track Not established within 10 seconds
             10) ModeS_Pre_J, track Not established within 10 seconds
             In these scenarios the target should/shouldnot be detected within 10 seconds
             of the start of the secnario.
             
                          
             
             
INPUTS:      RGS,ARINC,Top_Cable_Loss,Bottom_Cable_Loss
OUTPUTS:     Pass/Fail for each scenario, arrays of elements:
             -- Step1_Results[10]

HISTORY:

08/25/2020   MRS    Initial Release.
                                
"""

#Required Libraries

import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers.RGS2000NG import RGS2000NG  
from TXDLib.Handlers.ARINC_Client import ARINC_Client



##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def readTCASData(ARINC):
    """ Reads data via ARINC client, returns 4 lists containing data
        in the order intruders, range, altitude, bearing"""
    #Read the Number of TCAS Intruders
    intr = ARINC.TCAS_Read(b"READ,TCAS_OUT,INT")
    print("Number of Intruders",intr)

    #Get a List of All TCAS Intruders
    data = ARINC.TCAS_Read(b"READ,TCAS_OUT,ALL")
    print("Intruder Return String: ",data)
    #Decode the Return string into separate lists
    itr,rng,alt,brg = ARINC.TCAS_Decode(data)
    return itr, rng, alt, brg

def testIntruders(ARINC, timeInterval):
    """ Record intruder data end of time interval and 
    tests that data matches. Returns 4 booleans in the order 
    intruders, range, altitude, bearing """

    time.sleep(timeInterval)
    
    
    # get intruder data after one timeInterval
    itr1, rng1, alt1, brg1 = readTCASData(ARINC)

    print ("Intruders: ",itr1)
    print ("Range: ",rng1)
    print ("Altitude: ",alt1)
    print ("Bearing: ",brg1)


    return itr1,rng1,alt1,brg1


##############################################################################
################# MAIN     ##################################################
##############################################################################
def Test_2_4_2_1_4_2(rm, rgs,ARINC):
    """ DO-185E/385, Mode S Reply Reception, Sect 2.2.4.4.2.2 """
    
    rm.logMessage(0,"*** DO-185E/385, Mode 5 Reply Reception, Sect 2.2.4.4.2.2 ***")
    

    rm.logMessage(0,"*Test_2.2.4.4.2.2 - Start")  
    
    #Initialize results:
    Step1_Results = [0,0,0,0,0,0,0,0,0,0]
    
    #Initialize Aircraft Position for scenarios
    rgs.stopScen() #stop scenario if already running.
    rgs.init_own_aircraft_pos('8000')
    time.sleep(2)
    
    # Step1:Load and Start Scenario A
    rm.logMessage(0,"Step1:Test_385_Mode S Reply Reception - Scenario A") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODES_PRE_A.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 1 :
        Step1_Results[0] = 1
      
    # Step1:Load and Start Scenario B
    rm.logMessage(0,"Step1:Test_385_Mode S Reply Reception - Scenario B") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODES_PRE_B.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 1 :
        Step1_Results[1] = 1
                  
    # Step1:Load and Start Scenario C
    rm.logMessage(0,"Step1:Test_385_Mode S Reply Reception - Scenario C") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODES_PRE_C.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 1 :
        Step1_Results[2] = 1

    # Step1:Load and Start Scenario D
    rm.logMessage(0,"Step1:Test_385_Mode S Reply Reception - Scenario D") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODES_PRE_D.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 1 :
        Step1_Results[3] = 1
    
    # Step1:Load and Start Scenario E
    rm.logMessage(0,"Step1:Test_385_Mode S Reply Reception - Scenario E") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODES_PRE_E.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 0 :
        Step1_Results[4] = 1
    
    # Step1:Load and Start Scenario F
    rm.logMessage(0,"Step1:Test_385_Mode S Reply Reception - Scenario F") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODES_PRE_F.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 0 :
        Step1_Results[5] = 1
        
    # Step1:Load and Start Scenario G
    rm.logMessage(0,"Step1:Test_385_Mode S Reply Reception - Scenario G") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODES_PRE_G.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 0 :
        Step1_Results[6] = 1
           
    # Step1:Load and Start Scenario H
    rm.logMessage(0,"Step1:Test_385_Mode S Reply Reception - Scenario H") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODES_PRE_H.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 0 :
        Step1_Results[7] = 1
    
    # Step1:Load and Start Scenario I
    rm.logMessage(0,"Step1:Test_385_Mode S Reply Reception - Scenario I") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODES_PRE_I.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 0 :
        Step1_Results[8] = 1

    # Step1:Load and Start Scenario J
    rm.logMessage(0,"Step1:Test_385_Mode S Reply Reception - Scenario J") 
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('MODES_PRE_J.csv')
    time.sleep(15)
    rgs.startScen()
    intr,rng,alt,brg = testIntruders(ARINC, 10)
    if len(intr) == 0 :
        Step1_Results[9] = 1

    
    rm.logMessage(0,"Test_2.2.4.4.2.2 - Done")    
    rgs.stopScen() #stop scenario if already running.
    
    rm.logMessage(0,"Step1_Results: " + str(Step1_Results))
    
    return Step1_Results

##############################################################################
#run as main from command line
if __name__ == "__main__":
    #SetUP Resource Manager
    rm = ate_rm()
   
    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")

    #Initiazlie the RGS
    rgs = RGS2000NG(rm)
  
    
    res = Test_2_4_2_1_4_2(rm, rgs, ARINC)
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()
    
    #Close RGS
    rgs.close()

