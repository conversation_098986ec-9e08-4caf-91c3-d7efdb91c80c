# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Reply Transmission Frequency, Section *******.1.
             
             "Interrogate the transponder with a standard Mode A interrogation.
             Use a 14 pulse (7777) reply group. Repeat with a standard 
             ModeA/ModeS All Call.  Determine the reply frequency for both
             types."
             
INPUTS:      RM,ATC,PathLoss
OUTPUTS:     'Frequencies' array of frequencies for ModeA and ModeA/S

HISTORY:

02/06/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review
02/25/2021   MRS    Updates for new Handlers and Lobster.
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# MAIN     ##################################################
##############################################################################
        
def Test_2_3_2_2_1(rm,atc,PathLoss):
    """ DO-181E, Reply Transmission Frequency,Sect *******.1 """
    
    rm.logMessage(2,"*** DO-181E, Reply Transmission Frequency,Sect *******.1 ***\r\n")
    
    #Initialize Frequencies
    Frequencies = [0.0 , 0.0]                               #Values read by TestStand

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
         
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(25)
    
    atc.waitforstatus()    

    #Measure Frequency
    Frequencies[0] = atc.getPulseFrequency(2)    
    
    # fix for erroneous frequency
    count = 0
    while Frequencies[0] == 20 and count < 10:
        Frequencies[0] = atc.getPulseFrequency(2)
        count = count + 1            
            
    #print result at this frequency
    rm.logMessage(0,("RESULT1: Frequency %f") % (Frequencies[0])) 

    #Turn OFF RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    #Set Up Transponder -- MODE A/MODE S All Call
    atc.transponderModeAS()
    atc.gwrite(":ATC:XPDR:PRF 50")    #Pulse Repatition
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
     
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(25)
    
    atc.waitforstatus()    
        
   
    #Measure Frequency
    Frequencies[1] = atc.getPulseFrequency(2)    

    # fix for erroneous frequency reply
    count = 0
    while Frequencies[1] == 20 and count < 10:
        Frequencies[1] = atc.getPulseFrequency(2)
        count = count + 1            

    #print result at this frequency
    rm.logMessage(0,("RESULT2: Frequency %f") % (Frequencies[1])) 

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    rm.logMessage(0,"Test_2_3_2_2_1 - Done: " + str(Frequencies))       
    rm.logMessage(2,"Done, closing session")

    return Frequencies

##############################################################################
#run as main from command line
if __name__ == "__main__":

    #SetUP Resource Manager
    rm = ate_rm()
 
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    
 
    res = Test_2_3_2_2_1(rm,atc_obj,-12.0)
    
    
    atc_obj.close()

    


