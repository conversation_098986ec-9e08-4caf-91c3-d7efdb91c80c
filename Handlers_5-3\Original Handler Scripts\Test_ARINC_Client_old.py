# -*- coding: utf-8 -*-
"""
Created on Wed Jun 24 12:45:00 2020

Example for the ARINC_Client class

@author: E282068
"""
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers.ARINC_Client import ARINC_Client

#from ARINC_Client import ARINC_Client
import time

###############################################################################

#SetUP Resource Manager
rm = ate_rm()
 
#Instanciate the Class 
a_client = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")

#------------------------------------------------------------------------------
#DME Commands
#------------------------------------------------------------------------------
#READ A429 Words 
data = a_client.DME_Read(b"READ,DME_OUT,035") 
print("Received from Server: ",data)
a_client.decode_arinc_word(data)

data = a_client.DME_Read(b"READ,DME_OUT,201") 
print("Received from Server: ",data)
a_client.decode_arinc_word(data)

data = a_client.DME_Read(b"READ,DME_OUT,202") 
print("Received from Server: ",data)
a_client.decode_arinc_word(data)

data = a_client.DME_Read(b"READ,DME_OUT,246") 
print("Received from Server: ",data)
a_client.decode_arinc_word(data)

data = a_client.DME_Read(b"READ,DME_OUT,247") 
print("Received from Server: ",data)
a_client.decode_arinc_word(data)

data = a_client.DME_Read(b"READ,DME_OUT,350") 
print("Received from Server: ",data)
a_client.decode_arinc_word(data)

#READ Distance
#automatically does a read, decodes, and gets distance
print("------------------------------------------------------------")
data = a_client.getDistance_201()
print("Received from Server: ",data)

#READ Channel
#automatically does a read, decodes, and gets channel
data = a_client.getChannel_035()
print("Recieved from Server: ", data)
print("------------------------------------------------------------")


#READ/WRITE, perform a Write and then Read Back the result
#write,bus,label,ssm,sdi,data(bits 28 thru 11)
data = a_client.DME_Write(b"WRITE,DME_IN,035,0,0,0x000ffff")
print("Received from Server: ",data)
time.sleep(1)
data = a_client.DME_Read(b"READ,DME_OUT,035")    
print("Received from Server: ",data)
a_client.decode_arinc_word(data)

data = a_client.DME_Write(b"WRITE,DME_IN,035,1,0,0x003a5a5")
print("Received from Server: ",data)
time.sleep(1)
data = a_client.DME_Read(b"READ,DME_OUT,035")    
print("Received from Server: ",data)
a_client.decode_arinc_word(data)
    
data = a_client.DME_Write(b"WRITE,DME_IN,035,0,2,0x0000000")
print("Received from Server: ",data)
time.sleep(1)
data = a_client.DME_Read(b"READ,DME_OUT,035")    
print("Received from Server: ",data)
a_client.decode_arinc_word(data)
   
data = a_client.DME_Write(b"WRITE,DME_IN,035,3,3,0x0000000")
print("Received from Server: ",data)
time.sleep(1)
data = a_client.DME_Read(b"READ,DME_OUT,035")    
print("Received from Server: ",data)
a_client.decode_arinc_word(data)

# SET Channel
# formats channel and sends a write message 
print("Writitng to Set Channel to 111.90 MHz")
a_client.writeChannel(111.90)
time.sleep(1)


print("Writitng to Set Channel to 117.95 MHz")
a_client.writeChannel(117.95)
time.sleep(1)



#------------------------------------------------------------------------------
#TCAS Commands
#------------------------------------------------------------------------------

#Read the Number of TCAS Intruders
intr = a_client.TCAS_Read(b"READ,TCAS_OUT,INT")
print("Number of Intruders",intr)

#Get a List of All TCAS Intruders
data = a_client.TCAS_Read(b"READ,TCAS_OUT,ALL")
print("Intruder Return String: ",data)
#Decode the Return string into separate lists
itr,rng,alt,brg = a_client.TCAS_Decode(data)
print ("Intruders: ",itr)
print ("Range: ",rng)
print ("Altitude: ",alt)
print ("Bearing: ",brg)





###############################################################################

#Close the Connection
#note this step is only for shutdowns
a_client.close_ARINC_Client()
print("Connection to Server Closed.")

#Kill the Server if you want to (its killed if it running on startup)
#note this step is only for shutdowns.
#a_client.kill_ARINC_Server()
#print("Server Killed ... done!")



