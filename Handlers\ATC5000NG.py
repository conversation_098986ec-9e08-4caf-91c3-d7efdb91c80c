# -*- coding: utf-8 -*-
"""
Created on 3/31/2020

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
DESCRIPTION:
ATC5000ng limited function and attribute class.

INPUTS: VISA Resource Manager obj (rm)
OUTPUTS: N/A

HISTORY:
    
04/21/2020  MRS  Changed generic write/query to gwrite/gquery,
                 Added Transponder Utility Methods (cable_loss, 
                 aircraft_pos, waitforstatus and various XPDR Mode
                 commands)
04/29/2020  MRS  Move import for Honeywell Lobster Observer Client
                 interface inside this module.
                 Added Data Logging Routines.
05/21/2020  MRS  Added methond to get reply delay 'getReplyDelay'

05/29/2020  KIF  Added DME Initization Functions
06/02/2020  MRS  Add Query Command for Pulse Width, updates to all Query 
                 commands. Added time delays to DME commands.
08/05/2020  MRS  Converted from pyvisa to sockets
08/06/2020  AKS  Changed Default for pulse % reply to -1
                 Eliminated print statements from data logging
01/05/2021  MRS  Replaced 'print' commands with logmessage commands. 
01/29/2021  MRS  Added ModeA Reply Request.
03/10/2021  MRS  Changed default for ReplyDelay to -1.0 for error checking.
11/2022     CS Edited for TXD Pre-Qual
10/2023     CS Added getPulsePosition, getRiseTime, and getFallTime functions
"""

import time
import socket


class ATC5000NG():
    def __init__(self, ate_rm):
        
        self.resourceManager = ate_rm
 
        #Connection to the ATC
        HOST = '*************'  # Instrument Address
        PORT = 2001              # Port to listen on (non-privileged ports are > 1023)
		#Create a Socket
        try:
            self.atc = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.resourceManager.logMessage(1,"Socket successfully created")
        except socket.error as err: 
            print ("socket creation failed with error %s" %(err))
            self.resourceManager.logMessage(3,"Socket Creation Failed")
            return
      
        self.atc.settimeout(1) #set the socket to timeout after 1 second
        
		#Connect to ATC
        try:
            self.atc.connect((HOST, PORT))
            self.resourceManager.logMessage(1,"ATC5000 Connection Success")
        except socket.error as err:
            print ("socket connection failed with error %s" %(err))
            self.resourceManager.logMessage(3,"Failed to connect to resource")
            self.atc.close()
            return

    def reconnect_socket(self):
        #Connection to the ATC
        HOST = '*************'  # Instrument Address
        PORT = 2001              # Port to listen on (non-privileged ports are > 1023)
		#Create a Socket
        try:
            self.atc = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.resourceManager.logMessage(1,"Socket successfully recreated")
        except socket.error as err: 
            self.resourceManager.logMessage(3,"Socket Creation Failed with error %s" %(err))
            return
      
        self.atc.settimeout(1) #set the socket to timeout after 1 second
        
		#Connect to ATC
        try:
            self.atc.connect((HOST, PORT))
            self.resourceManager.logMessage(1,"ATC5000 Connection Success")
        except socket.error as err:
            print ("socket connection failed with error %s" %(err))
            self.resourceManager.logMessage(3,"Failed to connect to resource")
            self.atc.close()
            return

        return


##################################################
# Basic Commands
##################################################
    def basicTvlMsg(self, tvlTxt):
        self.resourceManager.logMessage(1, "TXD Python Lib: atc.py", tvlTxt)
        
    def write(self,message):
        """ This function is used to send message to host (TTG/ATC/RGS). """    
        #add terminiation
        message = message + "\r"    #\n
        self.atc.sendall(bytes(message, encoding = "utf8"))
        self.resourceManager.logMessage(1,message)

    def read(self,message):
        """ This function is used to read from the Host, returns string. """        
        data = self.atc.recv(256)    
        print ("Message from target: %s" %data)
        #convert bytes to back to string
        data_s = data.decode('utf-8')
        data_s = data_s.strip()   
        self.resourceManager.logMessage(1,data_s)        
        return data_s

    def query(self,message):
        """ This function is used to query the Host, returns string. """        
        #add termination
        message = message + "\r"
        #self.atc.write(message)
        None_if_successful = self.atc.sendall(bytes(message, encoding = "utf-8"))
        self.resourceManager.logMessage(1,"Sendall error message (query func): "+str(None_if_successful))    

        data = self.atc.recv(256)
        self.resourceManager.logMessage(1,"Received data from ATC with cmd **" +str(message)+"** : "+ str(data)) 
        #convert bytes to back to string
        data_s = data.decode('utf-8')
        data_s = data_s.strip()       
        #self.resourceManager.logMessage(1, "Is query working?: " + str(message)) 
        #self.resourceManager.logMessage(1,"Message from Query: "+str(data_s))        
        return data_s 

    def percentquery(self,message):
        """ This function is used to query the Host, returns string. """        
        #add termination
        message = message + "\r"
        None_if_successful = self.atc.sendall(bytes(message, encoding = "utf-8"))
        self.resourceManager.logMessage(1,"Sendall error message (query func): "+str(None_if_successful))
        time.sleep(1)
        data = self.atc.recv(256)
        time.sleep(1)
        self.resourceManager.logMessage(1,"Received data from ATC with cmd **" +str(message)+"** : "+ str(data))
        #convert bytes to back to string
        data_s = data.decode('utf-8')
        data_s = data_s.strip()       
        #self.resourceManager.logMessage(1,"Message from Query: "+str(data_s))        
        return data_s

    def pulsequery(self, message):
        """ This function is used to query the Host, returns string. """        
        #add termination
        message = message + "\r"
        message.strip()
        self.resourceManager.logMessage(1,"Message cmd to be sent to ATC: "+str(message))
        None_if_successful = self.atc.sendall(bytes(message, encoding = "utf-8"))
        #self.atc.write(message)
        self.resourceManager.logMessage(1,"Sendall error message (pulse query func). None means no Error: "+str(None_if_successful))

        data = self.atc.recv(1048576)
        self.resourceManager.logMessage(1,"Data from ATC with cmd **" + str(message) + "**: "+ str(data))
        #convert bytes to back to string
        data_s = data.decode('utf-8')
        data_s = data_s.strip()
        self.resourceManager.logMessage(1,"Data from ATC, after decoding and stripping (in pulse query func): "+str(data_s))        
        return data_s
    
    def delayquery(self, message):
        """ This function is used to query the Host, returns string. """        
        #add termination
        message = message + "\r"
        message.strip()
        self.resourceManager.logMessage(1,"Message cmd to be sent to ATC: "+str(message))
        None_if_successful = self.atc.sendall(bytes(message, encoding = "utf-8"))
        #self.atc.write(message)
        self.resourceManager.logMessage(1,"Sendall error message (pulse query func). None means no Error: "+str(None_if_successful))

        data = self.atc.recv(1048576)
        self.resourceManager.logMessage(1,"Data from ATC with cmd **" + str(message) + "**: "+ str(data))
        #convert bytes to back to string
        data_s = data.decode('utf-8')
        data_s = data_s.strip()
        self.resourceManager.logMessage(1,"Data from ATC, after decoding and stripping (in pulse query func): "+str(data_s))        
        return data_s
		
    def close(self):
        """ Closes the equipment connection """
        self.write(":ATC:DME:STOP")
        time.sleep(3)
        self.atc.close()

    def Ident(self):
        """ Returns the Scope identification string. """
        return self.query("*IDN?")

    def Reset(self):
        """ Resets the equipment """
        self.write(":ATC:RESET")
        time.sleep(15)     #may not be enough!
        self.resourceManager.logMessage(1,"Reset Complete\n")
    
    
    def atcStatus(self):
        """ Returns the equipment status. * indicates equipment is ready for next command. """
        return self.query(":ATC:STATUS?")
    
    def gwrite(self,str):
        """ Generic Write, input command string. """
        self.resourceManager.logMessage(1,"Cmd: "+ str)
        self.write(str)
        
    def gquery(self,str):
        """ Generic Query, input command string. """
        return self.query(str)

##################################################
# Transponder Mode Commands
##################################################

    def init_own_aircraft_pos(self):
        """ Set Lat/Lon/Hdg/Alt and Xpdr Address for own aircraft. """
        self.write(":ATC:OWN:LAT 48.834340")
        self.write(":ATC:OWN:LONG -122.214200")
        self.write(":ATC:OWN:HEAD 0")
        self.write(":ATC:OWN:ALT 12000")
        self.write(":ATC:OWN:MSADDR 4")
        self.resourceManager.logMessage(0,"ATC Own Aircraft Set")
        time.sleep(3)
        
    def set_cable_loss(self,top_loss, bot_loss):
        """ Set the ATC Cable Loss for Top/Bottm Antennas. """
        cmd = ':ATC:XPDR:CABLOS ' + top_loss
        self.write(cmd)
        cmd = ':ATC:XPDR:CABLOSBOT ' + bot_loss
        self.write(cmd)
        
    def waitforstatus(self):
        """ Wait for Valid Status. """
        STATUS = self.query(":ATC:STATUS?")
        while STATUS != '20':
            STATUS = self.query(":ATC:STATUS?")
            self.resourceManager.logMessage(1,"STATUS: " + STATUS)        
            time.sleep(1)
    
    def getPercentReply(self, repeat_count):
        """ Get the % Replies: 'repeat_count' as int, specifies the
        number of repeated attempts.  Returns %replies for ATCRBS 
        Top/Bot and ModeS Top/Bot"""
        
        replyrate = [-1.0,-1.0,-1.0,-1.0]
        data = [0.0,0.0,0.0,0.0]
        success = 0
        
        for r in range(repeat_count):
            try:
                line = self.percentquery(":ATC:XPDR:PREP?")
                time.sleep(1)
                self.resourceManager.logMessage(2,"data on line variable: " + line) 
                success = 1
                break

            except socket.timeout: #fail after 1 second
                line = "Percent Reply"
                print("Percent Reply, Socket Receive Fail")
                self.resourceManager.logMessage(3,"Socket Error Timeout" + line)    
                time.sleep(1)
            
        if (success == 1):
            #Clean up reply line
            line = line.strip()
            data = line.split(',')
            self.resourceManager.logMessage(1,"Reply Rate: " + line)
            self.resourceManager.logMessage(2,"Query data on 'data' variable from ATC (percentReply func) AFTER STRIP/SPLIT: " + str(data))
            self.resourceManager.logMessage(1,"Frequency: " + str(data))        
           
            #Check for Valid Data
            if (self.is_number(data[0])):
                if (len(data) == 1):
                    replyrate[0] = -1.0
                    replyrate[1] = -1.0
                if (len(data) > 1):             #Mode S return
                    self.resourceManager.logMessage(1,"Data values 1: " + str(data[0]))
                    replyrate[0] = float(data[0])    # %Top Mode ATCRBS
                    self.resourceManager.logMessage(1,"Data values 2: " + str(data[1]))
                    replyrate[1] = float(data[1])    # %Top Mode ATCRBS               
                if (len(data) > 2):            #All other Modes
                    self.resourceManager.logMessage(1,"Data values 3: " + str(data[2]))
                    replyrate[2] = float(data[2])    # %Top Mode ATCRBS
                    self.resourceManager.logMessage(1,"Data values 4: " + str(data[3]))
                    replyrate[3] = float(data[3])    # %Top Mode ATCRBS               
            else:
                #Check for #,?,* ! == not ready!
                if (data[0] == '#'):             
                    self.resourceManager.logMessage(3,"Data Not Ready # ")    
                if (data[0] == '?'):             
                    self.resourceManager.logMessage(3,"Command did not complete ? ")    
                if (data[0] == '*'):             
                    self.resourceManager.logMessage(3,"Command Completed * ")    
                if (data[0] == '!'):             
                    self.resourceManager.logMessage(3,"Invalid Command Syntax ! ")    
        
        return replyrate

    def getPulseFrequency(self, repeat_count):
        """ Get the Pulse Reply Frequency: 'repeat_count' as int, specifies the
        number of repeated attempts.  Returns the Pulse Reply Frequnecy for ATCRBS 
        Top/Bot and ModeS Top/Bot"""
        
        frequency = 0.0
        success = 0
        
        for r in range(repeat_count):
            #line = self.query(":ATC:MEA:FREQ?")
            #is_query_working = self.query(":ATC:STATUS?")
            try:
                #reply, address = self.atc.recvfrom(1024)
                line = self.pulsequery(":ATC:MEA:FREQ?")
                #self.resourceManager.logMessage(2,"Reply: " + str(reply) + "\nAddress: "+str(address)) 
                self.resourceManager.logMessage(2,"Query data on 'line' variable from ATC (pulseQuery func): " + str(line))
                time.sleep(2)
                success = 1
                break

            except socket.timeout: #fail after 1 second
                line = "Pulse Frequency"
                print("Pulse Frequency, Socket Receive Fail")
                self.resourceManager.logMessage(3,"Socket Error Timeout " + line)
                frequency = 0.0    
                time.sleep(1)
        
        if (success == 1):
            #Clean up reply line
            line = line.strip()
            data = line.split('\n')
            self.resourceManager.logMessage(2,"Query data on 'data' variable from ATC (pulseQuery func) AFTER STRIP/SPLIT: " + str(data))
            self.resourceManager.logMessage(1,"Frequency: " + str(data))
            
            #Check for Valid Data
            if (self.is_number(data[0])):
                frequency = float(data[0])
            else:
                #Check for #,?,* ! == not ready!
                if (data[0] == '#'):             
                    self.resourceManager.logMessage(3,"Data Not Ready # ")   
                if (data[0] == '?'):             
                    self.resourceManager.logMessage(3,"Command did not complete ? ")    
                if (data[0] == '*'):             
                    self.resourceManager.logMessage(3,"Command Completed * ")    
                if (data[0] == '!'):             
                    self.resourceManager.logMessage(3,"Invalid Command Syntax ! ")    
        
        return frequency
    
    def getReplyDelay(self,repeat_count):
        """ Get the Pulse Reply Delay: 'repeat_count' as int, specifies the
        number of repeated attempts.  Returns the Pulse Reply Delay."""
        
        delay = -1.0
        success = 0
        
        for r in range(repeat_count):
            try:
                line = self.delayquery(":ATC:XPDR:DREP?")
                time.sleep(1)
                success = 1
                break
            except socket.timeout: #fail after 1 second
                line = "Reply Delay"
                print("Reply Delay, Socket Receive Fail")
                self.resourceManager.logMessage(3,"Socket Error Timeout" + line)    
                time.sleep(1)
        
        if (success == 1):
            #Clean up reply line
            line = line.strip()
            data = line.split(',')
            self.resourceManager.logMessage(1,"Reply Delay: " + line)        
           
            #Check for Valid Data
            if (self.is_number(data[0])):
                delay = float(data[0])
            else:
                #Check for #,?,* ! == not ready!
                if (data[0] == '#'):             
                    self.resourceManager.logMessage(3,"Data Not Ready # ")    
                if (data[0] == '?'):             
                    self.resourceManager.logMessage(3,"Command did not complete ? ")    
                if (data[0] == '*'):             
                    self.resourceManager.logMessage(3,"Command Completed * ")    
                if (data[0] == '!'):             
                    self.resourceManager.logMessage(3,"Invalid Command Syntax ! ")    
       
        return delay
 
    def getPulseWidth(self,repeat_count):
        """ Get the Pulse Width: 'repeat_count' as int, specifies the
        number of repeated attempts.  Returns the Pulse Width."""
        
        width = 0.0
        success = 0
        
        for r in range(repeat_count):
            try:
                line = self.query(":ATC:MEASure:PULSE:WIDTH?")
                time.sleep(1)
                success = 1
                break
            except socket.timeout: #fail after 1 second
                line = "Pulse Width"
                print("Pulse Width, Socket Receive Fail")
                self.resourceManager.logMessage(3,"Socket Error Timeout" + line)    
                time.sleep(1)
                    
        if (success == 1):
            #Clean up reply line
            line = line.strip()
            data = line.split(',')
            self.resourceManager.logMessage(1,"Pulse Width: " + line)        
            
            #Check for Valid Data
            if (self.is_number(data[0])):
                width = float(data[0])
            else:
                #Check for #,?,* ! == not ready!
                if (data[0] == '#'):             
                    self.resourceManager.logMessage(3,"Data Not Ready # ")    
                if (data[0] == '?'):             
                    self.resourceManager.logMessage(3,"Command did not complete ? ")    
                if (data[0] == '*'):             
                    self.resourceManager.logMessage(3,"Command Completed * ")    
                if (data[0] == '!'):             
                    self.resourceManager.logMessage(3,"Invalid Command Syntax ! ")    
        
        return width

    
    def getPulsePosition(self,repeat_count):
        """ Get the Pulse Position: 'repeat_count' as int, specifies the
        number of repeated attempts.  Returns the Pulse Position."""
        
        position = 0.0
        success = 0
        
        for r in range(repeat_count):
            try:
                line = self.query(":ATC:MEASure:PULSE:POS?")
                time.sleep(1)
                success = 1
                break
            except socket.timeout: #fail after 1 second
                line = "Pulse Position"
                print("Pulse Position, Socket Receive Fail")
                self.resourceManager.logMessage(3,"Socket Error Timeout" + line)    
                time.sleep(1)
                    
        if (success == 1):
            #Clean up reply line
            line = line.strip()
            data = line.split(',')
            self.resourceManager.logMessage(1,"Pulse Position: " + line)        
            
            #Check for Valid Data
            if (self.is_number(data[0])):
                position = float(data[0])
            else:
                #Check for #,?,* ! == not ready!
                if (data[0] == '#'):             
                    self.resourceManager.logMessage(3,"Data Not Ready # ")    
                if (data[0] == '?'):             
                    self.resourceManager.logMessage(3,"Command did not complete ? ")    
                if (data[0] == '*'):             
                    self.resourceManager.logMessage(3,"Command Completed * ")    
                if (data[0] == '!'):             
                    self.resourceManager.logMessage(3,"Invalid Command Syntax ! ")    
        
        return position

    def getRiseTime(self,repeat_count):
        """ Get the Pulse Position: 'repeat_count' as int, specifies the
        number of repeated attempts.  Returns the Pulse Position."""
        
        risetime = 0.0
        success = 0
        
        for r in range(repeat_count):
            try:
                line = self.query(":ATC:MEASure:PULSE:RISE?")
                time.sleep(1)
                success = 1
                break
            except socket.timeout: #fail after 1 second
                line = "Rise Time"
                print("Rise Time, Socket Receive Fail")
                self.resourceManager.logMessage(3,"Socket Error Timeout" + line)    
                time.sleep(1)
                    
        if (success == 1):
            #Clean up reply line
            line = line.strip()
            data = line.split(',')
            self.resourceManager.logMessage(1,"Rise Time: " + line)        
            
            #Check for Valid Data
            if (self.is_number(data[0])):
                risetime = float(data[0])
            else:
                #Check for #,?,* ! == not ready!
                if (data[0] == '#'):             
                    self.resourceManager.logMessage(3,"Data Not Ready # ")    
                if (data[0] == '?'):             
                    self.resourceManager.logMessage(3,"Command did not complete ? ")    
                if (data[0] == '*'):             
                    self.resourceManager.logMessage(3,"Command Completed * ")    
                if (data[0] == '!'):             
                    self.resourceManager.logMessage(3,"Invalid Command Syntax ! ")    
        
        return risetime

    def getFallTime(self,repeat_count):
        """ Get the Pulse Position: 'repeat_count' as int, specifies the
        number of repeated attempts.  Returns the Pulse Position."""
        
        falltime = 0.0
        success = 0
        
        for r in range(repeat_count):
            try:
                line = self.query(":ATC:MEASure:PULSE:FALL?")
                time.sleep(1)
                success = 1
                break
            except socket.timeout: #fail after 1 second
                line = "Fall Time"
                print("Fall Time, Socket Receive Fail")
                self.resourceManager.logMessage(3,"Socket Error Timeout" + line)    
                time.sleep(1)
                    
        if (success == 1):
            #Clean up reply line
            line = line.strip()
            data = line.split(',')
            self.resourceManager.logMessage(1,"Fall Time: " + line)        
            
            #Check for Valid Data
            if (self.is_number(data[0])):
                falltime = float(data[0])
            else:
                #Check for #,?,* ! == not ready!
                if (data[0] == '#'):             
                    self.resourceManager.logMessage(3,"Data Not Ready # ")    
                if (data[0] == '?'):             
                    self.resourceManager.logMessage(3,"Command did not complete ? ")    
                if (data[0] == '*'):             
                    self.resourceManager.logMessage(3,"Command Completed * ")    
                if (data[0] == '!'):             
                    self.resourceManager.logMessage(3,"Invalid Command Syntax ! ")    
        
        return falltime
                  
    def getModeAReply(self,repeat_count):
        """ Get the ModeA Reply: 'repeat_count' as int, specifies the
        number of repeated attempts.  Returns the ModeAReply."""
        
        ModeAReply = -1.0
        success = 0
        
        for r in range(repeat_count):
            try:
                line = self.query(":ATC5000NG:XPDR:AREPLY?")
                success = 1
                break
            except socket.timeout: #fail after 1 second
                line = "ModeA Reply"
                print("ModeA Reply, Socket Receive Fail")
                self.resourceManager.logMessage(3,"Socket Error Timeout" + line)    
                time.sleep(1)
                    
        if (success == 1):
            #Clean up reply line
            line = line.strip()
            data = line.split(',')
            self.resourceManager.logMessage(1,"ModeA Reply: " + line)        
            
            #Check for Valid Data
            if (self.is_number(data[0])):
                ModeAReply = float(data[0])
            else:
                #Check for #,?,* ! == not ready!
                if (data[0] == '#'):             
                    self.resourceManager.logMessage(3,"Data Not Ready # ")    
                if (data[0] == '?'):             
                    self.resourceManager.logMessage(3,"Command did not complete ? ")    
                if (data[0] == '*'):             
                    self.resourceManager.logMessage(3,"Command Completed * ")    
                if (data[0] == '!'):             
                    self.resourceManager.logMessage(3,"Invalid Command Syntax ! ")    
        
        return ModeAReply


    def getModeCReply(self,repeat_count):
        """ Get the ModeC Reply: 'repeat_count' as int, specifies the
        number of repeated attempts.  Returns the ModeAReply."""
        
        ModeCReply = -1.0
        success = 0
        
        for r in range(repeat_count):
            try:
                line = self.query(":ATC5000NG:XPDR:CREPLY?")
                success = 1
                break
            except socket.timeout: #fail after 1 second
                line = "ModeC Reply"
                print("ModeC Reply, Socket Receive Fail")
                self.resourceManager.logMessage(3,"Socket Error Timeout" + line)    
                time.sleep(1)
                    
        if (success == 1):
            #Clean up reply line
            line = line.strip()
            data = line.split(',')
            self.resourceManager.logMessage(1,"ModeC Reply: " + line)        
            
            #Check for Valid Data
            if (self.is_number(data[0])):
                ModeCReply = float(data[0])
            else:
                #Check for #,?,* ! == not ready!
                if (data[0] == '#'):             
                    self.resourceManager.logMessage(3,"Data Not Ready # ")    
                if (data[0] == '?'):             
                    self.resourceManager.logMessage(3,"Command did not complete ? ")    
                if (data[0] == '*'):             
                    self.resourceManager.logMessage(3,"Command Completed * ")    
                if (data[0] == '!'):             
                    self.resourceManager.logMessage(3,"Invalid Command Syntax ! ")    
        
        return ModeCReply


    def transponderMode(self):
        """ Set the ATC to Transponder Mode """
        self.write(":ATC:SCE:TYPE XPDR")
        self.write(":ATC:SET:FACT XPDR")
        self.write(":ATC:XPDR:RES")
        self.resourceManager.logMessage(0,"Transponder Mode, Long Wait while reseting")
        time.sleep(5)
        
    def transponderModeA(self):
        """ Set the ATC to Transponder Mode A """ 
        self.write(":ATC:MEA:DFORMAT 2")      #Measurement Mode to Float
        time.sleep(.03)
        self.write(":ATC:XPDR:TYPE 0")        #Interrogation Type (SINGLE)
        time.sleep(.03)
        self.write(":ATC:XPDR:MOD 0")         #Test Mode (MODE A)
        time.sleep(.03)
        self.write(":ATC:XPDR:FREQ 1030.00")  #Frequency
        time.sleep(.03)
        self.write(":ATC:XPDR:PRF 400")       #Pulse Repitition
        time.sleep(.03)
        self.write(":ATC:XPDR:POW -21")       #Power Level
        time.sleep(.03)
        self.write(":ATC:MEA:SET:PUL 0")      #Measure F1 ATCRBS Reply
        #self.write(":ATC:MEA:SET:TRIG:ANT TOP") #Top Antenna
        self.write(":ATC:MEA:SET:TRIG:ANT BOTTOM") #BOTTOM Antenna
        time.sleep(.03)
        self.resourceManager.logMessage(0,"Transponder Mode A")
        time.sleep(3)
        
    def transponderModeA_Only(self):
        """ Set the ATC to Transponder Mode A Only""" 
        self.write(":ATC:MEA:DFORMAT 2")      #Measurement Mode to Float
        self.write(":ATC:XPDR:TYPE 0")        #Interrogation Type (SINGLE)
        self.write(":ATC:XPDR:MOD 2")         #Test Mode (MODE A Only - All Call)
        self.write(":ATC:XPDR:FREQ 1030.00")  #Frequency
        self.write(":ATC:XPDR:PRF 400")       #Pulse Repitition
        self.write(":ATC:XPDR:POW -21")       #Power Level
        self.write(":ATC:MEA:SET:PUL 0")      #Measure F1 ATCRBS Reply
        #self.write(":ATC:MEA:SET:TRIG:ANT TOP") #Top Antenna
        self.write(":ATC:MEA:SET:TRIG:ANT BOTTOM") #BOTTOM Antenna
        self.resourceManager.logMessage(0,"Transponder Mode A Only")
        
    def transponderModeAS(self):
        """ Set the ATC MODE A/MODE S All Call """
        self.write(":ATC:MEA:DFORMAT 2")      #Measurement Mode to Float
        time.sleep(.03)
        self.write(":ATC:XPDR:TYPE 0")        #Interrogation Type (SINGLE)
        time.sleep(.03)
        self.write(":ATC:XPDR:MOD 4")         #Test Mode (ModeA/ModeS All Call)
        time.sleep(.03)
        self.write(":ATC:XPDR:FREQ 1030.00")  #Frequency
        time.sleep(.03)
        self.write(':ATC:XPDR:POW -21')       #Power
        time.sleep(.03)
        self.write(":ATC:XPDR:PRF 50")        #Pulse Repitition
        time.sleep(.03)
        self.write(":ATC:MEA:SET:PUL 14")     #Measure P1 Mode S Reply
        time.sleep(.03)
        #self.write(":ATC:MEA:SET:TRIG:ANT TOP") #Top Antenna
        time.sleep(.03)
        self.write(":ATC:MEA:SET:TRIG:ANT BOTTOM") #BOTTOM Antenna
        self.resourceManager.logMessage(0,"Transponder Mode AS")
        time.sleep(3)
                    
    def transponderModeC(self):
        """ Set the ATC to Transponder Mode C """ 
        self.write(":ATC:MEA:DFORMAT 2")      #Measurement Mode to Float
        self.write(":ATC:XPDR:TYPE 0")        #Interrogation Type (SINGLE)
        self.write(":ATC:XPDR:MOD 1")         #Test Mode (MODE C)
        self.write(":ATC:XPDR:FREQ 1030.00")  #Frequency
        self.write(":ATC:XPDR:PRF 100")       #Pulse Repitition
        self.write(":ATC:XPDR:POW -21")       #Power Level
        self.write(":ATC:MEA:SET:PUL 0")      #Measure F1 ATCRBS reply
        #self.write(":ATC:MEA:SET:TRIG:ANT TOP") #Top Antenna
        self.write(":ATC:MEA:SET:TRIG:ANT BOTTOM") #BOTTOM Antenna
        self.resourceManager.logMessage(0,"Transponder Mode C")
        
    def transponderModeC_Only(self):
        """ Set the ATC to Transponder Mode C """ 
        self.write(":ATC:MEA:DFORMAT 2")      #Measurement Mode to Float
        self.write(":ATC:XPDR:TYPE 0")        #Interrogation Type (SINGLE)
        self.write(":ATC:XPDR:MOD 3")         #Test Mode (MODE C Only - All Call)
        self.write(":ATC:XPDR:FREQ 1030.00")  #Frequency
        self.write(":ATC:XPDR:PRF 100")       #Pulse Repitition
        self.write(":ATC:XPDR:POW -21")       #Power Level
        self.write(":ATC:MEA:SET:PUL 1")      #Measure C1 ATCRBS reply
        #self.write(":ATC:MEA:SET:TRIG:ANT TOP") #Top Antenna
        self.write(":ATC:MEA:SET:TRIG:ANT BOTTOM") #BOTTOM Antenna
        self.resourceManager.logMessage(0,"Transponder Mode C Only")

    def transponderModeCS(self):
        """ Set the ATC to Transponder ModeC/ModeS All Call """ 
        self.write(":ATC:MEA:DFORMAT 2")      #Measurement Mode to Float
        self.write(":ATC:XPDR:TYPE 0")        #Interrogation Type (SINGLE)
        self.write(":ATC:XPDR:MOD 5")         #Test Mode (MODEC/ModeS - All Call)
        self.write(":ATC:XPDR:FREQ 1030.00")  #Frequency
        self.write(":ATC:XPDR:PRF 50")        #Pulse Repitition
        self.write(":ATC:XPDR:POW -21")       #Power Level
        self.write(":ATC:MEA:SET:PUL 1")      #Measure C1 ATCRBS reply
        #self.write(":ATC:MEA:SET:TRIG:ANT TOP") #Top Antenna
        self.write(":ATC:MEA:SET:TRIG:ANT BOTTOM") #BOTTOM Antenna
        self.resourceManager.logMessage(0,"Transponder Mode CS")

    def transponderModeS(self):
        """ Set the ATC to Transponder Mode S """ 
        self.write(":ATC:MEA:DFORMAT 2")      #Measurement Mode to Float
        time.sleep(.03)
        self.write(":ATC:XPDR:TYPE 0")        #Interrogation Type (SINGLE)
        time.sleep(.03)
        self.write(":ATC:XPDR:MOD 6")         #Test Mode (MODE S)
        time.sleep(.03)
        self.write(":ATC:XPDR:FREQ 1030.00")  #Frequency
        time.sleep(.03)
        self.write(":ATC:XPDR:PRF 50")        #Pulse Repitition
        time.sleep(.03)
        self.write(":ATC:XPDR:POW -21")       #Power Level
        time.sleep(.03)
        self.write(":ATC:MEA:SET:PUL 14")      #Measure P1 Mode S reply
        #self.write(":ATC:MEA:SET:TRIG:ANT TOP") #Top Antenna
        self.write(":ATC:MEA:SET:TRIG:ANT BOTTOM") #BOTTOM Antenna
        time.sleep(.03)
        self.resourceManager.logMessage(0,"Transponder Mode S")
        
##################################################
# Data Logging Commands/Utilities
##################################################
    def is_number(self,s):
        """ Check for Valid Decimal Number, input number 's' as string,
        returns true/false"""
        try:
            float(s)
            return True
        except ValueError:
            self.resourceManager.logMessage(3,"Not a Number: " + s)    
            pass
     
        try:
            import unicodedata
            unicodedata.numeric(s)
            return True
        except (TypeError, ValueError):
            self.resourceManager.logMessage(3,"Not a Number: " + s)    
            pass
     
        return False
    
    def hex2bin(self,data, charCount):
        """ Convert Ascii Hex to Binary.  Input Ascii Hex string 'data'
        and charCount as integer for the number of characters in the 
        string. """
    
        padded = "{d:0<{c}}".format(d = data, c = charCount)
        binary = "".join(("{0:0>4b}".format(int(x, 16)) for x in padded))
        return binary
    
    
    def data_log_start(self):
        """ Start data logging """
        self.write(":ATC:RCV:LOG:CLE")   #Clear old data
        self.write(":ATC:RCV:MA 0F")     #Record only UUT Messages
        self.write(":ATC:RCV:REC ON")    #Turn on Recording
        self.write(":ATC:RCV:LOG:CLE")
        time.sleep(4)
        
        #Show Mask and Status
        #l1 = self.query(":ATC:RCV:MA?")    
        #l2 = self.query(":ATC:RCV:REC?")
        #l3 = 'Mask: ' + l1 + ' State: ' + l2
        #print (l3)
        self.resourceManager.logMessage(0,"ATC Data Recoding ON - Start")
        
    def data_log_decode(self,line):
        """ Partially Decodes The Data Line. 
        Temp routine for now"""
    
        #first byte
        byte1 = line[0:2]
        if (byte1 == '01'):
            line = line + " : " + 'ModeS Reply'
        if (byte1 == '02'):
            line = line + " : " + 'ATCRBS Reply'
        if (byte1 == '03'):
            line = line + " : " + 'ModeS Inter'
        if (byte1 == '04'):
            line = line + " : " + 'ATCRBS Inter'
        if (byte1 == '05'):
            line = line + " : " + 'ATC ModeS Reply'
        if (byte1 == '06'):
            line = line + " : " + 'ATC ATCRBS Reply'
        if (byte1 == '07'):
            line = line + " : " + 'ATC Mode S Inter'
        if (byte1 == '08'):
            line = line + " : " + 'ATC ATCRBS Inter'
        
        #last 5 bytes, timestamp
        byte40_50 = line[40:50]
        i = int(byte40_50,16)      #base 16 not 16 chars
        line = line + ' :time ' + str(i)
        
        return line
    
        
    def data_log_stop(self,fname):
        """ Stop data logging and download data, input FileName as string.
        Note this takes 1 second per record, so limit the time/records (with a MASK)
        you are recording or you'll be downloading forever (i.e. 300 records = 5min)."""
        
        self.write(":ATC:RCV:REC OFF")
        time.sleep(2)
        self.resourceManager.logMessage(0,"ATC Data Recoding OFF, Downloading...")

        """
        LineCnt = self.query(":ATC:RCV:CO?") 
        LineCnt = self.query(":ATC:RCV:CO?") 
        # MsgCnt =  self.query(":ATC:RCV:MTCO?")
        # MsgCnt =  self.query(":ATC:RCV:MTCO?")
        MsgCnt = ""
        # Status =  self.query(":ATC:RCV:ST?")
        # Status =  self.query(":ATC:RCV:ST?")
        Status = ""
        l3 = "LineCnt: " + LineCnt + " MsgCnt: " + MsgCnt + " Status: " + Status
        # print (l3)
        self.resourceManager.logMessage("0",0,"ATC Data Recoding OFF, Downloading...",l3)
        """
        
        fout = open(fname,"w")
        
        rec = self.query(":ATC:RCV:CO?")
        
        count = 0
        while(not self.is_number(rec) and count < 10):
            rec = self.query(":ATC:RCV:CO?")
            count = count + 1
            
        if (self.is_number(rec)):
            rec = int(rec)
            print("Download Lines = ", rec)
            for x in range(rec):
                line = self.query(":ATC:RCV:LOG:DL?")
                l1 = line.strip()
                #l1 = self.data_log_decode(l1)
                print("Line: %d : %s" % (x,l1))
                time.sleep(.1)
                fout.write(line+'\r')
            
        fout.close()    
        
        self.write(":ATC:RCV:REC OFF")
        time.sleep(1)
        self.resourceManager.logMessage(0,"ATC Data Downloading Complete")
        


##################################################
# UAT Mode Commands
##################################################
        
    def UATMode(self):
        self.resourceManager.logMessage(1,"UAT Mode Initialized")
        self.write(":ATC:SCE:TYPE UAT")
        self.write(":ATC:SCE:CHANNEL UATRX1")
        self.write(":ATC:OWN:LAT 48.834340")
        self.write(":ATC:OWN:LONG -122.214200")
        self.write(":ATC:OWN:HEAD 0")
        self.write(":ATC:OWN:ALT 12000")
        self.write(":ATC:OWN:MSADDR 4")
        #Scenario Definition Example
        self.write(":ATC:SCE:RESET")
        self.write(":ATC:SCE:TIME 3000")
        self.write(":ATC:SCE:STATIC:QUANTITY 2,0")  #both targest on RX1
        #atc.write(":ATC:SCE:DYNAMIC:QUANTITY 2,2")
        self.write(":ATC:SCE:UTCGPS OFF")
        self.write(":ATC:SCE:CAPTURE ON")
        self.write(":ATC:RCV:MASK F00")

        self.write(":ATC:SCE:STATIC:1:PLCODE 3")
        self.write(":ATC:SCE:STATIC:1:ADDRQ 0")
        self.write(":ATC:SCE:STATIC:1:AVSIZE 0")
        self.write(":ATC:SCE:STATIC:1:AGSTATE 0")
        self.write(":ATC:SCE:STATIC:1:ALTTYPE 0")
        self.write(":ATC:SCE:STATIC:1:UAT:GPSLAT 0")
        self.write(":ATC:SCE:STATIC:1:UAT:GPSLONG 0")
        self.write(":ATC:SCE:STATIC:1:MSO 752")
        self.write(":ATC:SCE:STATIC:1:NIC 0")
        self.write(":ATC:SCE:STATIC:1:OFFSET 0")
        self.write(":ATC:SCE:STATIC:1:OFFMANUAL OFF")
        self.write(":ATC:SCE:STATIC:1:TAH 0")
        self.write(":ATC:SCE:STATIC:1:VVSOURCE 0")
        self.write(":ATC:SCE:STATIC:1:UPLINK 0")
        self.write(":ATC:SCE:STATIC:1:UTC ON")
        self.write(":ATC:SCE:STATIC:1:PLMS FFFF")
        self.write(":ATC:SCE:STATIC:1:PLTS FFFF") 

##################################################
# DME Mode Commands
##################################################
    def DMEMode(self):
        """ Initializes the ATC5000 into DME Mode with VOR Pair 0 at 134.4MHz """

         #Initialize ATC to DME mode
        self.write(":ATC:SCE:TYPE DME")
        self.write(":ATC:SET:FACT DME")
        self.write(":ATC:DME:RES")
        self.resourceManager.logMessage(1,"DME Mode, Long Wait while reseting")
        time.sleep(5)
        self.write(":ATC:OWN:LAT 48.834340")
        self.write(":ATC:OWN:LONG -122.214200")
        self.write(":ATC:OWN:HEAD 0")
        self.write(":ATC:OWN:ALT 12000")
        self.write(":ATC:OWN:MSADDR 4")
        #Set Channel into DME  mode with VOR Pair 0
        self.write(":ATC:DME:CHANNEL:MODE 0VOR")
        time.sleep(1)
        self.write(":ATC:DME:CHANNEL 134.40")
        time.sleep(1)
        #Set Range to 34NMi
        self.write(":ATC:DME:RANGE 34") 
        time.sleep(1)
        #Set Squitter Rate to 2700pps
        self.write(":ATC:DME:SQUIT 2700") 
        time.sleep(1)
        cmd = ':ATC:DME:POWER -70' 
        self.write(cmd)
        time.sleep(1)
        self.write(":ATC:DME:START")

    def DMEMode2(self, channel, DME_range):
        """ Initializes the ATC5000 into DME Mode with VOR Pair 0 at a channel and range passed in """

         #Initialize ATC to DME mode
        self.write(":ATC:SCE:TYPE DME")
        time.sleep(.03)
        self.write(":ATC:SET:FACT DME")
        #self.write(":ATC:DME:RES")
        self.resourceManager.logMessage(1,"DME Mode, resetting")
        time.sleep(5)
        self.write(":ATC:OWN:LAT 48.834340")
        time.sleep(.03)
        self.write(":ATC:OWN:LONG -122.214200")
        time.sleep(.03)
        self.write(":ATC:OWN:HEAD 0")
        time.sleep(.03)
        self.write(":ATC:OWN:ALT 12000")
        time.sleep(.03)
        self.write(":ATC:OWN:MSADDR 4")
        time.sleep(.03)
        self.write(":ATC:DME:CHANNEL:MODE 0VOR")
        time.sleep(.03)
        #Set Channel from passed in parameter: channel
        self.write(":ATC:DME:CHANNEL "+ str(channel))
        time.sleep(.03)
        #Set Range to passed in parameter: DME_range
        self.write(":ATC:DME:RANGE " + str(DME_range)) 
        time.sleep(.03)
        self.write(":ATC:DME:SQUIT 2700") 
        time.sleep(.03)
        self.write(':ATC:DME:POWER -70')
        time.sleep(.03)
        self.write(":ATC:DME:START")
        time.sleep(12)                    # Whenever you start DME, be sure to put in at least an 8 sec delay to give ATC time to output


    def set_VOR_PAIR5(self):
        """ This FUnction switched teh DME Channel to VOR Pair 5 at 117.9MHz """
        self.write(":ATC:DME:CHANNEL:MODE 5VOR")
        time.sleep(1)
        self.write(":ATC:DME:CHANNEL 117.9")
        time.sleep(3)
        