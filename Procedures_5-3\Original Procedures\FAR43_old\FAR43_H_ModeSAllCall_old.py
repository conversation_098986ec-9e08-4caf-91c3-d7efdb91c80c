# -*- coding: utf-8 -*-
"""
Created on <PERSON>e Jan  6 08:58:20 2021

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the FAR43 H Mode S All Call requiremensts.
             
            Mode S All-Call Interrogations: Interrogate the Mode S transponder
            with the Mode S-only all-call format UF = 11, and the ATCRBS/Mode
            S all-call formats (1.6 microsecond P4 pulse) and verify that the
            correct address and capability are reported in the replies
            (downlink format DF = 11)

             
INPUTS:      ate_rm,ATC5000NG,RFBOB
OUTPUTS:     DF11_A_Msg    = % Reply
             DF11_C_Msg    = % Reply
             DF11_S_Msg    = % Reply
             DF11_CA       = DF11 CA Field
             DF11_ADRS     = DF11 Adress Field

             FAR43_HOut.log - log file
            
             NOTEs: 
                 1) Interrogations are UF11 messages (with Address = FFFFFF)
                              

HISTORY:
01/06/2021   MRS    Initial Release.

                                 
"""

#Required Libraries
import time
import os

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import RFBOB
from TXDLib.Handlers import ATC_RGS_Log_Decode


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def init_RFBOB(rfbob):
    """ initializes the RF BOB to Prim & Sec to BOTTOM port --- should be default. """
    #rfbob.connect()
    rfbob.setSwitch(0,0)   #Primary Bottom Port
    rfbob.setSwitch(1,0)   #Secondary Bottom Port
    rfbob.setSwitch(10,1)
    #rfbob.disconnect()

def post_process_modes(rm,mode):
    """ This routine post-processes ModeS log files for DF11 messages.
    Returns CA and Adrs fields for DF11 messages. """

    rm.logMessage(1,"PostProcessing ModeS log file.")
    CA = -1
    ADRS = -1
 
    #filename
    fname = 'FAR43_HOut.log'

    #open file
    try:
        fn = open(fname,'r')
    except IOError:
        rm.logMessage(3,"Post Processing Second ModeS File: ERROR File Not Found.")
        return -1
   
    #Read In lines
    idx1  = -1
    idx2  = -1
    for line in fn:
        #print(line)

        #Check line for DFx type
        idx1 = line.find(mode)  
          
        #Check for CA field
        if idx1 > 0:
            #Check for CA field
            idx2 = line.find('CA')
            if idx2 > 0:
                CAs = line[idx2+3:idx2+5]
        
        #Check for Addrs field
        if idx1 > 0:
            #Check for CA field
            idx2 = line.find('Adrs')
            if idx2 > 0:
                Adrs = line[idx2+5:idx2+12]

    #Close the file
    fn.close()

    #Convert strings to ints
    CA = int(CAs)
    ADRS = int(Adrs)

    return CA,ADRS
       
def delete_logfiles(fname):
    """ This routine deletes log files. """

    #delete the log file, if not needed
    #if its deleleted already, not a problem.
    try:
       os.remove(fname)
    except OSError:
        rm.logMessage(3,"Delete First ATCRBS File:File Not Found.")

    return


##############################################################################
################# MAIN     ##################################################
##############################################################################
        
def FAR43_H(rm,atc,rfbob):
    """ FAR43, H - MODE S All Call """
    rm.logMessage(2,"*** FAR43 H, MODE S All Call ***")
    
    #initialize rfbob
    init_RFBOB(rfbob)

    #Initialize Results
    DF11_A_Msg = 0.0   # % Reply
    DF11_C_Msg = 0.0   # % Reply
    DF11_S_Msg = 0.0   # % Reply
    DF11_CA = -1
    DF11_ADRS = -1

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
         
    #Set Up Transponder -- MODE A/S All Call  *** UF11 ****
    atc.transponderModeAS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()                  
            
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    DF11_A_Msg = replyrate[3]                                     #ModeAS Bottom 
    rm.logMessage(2,"DF11 A ReplyRate: %f" % (DF11_A_Msg))         
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Set Up Transponder -- MODE C/S All Call  *** UF11 ****
    atc.transponderModeCS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()                  
            
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    DF11_C_Msg = replyrate[3]                                     #ModeCS Bottom          
    rm.logMessage(2,"DF11 C ReplyRate: %f" % (DF11_C_Msg))         
   
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Set Up Transponder -- MODE S All Call  *** UF11 ****
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF
    atc.gwrite(":ATC:XPDR:PRF 20")       #Pulse Repatition, keep it low to limit log file


    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()   

    #start data logging
    atc.data_log_start()
    time.sleep(5)
            
    #get reply rate
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
            
    DF11_S_Msg = replyrate[1]                                     #ModeS Bottom          
    rm.logMessage(2,"DF11 S ReplyRate: %f" % (DF11_S_Msg))         

    #stop recording and download data, log file generated for verification.
    atc.data_log_stop("FAR43_H.log")
    time.sleep(1)
 
    #decode the data
    ATC_RGS_Log_Decode.main("FAR43_H.log")
    time.sleep(2)
                    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Get Sqitter Rate,CA and Address of DF11 Message
    DF11_CA,DF11_ADRS = post_process_modes(rm,'DF11')
    
    #CleanUp, delete un-needed log files
    delete_logfiles("FAR43_H.log")

    rm.logMessage(2,"ReplyRates: %f,%f,%f" % (DF11_A_Msg,DF11_C_Msg,DF11_S_Msg))   
    rm.logMessage(2,"DF11 CA, ADRS: %d,%d" % (DF11_CA,DF11_ADRS))   
    rm.logMessage(2,"Done, closing session")

    
    return [DF11_A_Msg,DF11_C_Msg,DF11_S_Msg,DF11_CA,DF11_ADRS]

##############################################################################
#run as main from command line
if __name__ == "__main__":
    
    rm = ate_rm()

    #Initialize the RFBOB
    rf_obj = RFBOB(rm)
    rf_obj.connect()

    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

    
    res = FAR43_H(rm,atc_obj,rf_obj)
    

    atc_obj.close()
    rf_obj.disconnect()





