# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Receiver Characteristics, Section *******, Step 4 (ModC/ModeS All
             Call).
             
             "Interrogate the transponder with a standard Mode C ATCRBS/ModeS All
             Call interrogation at RF Levels at -81 dBm. Determine the Reply
             Ratio."
             
INPUTS:      RM,ATC,PathLoss
OUTPUTS:     'ReplyRatio' reply ratio at -81 dBm

HISTORY:

02/06/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review
02/25/2021   MRS    Updates for new Handlers and Lobster.
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

  
##############################################################################
################# MAIN     ##################################################
##############################################################################
def Test_2_3_2_1_Step4(rm,atc,PathLoss):
    """ DO-181E, Receiver Characteristics, Sect *******, Step 4 """

    rm.logMessage(2,"DO-181E, Receiver Characteristics, Sect *******, Step 4 ***\r\n")
    
    #Initialize Reply Ratio
    ReplyRatio = 0.0                         #Value read by TestStand
   
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
  
    #Set Up Transponder -- MODE C/S
    atc.transponderModeCS()
    atc.gwrite(":ATC:XPDR:PRF 100")    #Pulse Repatition
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    
    #Check the Reply Rate for Mode C/Mode S All-Call at -81 dBm
    P1 = -81.0 - PathLoss
    cmd = ':ATC:XPDR:POW ' + str(P1)
    atc.gwrite(cmd)
    rm.logMessage(0,cmd)
    
    #long wait
    time.sleep(25.0)
    
    atc.waitforstatus()
    rm.logMessage(0,"Test_2_3_2_1_Step4 - Power at -81dBm, Get Reply Rate")    
    
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 times
    count = 0
    while replyrate[3] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    
    #ModeCS Bot
    ReplyRatio = replyrate[3]                      #Mode C/S Bot
                   
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    rm.logMessage(0,"Test_2_3_2_1_Step4 - Done: " + str(ReplyRatio))
    rm.logMessage(2,"Done, closing session")
        
    return ReplyRatio

##############################################################################
#run as main from command line
if __name__ == "__main__":

    #SetUP Resource Manager
    rm = ate_rm()
 
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    
 
    res = Test_2_3_2_1_Step4(rm,atc_obj,-12.0)
    
    
    atc_obj.close()