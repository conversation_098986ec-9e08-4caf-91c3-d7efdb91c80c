# -*- coding: utf-8 -*-
"""
Created on 1/17/2023

@author: h468184
         <PERSON><PERSON><PERSON>
         TED GROUP
         
DESCRIPTION:
HBIT limited function and attribute class.  Provides Connections and Command capibilites for HBIT.

INPUTS: ATE Resource Manager obj (rm), Connection String (connect_str)
OUTPUTS: N/A

HISTORY:
    
01/17/2023  CR  Initial Release
03/23/2023  GMB added a few error exceptions, and a close function
04/27/2024  CS  Edited for TXD-ATR Prequal, fixed function names, added log message if connection fails

"""
import pyvisa


class AST0751A1B():
    def __init__(self, rm):
       # Add conneciton to Observer client, ignore linting errors
        self.resourceManager = rm
        # Attempt connection to resource
        AST0751A1B_IP = "TCPIP0::***********::INSTR" 
        try:
            self.AST0751A1B = self.resourceManager.rm.open_resource(AST0751A1B_IP)
            self.resourceManager.logMessage(0, "Connection Success")
            self.basicWrite('')
        except pyvisa.VisaIOError:
            self.resourceManager.logMessage(3, "Failed to connect to resource")
            raise
        tvlTxt = "Resource Opened."
        self.basicTvlMsg(tvlTxt)

 ##################################################
# Basic Commands
##################################################

    def basicWrite(self, cmd):
        tvlTxt = "> %s" % cmd
        self.basicTvlMsg(tvlTxt)
        resp = self.AST0751A1B.write("%s" % cmd)
        return resp

    def basicQuery(self, cmd,logEnable=True):
        tvlTxt = "> %s" % cmd
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        resp = self.AST0751A1B.query("%s" % cmd).strip()
        tvlTxt = "< %s" % resp
        if logEnable==True:
            self.basicTvlMsg(tvlTxt)
        return resp
    
    def close(self):
        self.AST0751A1B.close()
        tvlTxt = "Resource Closed."
        self.basicTvlMsg(tvlTxt)
    
    def reset(self):
        cmd = "*RST"
        return self.basicWrite(cmd)

    def ident(self):
        cmd = "*IDN?"
        return self.basicQuery(cmd)
    
    def setVoltageLimit(self, volts):
        """ Sets Over Voltage Protection on Power Supply, but it's called Limit Voltage in Asterion documentation"""
        try:
            float(volts)
            self.basicWrite('LIMit:VOLTage ' + str(volts))
        except ValueError as e:
            self.resourceManager.logMessage(3, "Invalid Voltage {}".format(e.args))
   
    def setCurrentLimit(self, curr):
        """ Sets Over current Protection on Power Supply, but it's called Limit Voltage in Asterion documentation"""
        try:
            float(curr)
            self.basicWrite('LIMit:CURRent ' + str(curr))
        except ValueError as e:
            self.resourceManager.logMessage(3, "Invalid Current {}".format(e.args))

    def setFrequencyLimit(self, freq):
        """ Sets Over current Protection on Power Supply, but it's called Limit Voltage in Asterion documentation"""
        try:
            float(freq)
            self.basicWrite('[SOURce:]LIMit:CURRent ' + str(freq))
        except ValueError as e:
            self.resourceManager.logMessage(3, "Invalid Frequency {}".format(e.args))

    def basicTvlMsg(self, tvlTxt):
        self.resourceManager.logMessage(1, tvlTxt)
        return None

    def enableOutput(self):
        cmd = "OUTPut ON"
        return self.basicWrite(cmd)

    def disableOutput(self):
        cmd = "OUTPut OFF"
        return self.basicWrite(cmd)

    def setModeAC(self):
        cmd = "MODE AC"
        return self.basicWrite(cmd)

    def setACVolt(self, lvl):
        cmd = 'VOLTage %d' % lvl
        return self.basicWrite(cmd)

    def setModeDC(self):
        cmd = "MODE DC"
        return self.basicWrite(cmd)

    def setDCVolt(self,lvl):
        cmd = "VOLTage:DC %d" % lvl
        return self.basicWrite(cmd)

    def setFreq(self, freq):
        # in Hz, MIN=16 MAX=1000
        cmd = "FREQuency %d" % freq
        return self.basicWrite(cmd)

    def setCurr(self,curr):
        # in Amps
        cmd = "CURRent %f" % curr
        return self.basicWrite(cmd)
##################################################
# Voltage Commands
##################################################

    def getVoltSource(self):
        """ Returns the set Voltage in Volts """
        return self.basicQuery(':SOUR:VOLT?')

    def getVoltMeasured(self):
        """ Returns the measured Voltage in Volts """
        return self.basicQuery(':MEAS:VOLT?')
##################################################
# Current Commands
##################################################

    def getAmpSource(self):
        """ Returns the set Current in Amps """
        return self.basicQuery(':SOUR:CURR?')

    def getAmpMeasured(self):
        """ Returns the measured Current in Amps """
        return self.basicQuery(':MEAS:CURR?')

    
