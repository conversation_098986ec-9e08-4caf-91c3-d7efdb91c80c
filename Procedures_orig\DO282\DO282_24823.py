# -*- coding: utf-8 -*-
"""
SCRIPT IDENTIFIER:  DO282_24823.py

MODULE HISTORY:
        
AUTHOR: H157797

MODULE DESCRIPTION:
  This script creates the test scenario and sends the commands to RF generator like ATC5000NG 
  for DO-282 Section *******.3 Verification of Receiver Selectivity.
  
DEPENDENCY:
   Python v3.6 or above
   UAT_CONNECTION.py
   FEC.py
NOTE:
   The cable line loss need to be considered. The loss of the message power levels could be set by 
   this script's input argument. By default, it is set to 0db assuming the cable line loss is 0db.
   
   Step 2/Step 4 require manual verification of ADS-B Message that is generated from RF generator is 
   properly displayed on VSA TRACE for ADS-B message Spectrum – with or without Continue Wave as expected.
   
HONEYWELL DIFFERENCE/DEVIATION: N/A

"""

import os
import sys
import time
import datetime
#import argparse
import csv

from TXDLib.Handlers import ate_rm
from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc
from TXDLib.Procedures.DO282 import FEC as fec
from TXDLib.Procedures.DO282 import UAT_LOGGING as ul



#parser = argparse.ArgumentParser()
#parser = argparse.ArgumentParser(description='DO-282 *******.1.1 Test scenario script')
#parser.add_argument("-l", "--lineloss", default=3, type=int, help="Cable Line loss, unit: db")
#args = parser.parse_args()

#Cable line loss by default 3db
CABLE_LINE_LOSS = 3.0
INPUT_FILE_PATH = "DO282_24823_INPUT"
MINIMAL_SIGNAL_LEVEL = -90
MAX_SINGAL_LEVEL = -20


longADSB = ''
fecOfLongAdsb = ''

#Apply Long ADS-B Input Messages to verifiy the receiver selectivity.
#Parameters:
#	test_mode_type_Value:specifies the UAT test mode type.
#	squitterPower: The power level of the squitter messages. 
#	interferencePower: Defines the interference Pulse Power Level.
#	frequencyValue: Defines the carrier frequency.
def test_case(test_mode_type_Value=None,squitterPower=None,interferencePower=None,frequencyValue=None,inputLogFile=None):
    f = open(inputLogFile,'w',encoding='utf-8',newline='')

    #connect to the ATC target
    uc.create_connection()
    
    # set mode to UAT mode
    uc.set_test_mode("UAT")
    
    if test_mode_type_Value=="Receiver_selectivity":
        #initial the UAT scenario
        uc.set_scenario(reset=True, channel=1, run_time=15, test_mode_type=test_mode_type_Value, mso_step=20)
    
        #Set the scenario page for the UAT Receiver Selectivity
        uc.set_receiver_selectivity(power=interferencePower, frequency=frequencyValue)

    elif test_mode_type_Value=="Normal":
         uc.set_scenario(reset=True, channel=1, run_time=15, test_mode_type=test_mode_type_Value, mso_step=20)
    
    
    #200 Long ADS-B Message
    try:
        csvWriter = csv.writer(f)
        csvWriter.writerow(['SYSTEM TIME','MSG ID','MESSAGE'])
        for i in range(200):
            longAdsb = uc.generate_random_uat_msg(fec.LONG_ADSB_MSG_TYPE)
            fecOfLongAdsb = fec.fec_parity(fec.LONG_ADSB_MSG_TYPE, longAdsb)
            csvWriter.writerow([datetime.datetime.now().strftime('%H:%M:%S.%f'),i+1,longAdsb+fecOfLongAdsb])
            uc.set_sta_long_adsb(intruderNumber=i+1, data=longAdsb, fec=fecOfLongAdsb, power=squitterPower)
    finally:
        f.close()
    # Sleep 50 sec for the scenario loading into RF generator
    time.sleep(50)
    uc.scenario_start(start=1)

    #Sleep 30 sec for the scenario loading into RF generator
    time.sleep(30)
    
    uc.close_connection()  

def receiver_selectivity(rm):

    inputFile = os.getcwd()+"\\"+INPUT_FILE_PATH
    ul.mkdir(inputFile)
    caseId = 0

    rm.logMessage(0,"-------------------------Receiver Selectivity Pre-Test Setup-----------------------------")
    rm.logMessage(0,"Running Test Case 1: No Interference")
    rm.logMessage(0,"-----------------------------------------------------------------------------------------")    
    rm.logMessage(0,"		Apply the Desired Message Signal with a signal of –50 ± 0.5 dBm")
    caseId +=1
    inputFilePath = inputFile+'\\CASE_'+str(caseId)+'.csv'
    test_case(test_mode_type_Value="Normal",squitterPower=-50 + CABLE_LINE_LOSS,inputLogFile=inputFilePath)

    rm.logMessage(0,"-------------------------Receiver Selectivity Pre-Test Setup-----------------------------")
    rm.logMessage(0,"Running Test Case 2: With continuous wave")
    rm.logMessage(0,"-----------------------------------------------------------------------------------------")  
    rm.logMessage(0,"     Apply the Desired Message Signal with a signal of –50dbm")
    rm.logMessage(0,"     Vector Signal Analyzer Range / ch[annel] 1 [signal] range is –35 dBm")
    rm.logMessage(0,"		Apply the Desired Message Signal with the Center Frequency: 979MHz")
    caseId +=1
    inputFilePath = inputFile+'\\CASE_'+str(caseId)+'.csv'
    test_case(test_mode_type_Value="Receiver_selectivity",squitterPower=-50 + CABLE_LINE_LOSS,interferencePower=-35,inputLogFile=inputFilePath,frequencyValue=979)

    rm.logMessage(0,"-------------------------Receiver Selectivity Test---------------------------------------")
    rm.logMessage(0,"Apply the Desired Message Signal with the Center Frequency: 979MHz")
    rm.logMessage(0,"Set the UAT test message power level at the ADS-B Receiver input –93 dBm.")
    rm.logMessage(0,"              Selectivity Rejection Ratios          ")
    rm.logMessage(0,"    Center Frequency Offset    CW Interference Level")
    rm.logMessage(0,"             -1.0MHz                   -83dbm       ")
    rm.logMessage(0,"             +1.0MHz                   -78dbm       ")
    rm.logMessage(0,"            +/-2.0MHz                  -43dbm       ")
    rm.logMessage(0,"            +/-10.0MHz                 -33dbm       ")
    rm.logMessage(0,"-----------------------------------------------------------------------------------------") 

    rm.logMessage(0,"Running Test Case 3: Receiver Selectivity Test - ADSB Message: -93dBm; Inteference Pulse: -83dBm, Interference frequency: 977MHz")
    caseId +=1
    inputFilePath = inputFile+'\\CASE_'+str(caseId)+'.csv'
    test_case(test_mode_type_Value="Receiver_selectivity",squitterPower=-93 + CABLE_LINE_LOSS,interferencePower=-83,inputLogFile=inputFilePath,frequencyValue=977)

    rm.logMessage(0,"Running Test Case 4: Receiver Selectivity Test - ADSB Message: -93dBm; Inteference Pulse: -78dBm, Interference frequency: 979MHz")
    caseId +=1
    inputFilePath = inputFile+'\\CASE_'+str(caseId)+'.csv'
    test_case(test_mode_type_Value="Receiver_selectivity",squitterPower=-93 + CABLE_LINE_LOSS,interferencePower=-78,inputLogFile=inputFilePath,frequencyValue=979)
    
    rm.logMessage(0,"Running Test Case 5: Receiver Selectivity Test - ADSB Message: -93dBm; Inteference Pulse: -43dBm, Interference frequency: 976MHz")
    caseId +=1
    inputFilePath = inputFile+'\\CASE_'+str(caseId)+'.csv'
    test_case(test_mode_type_Value="Receiver_selectivity",squitterPower=-93 + CABLE_LINE_LOSS,interferencePower=-43,inputLogFile=inputFilePath,frequencyValue=976)

    rm.logMessage(0,"Running Test Case 6: Receiver Selectivity Test - ADSB Message: -93dBm; Inteference Pulse: -43dBm, Interference frequency: 980MHz")
    caseId +=1
    inputFilePath = inputFile+'\\CASE_'+str(caseId)+'.csv'
    test_case(test_mode_type_Value="Receiver_selectivity",squitterPower=-93 + CABLE_LINE_LOSS,interferencePower=-43,inputLogFile=inputFilePath,frequencyValue=980)

    rm.logMessage(0,"Running Test Case 7: Receiver Selectivity Test - ADSB Message: -93dBm; Inteference Pulse: -33dBm, Interference frequency: 968MHz")
    caseId +=1
    inputFilePath = inputFile+'\\CASE_'+str(caseId)+'.csv'
    test_case(test_mode_type_Value="Receiver_selectivity",squitterPower=-93 + CABLE_LINE_LOSS,interferencePower=-33,inputLogFile=inputFilePath,frequencyValue=968)
    
    rm.logMessage(0,"Running Test Case 8: Receiver Selectivity Test - ADSB Message: -93dBm; Inteference Pulse: -33dBm, Interference frequency: 988MHz")
    caseId +=1
    inputFilePath = inputFile+'\\CASE_'+str(caseId)+'.csv'
    test_case(test_mode_type_Value="Receiver_selectivity",squitterPower=-93 + CABLE_LINE_LOSS,interferencePower=-33,inputLogFile=inputFilePath,frequencyValue=988)
	
	
if __name__ == '__main__':
   
    #SetUP Resource Manager
    rm = ate_rm()


    receiver_selectivity(rm)

	