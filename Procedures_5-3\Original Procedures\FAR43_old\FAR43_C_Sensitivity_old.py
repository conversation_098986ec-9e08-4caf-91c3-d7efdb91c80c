﻿# -*- coding: utf-8 -*-
"""
Created on <PERSON>e Jan  5 08:58:20 2021

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the FAR43 C Receiver Sensitivity requiremensts.
             
    (1) Verify that for any class of ATCRBS Transponder, the receiver minimum triggering 
    level (MTL) of the system is -73 ±4 dbm, or that for any class of Mode S transponder
    the receiver MTL for Mode S format (P6 type) interrogations is -74 ±3 dbm by use of 
    a test set either:

       (i) Connected to the antenna end of the transmission line;

       (ii) Connected to the antenna terminal of the transponder with a correction 
       for transmission line loss; or

       (iii) Utilized radiated signal.

   (2) Verify that the difference in Mode 3/A and Mode C receiver sensitivity does 
   not exceed 1 db for either any class of ATCRBS transponder or any class of Mode S
   transponder.
             
INPUTS:      ate_rm,ATC5000NG,RFBOB,PathLoss
OUTPUTS:     MTL_Power_ModeA -  Power where %replies at 90% or greater for Mode A
             MTL_Power_ModeC -  Power where %replies at 90% or greater for Mode C
             MTL_Power_ModeS -  Power where %replies at 90% or greater for Mode C
             
             NOTE: PathLoss includes RFBOB loss and Cable Loss, this module only uses
                Prim Bottom RFBOB(-10) and Bottom Cable.  Power Values are adjusted 
                per this value (should be around 12dBm).

HISTORY:
01/05/2021   MRS    Initial Release.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import RFBOB
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def init_RFBOB(rfbob):
    """ initializes the RF BOB to Prim & Sec to BOTTOM port --- should be default. """
    #rfbob.connect()
    rfbob.setSwitch(0,0)   #Primary Bottom Port
    rfbob.setSwitch(1,0)   #Secondary Bottom Port
    rfbob.setSwitch(10,1)
    #rfbob.disconnect()

##############################################################################
################# MAIN     ##################################################
##############################################################################
        
def FAR43_C(rm,atc,rfbob,PathLoss):
    """ FAR43, C - Receiver Sensitivity """
    rm.logMessage(2,"*** FAR43 C, Receiver Sensitivity ***")
    
    #initialize rfbob
    init_RFBOB(rfbob)

    #Initialize Power Levels and MTL Results
    Init_PowelLevel = -55.0                    #initial Interrogation Power Level
    MTL_Power_ModeA = 0.0                      #Values read by TestStand
    MTL_Power_ModeC = 0.0                      #Values read by TestStand
    MTL_Power_ModeS = 0.0                      #Values read by TestStand

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(5)
    
    atc.waitforstatus()    

    #decrease power levels unit %replies < 90%
    Power_Level = Init_PowelLevel
    for i in range(0,15):
       
       #get reply rate
       replyrate = atc.getPercentReply(2)
       # fix for erroneous reply rate,try 10 time
       count = 0
       while replyrate[1] == -1.0 and count < 10:
           replyrate = atc.getPercentReply(2)
           count = count + 1
            
       val = replyrate[1]                                     #ATCRBS Bottom
       if val > 89.0:                                         #%replies
            Power_Level = Power_Level - 1.0
            cmd = ':ATC:XPDR:POW ' + str(Power_Level)
            atc.gwrite(cmd)
            time.sleep(2)
            
            msg = "Inside Loop: " + "Pwr: " + str(Power_Level) + "ReplyRate: " + str(val) 
            rm.logMessage(1,msg)  
            
    #Lowest power level for Mode A
    MTL_Power_ModeA = Power_Level
       
    #Turn OFF RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    
    #Set Up Transponder -- MODE C
    atc.transponderModeC()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
   
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(5)

    atc.waitforstatus()            
        
    #decrease power levels unit %replies < 90%
    Power_Level = Init_PowelLevel
    for i in range(0,15):
            
       #get reply rate
       replyrate = atc.getPercentReply(2)
       # fix for erroneous reply rate,try 10 time
       count = 0
       while replyrate[1] == -1.0 and count < 10:
           replyrate = atc.getPercentReply(2)
           count = count + 1
            
       val = replyrate[1]                                     #ATCRBS Bottom
       if val > 89.0:                                         #%replies
            Power_Level = Power_Level - 1.0
            cmd = ':ATC:XPDR:POW ' + str(Power_Level)
            atc.gwrite(cmd)
            time.sleep(2)
            
            msg = "Inside Loop: " + "Pwr: " + str(Power_Level) + "ReplyRate: " + str(val) 
            rm.logMessage(1,msg)  
            
    #Lowest power level for Mode C
    MTL_Power_ModeC = Power_Level
       
    #Turn OFF RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Set Up Transponder -- MODE S
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF

    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   #longer wait here for switch to ModeS

    atc.waitforstatus()            
        
    #decrease power levels unit %replies < 90%
    Power_Level = Init_PowelLevel
    for i in range(0,15):
            
       #get reply rate
       replyrate = atc.getPercentReply(2)
       # fix for erroneous reply rate,try 10 time
       count = 0
       while replyrate[1] == -1.0 and count < 10:
           replyrate = atc.getPercentReply(2)
           count = count + 1
            
       val = replyrate[1]                                     #ModeS Bottom
       if val > 89.0:                                         #%replies
            Power_Level = Power_Level - 1.0
            cmd = ':ATC:XPDR:POW ' + str(Power_Level)
            atc.gwrite(cmd)
            time.sleep(2)
            
            msg = "Inside Loop: " + "Pwr: " + str(Power_Level) + "ReplyRate: " + str(val) 
            rm.logMessage(1,msg)  
            
    #Lowest power level for Mode S
    MTL_Power_ModeS = Power_Level
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Adjust Power Levels for PathLoss
    MTL_Power_ModeA = MTL_Power_ModeA - PathLoss
    MTL_Power_ModeC = MTL_Power_ModeC - PathLoss 
    MTL_Power_ModeS = MTL_Power_ModeS - PathLoss 
   
    rm.logMessage(2,"MTL's: %f,%f,%f" % (MTL_Power_ModeA,MTL_Power_ModeC,MTL_Power_ModeS))   
    rm.logMessage(2,"Done, closing session")

    
    return [MTL_Power_ModeA,MTL_Power_ModeC,MTL_Power_ModeS]

##############################################################################
#run as main from command line
if __name__ == "__main__":
    
    rm = ate_rm()
    
    #Initialize the RFBOB
    rf_obj = RFBOB(rm)
    rf_obj.connect()
   
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    
    
    res = FAR43_C(rm,atc_obj,rf_obj,12.5)
    
    atc_obj.close()
    rf_obj.disconnect()
 
