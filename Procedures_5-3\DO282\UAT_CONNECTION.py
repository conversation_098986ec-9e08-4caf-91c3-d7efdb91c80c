# -*- coding: utf-8 -*-
"""
Copyright:   All source code, and data contained in this document is
Proprietary and Confidential to Honeywell International Inc. and must
not be reproduced, transmitted, or disclosed; in whole or in part,
without the express written permission of Honeywell International Inc.

Created on Tue Mar 10 15:32:53 2020

AUTHOR: E524495, H157797, E669660

MODULE DESCRIPTION:
 The objective of the module is provided an interface between UAT DO-282 MOPS test 
 scripts and the RF Test equipments. 
 
 Note: 
   1. Support Test equipment either ATC5000NG, RGS2000NG or TTG-7000 with UAT function.
   2. The remote command syntax on the ATC5000NG/RGS2000NG/TTG-7000 w/ UAT option only 
      have differential equipment ident as below
      Example:
      ---------------------------------------------------------------------------------|
      |  Equipment |          Command Syntax                                           |  
      ---------------------------------------------------------------------------------|
      | RGS2000NG  |  {:RGS | :RGS2000NG}{:SCE | :SCENARIO}{:TI | :TIME}SP<numeric>CR  |    
      |                 ~~~    ~~~~~~~~~                                               |
      | ATC5000NG  |  {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:TI | :TIME}SP<numeric>CR  |
      |                 ~~~    ~~~~~~~~~                                               |
      | TTG-7000   |  {:TTG | :TTG7000}{:SCE | :SCENARIO}{:TI | :TIME}SP<numeric>CR    |
      |                 ~~~    ~~~~~~~~~                                               |
      ---------------------------------------------------------------------------------|

Available Subroutines:
 create_connection()            : Create a connection to the test equipment per config.ini
 close_connection()             : Close connection
 set_scenario()                 : Set up the scenario on UAT function page
 set_test_mode()                : Set Test Mode (TCAS, XPDR, UAT, MULTI-RECEIVER)
 set_doppler_modulation_freq()  : Set up UAT Doppler Modulation Frequency Test scenario
 set_receiver_selectivity()     : Set up UAT Receiver Selectivity Test scenario
 set_DME_Fruit_12us_spacing()   : Set up UAT DME Fruit 12us spacing Test scenario
 set_DME_Fruit_30us_spacing()   : Set up UAT DME Fruit 30us spacing Test scenario
 set_1090_Pulse_Interference()  : Set up 1090 Pulse Interference Test scenario
 set_Retrigger_Long_ADSB_msg()  : Set up UAT Retrigger Long ADS-B Msg Test scenario
 Set_Retrigger_Long_Ground_Link_Msg(): Set up UAT Retrigger Long Ground Link Msg Test scenario
 set_sta_fisb_matrix_msg()      : Set up the FIS-B Message Matrix Block for Static intruder
 set_sta_long_adsb()            : Set up the Long ADS-B Message Block for Static intruder
 set_sta_basic_adsb()           : Set up the Basic ADS-B Message Block for Static intruder
 set_sta_payload_type_adsb_msg(): Set up the specified payload type ADS-B Message for static intruder.
 generate_random_uat_msg()      : Randomly generate the uat messages(Basic/Long ADSB message, Ground Uplink message)
 set_static_test_mode()         : Set static test mode on UAT function page
 scenario_start()               : Start/Stop the scenario

Internal subroutines:
 connet()                       : Connect the test equipment by input socket IP Address/Port
 send()                         : Send the message via socket

"""

import socket
import configparser
import random
import sys
import os

# Default target RF generator: ATC5000NG
uat_socket = 1
target = ":ATC"

BASIC_ADSB_MSG_TYPE = 1
BASIC_ADSB_DATA_LEN = 18
BASIC_ADSB_FEC_LEN = 12

LONG_ADSB_MSG_TYPE = 2
LONG_ADSB_DATA_LEN = 34
LONG_ADSB_FEC_LEN = 14

GNDUPLINK_MSG_TYPE = 3
GNDUPLINK_DATA_LEN = 72
GNDUPLINK_FEC_LEN = 20

DATA_CONTENT = '0123456789ABCDEF'
logFileName = "ATC5000NG.log"
logEnable = "ON"


#########################################################################################
#   Public Function 
#########################################################################################

# Create connection
def create_connection():
    global target
    #config = configparser.ConfigParser()
    #config.read('TargetConfiguration/config.ini')

    #config.sections()

    try:
        #target = config.get('target', 'name')
        #target = ':' + target
        target = ':ATC'
        #host = config.get('target', 'ip')
        host = '*************'
        #port = config.getint('target', 'port')
        port = 2001
        connet(host, port)
        print("Config Connection Sucessfull")
    except Exception:
        print("Please check the config.ini file to make sure the target exist!")

    # print("Start test case on target%s ip:port == %s:%d" %(target, host, port))


# Close connection
def close_connection():
    global uat_socket
    uat_socket.close()


def set_sta_payload_type_adsb_msg(plcode=None, addrqualifier=None, address=None, mso=None, offset=None, offmanual=None,
                                   power=None, AorGstate=None, aircraftsize=None, altitype=None, vertivalVelocity=None,
                                   altitude=None, latitude=None, utcCoupled=None, longitude=None, verticalspeed=None,
                                   velocity=None, track=None, nic=None, uplinkFeedback=None, intruderNumber=None):
    global target

    # static Basic/Long adsb header
    if intruderNumber == None:
        print("Warning: set the intruder number from 1 to 1500 in the procedure set_sta_payload_type_adsb_msg")
        return
    else:
        if 0 < intruderNumber <= 1500:
            message_header = target + ":SCE:STAT:" + str(intruderNumber)
        else:
            print("The intruder number should be from 1 to 1500, your instruder number is:" + str(intruderNumber))
            return

    # Payload Type Code
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC:}<intruder number>{:
    #                 PLCODE}SP<numeric>CR
    # Description: This command sets the payload type code of the specified intruder. The <intruder number>
    #              specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 14 (decimal ASCII)
    #        Value             Type Code
    #         0     ADS-B Message Payload Type Code 0
    #         1     ADS-B Message Payload Type Code 1
    #         2     ADS-B Message Payload Type Code 2
    #         3     ADS-B Message Payload Type Code 3
    #         4     ADS-B Message Payload Type Code 4
    #         5     ADS-B Message Payload Type Code 5
    #         6     ADS-B Message Payload Type Code 6
    #         7     ADS-B Message Payload Type Code 7
    #         8     ADS-B Message Payload Type Code 8
    #         9     ADS-B Message Payload Type Code 9
    #        10     ADS-B Message Payload Type Code 10
    #        11     Basic ADS-B Message
    #        12     Long ADS-B Message
    #        13     Ground Uplink Message
    #        14     Ground Uplink Matrix Message
    # Example: :ATC:SCE:STAT:1:PLCODE 4\r
    if plcode != None:
        if 1 <= plcode <= 14:
            message = message_header + ":PLCODE " + str(plcode) + "\r\n"
            send(message)
        else:
            print("Please set PLCODE from 0 to 14")

    # Address Qualifier
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{: ADDRQ}SP<numeric>CR
    # Description: This command sets the address qualifier of the specified intruder. The <intruder number>
    #              specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 7 (decimal ASCII)
    #       Value               Address Type
    #         0      ADS-B target with ICAO 24 Bit address
    #         1      ADS-B target with self-assigned temporary address
    #         2      TIS-B or ADS-R target with ICAO 24 Bit address
    #         3      TIS-B target with track file identifier
    #         4      Surface Vehicle
    #         5      Fixed ADS-B Beacon
    #         6      ADS-R target with non-ICAO address
    #         7      Reserved
    # Example: :ATC:SCE:STAT:1:ADDRQ 2\r
    if addrqualifier != None:
        if 0 <= addrqualifier <= 7:
            message = message_header + ":ADDRQ " + str(addrqualifier) + "\r\n"
            send(message)
        else:
            print("Please set Address Qualifier from 0 to 7.")

    # Mode S Address
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{:MSADDR}SP<numeric>CR
    # Description: This command sets the Mode S address.
    # Numeric: 0 to FFFFFF (hexadecimal ASCII)
    # Default: Static starts at 0x000021
    #          Dynamic starts at 0x000001
    # Example: :ATC:SCE:STAT:1:MSADDR 000024\r
    if address != None:
        message = message_header + ":MSADDR " + address + "\r\n"
        send(message)
    else:
        print("Please set Mode S Address from 0 to FFFFFF")

    # MSO
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{: MSO}SP<numeric>CR
    # Description: This command sets the MSO of the specified intruder.
    #             The <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 3951 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:MSO 3\r

    if mso != None:
        if 0 <= mso <= 3951:
            message = message_header + ":MSO " + str(mso) + "\r\n"
            send(message)
        else:
            print("Please set MSO from 0 to 3951")

    # OFFSET MANUAL OVERRIDE
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{:OFFMANUAL}SP{ON | OFF}CR
    # Description: This command enables or disables the manual override of the offset for the specified intruder.
    #             The <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Example: :ATC:SCE:STAT:1:OFFMANUAL ON\r
    if offmanual != None:
        if offmanual == True:
            message = message_header + ":OFFMANUAL ON\r\n"
            send(message)
        elif offmanual == False:
            message = message_header + ":OFFMANUAL OFF\r\n"
            send(message)
        else:
            print("Please set Offset Manual Override True of Flase (1-True, 0-Flase)")

    # Power
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{:SQPWR}SP<numeric>CR
    # Description: This command sets the power level of the squitter messages.
    # Numeric: High Power Mode: 1 to -69 dBm (decimal ASCII)
    #         Low Power Mode: -20 to -90 dBm (decimal ASCII)
    #         UAT -110 to 5 dBm (decimal ASCII)
    # Default: -50 dBm
    #         -20 dBm (UAT)
    # Example: :ATC:SCE:DYN:1:SQPWR -60\r
    # Example: :ATC:SCE:POW HI\r
    # {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:POW | :POWER}SP{HI | LO | VLO}CR
    if power != None:
        if -110 <= power <= 5:
            message = message_header + ":SQPWR " + str(power) + "\r\n"
            send(message)
        else:
            print("Please set power from -110 to 5")

    # Air/Ground State
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{:AGSTATE}SP<numeric>CR
    # Description: This command sets the AG state of the specified intruder.
    #              The <intruder number> specifies the number of the intruder in the channel grouping
    #              specified.
    # Numeric: 0 to 3 (decimal ASCII)
    #          Value    State
    #            0      Subsonic
    #            1      Supersonic
    #            2      Grounded
    #            3      Reserved
    # Example::ATC:SCE:STAT: 1:AGSTATE 2\r
    if AorGstate != None:
        if 0 <= AorGstate <= 3:
            message = message_header + ":AGSTATE " + str(AorGstate) + "\r\n"
            send(message)
        else:
            print("please set Air/Ground State from 0 to 3.")

    # Aircraft Size
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{: AVSIZE}SP<numeric>CR
    # Description: This command sets the A/V size of the specified intruder. The <intruder number> specifies
    #              the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 15 (decimal ASCII)
    # NOTE: This command is valid only for intruders with AG state grounded.
    # Example: :ATC:SCE:STAT:1: AVSIZE 4\r
    if aircraftsize != None:
        if 0 <= aircraftsize <= 15:
            message = message_header + ":AVSIZE " + str(aircraftsize) + "\r\n"
            send(message)
        else:
            print("please set Aircraft Size from 0 to 15.")

    # Altitude Type
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #               <intruder number>{: ALTTYPE}SP<numeric>CR
    # Description: This command sets the altitude type of the specified intruder. The <intruder number>
    #              specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 1 (decimal ASCII)
    #       Value    Altitude Type
    #         0      Pressure Altitude
    #         1      Geometric Altitude
    # Default: 0
    # Example: :ATC:SCE:STAT:1:ALTTYPE 1\r
    if altitype != None:
        if 0 <= altitype <= 1:
            message = message_header + ":ALTTYPE " + str(altitype) + "\r\n"
            send(message)
        else:
            print("please set the Altitude Type either 0 or 1")

    # Vertical Velocity Source
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                <intruder number>{: VVSOURCE}SP<numeric>CR
    # Description: This command sets the VV source of the specified intruder. The <intruder number> specifies
    #              the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 1 (decimal ASCII)
    #       Value   Meaning
    #         0    Vertical Rate information from Geometric Source
    #         1    Vertical Rate information from Barometric Source
    # Example: :ATC:SCE:STAT:1:VVSOURCE 1\r
    if vertivalVelocity != None:
        if 0 <= vertivalVelocity <= 1:
            message = message_header + ":VVSOURCE " + str(vertivalVelocity) + "\r\n"
            send(message)
        else:
            print("please set the Vertical Velocity Source either 0 or 1")

    # Altitude
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{:ALT | :ALTITUDE}SP{NODATA | <numeric>}CR
    # Description: This command sets the selected intruder to the specified altitude.
    #               If altitude data is not available, then use the keyword “NODATA.”
    # Numeric: Altitude Binary Mode: -1000 to 50175 feet in 25 feet resolution (decimal ASCII)
    #          Altitude Gilham Mode: -1000 to 126700 feet in 100 feet resolution (decimal ASCII)
    # Default: 1000 feet
    # Example: :ATC:SCE:DYN:1:ALT 10175\r
    if altitude != None:
        if -1000 <= altitude <= 50175:
            message = message_header + ":ALT " + str(altitude) + "\r\n"
            send(message)
        else:
            print("please set the Altitude from -1000 to 50175 feet in 25 feet resolution")

    # Latitude
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{:LAT | :LATITUDE}SP<numeric>CR
    # Description: This command sets the selected intruder to the specified latitude.
    # Numeric: -90 to 90 degrees (double ASCII)
    # Default: Calculated using the range and bearing and the own aircraft position.
    # Example: :ATC:SCE:DYN:1:LAT -87.2335\r
    if latitude != None:
        if -90 <= latitude <= latitude:
            message = message_header + ":LAT " + str(latitude) + "\r\n"
            send(message)
        else:
            print("Please set the Latitude from -90 to 90 degrees.")

    # UTC Coupled Condition
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                <intruder number>{:UTC}SP{ON | OFF}CR
    # Description: This command enables or disables the UTC coupled condition of the specified intruder. The
    #               <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Example: :ATC:SCE:STAT:1:UTC ON\r
    if utcCoupled != None:
        if utcCoupled=='ON' or utcCoupled =='OFF':
            message = message_header + ":UTC " + utcCoupled + "\r\n"
            send(message)
        else:
            print("Please set the UTC Coupled Condition either ON or OFF.")

    # Longitude
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{:LONG | :LONGITUDE}SP<numeric>CR
    # Description: This command sets the selected intruder to the specified longitude.
    # Numeric: -180 to 180 degrees (double ASCII)
    # Default: Calculated using the range and bearing and the own aircraft position.
    # Example: :ATC:SCE:DYN:1:LONG 23.452\r
    if longitude != None:
        if -180 <= longitude <= 180:
            message = message_header + ":LONG " + str(longitude) + "\r\n"
            send(message)
        else:
            print("Please set the Longitude from -180 to 180 degrees.")

    # Vertical Speed
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{:VERT | :VERTICAL}SP<numeric>CR
    # Description: This command sets the selected intruder vertical speed.
    # Numeric: -32704 to 32704 feet per minute (decimal ASCII)
    # NOTE: The vertical speed information is only used for the velocity squitter of an extended Mode S
    #       when the intruder is a static intruder.
    # Default: 0
    # Example: :ATC:SCE:DYN:1:VERT 100\r
    if verticalspeed != None:
        if -32704 <= verticalspeed <= 32704:
            message = message_header + ":VERT " + str(verticalspeed) + "\r\n"
            send(message)
        else:
            print("please set Vertical Speed from -32704 to 32704 feet per minute.")

    # Velocity
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{:VEL | :VELOCITY}SP{NODATA | <numeric>}CR
    # Description: This command sets the selected intruder to the specified velocity. If velocity data is not
    #              available, then use the keyword “NODATA.” If velocity data is not available then the E/W
    #              velocity, N/S velocity and Ground Speed information is not available.
    # Numeric: 0 to 5782 knots (decimal ASCII)
    # Default: 0
    # Example: :ATC:SCE:DYN:1:VEL 321\r
    if velocity != None:
        if 0 <= velocity <= 5782:
            message = message_header + ":VEL " + str(velocity) + "\r\n"
            send(message)
        else:
            print("please set Velocity from 0 to 5782 knots.")

    # Track
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{:TRA | :TRACK}SP<numeric>CR
    # Description: This command sets the selected intruder track angle.
    # Numeric: -180 to 180 degrees (decimal ASCII)
    #           0 to 360 degrees (decimal ASCII)
    # Default: 0
    # Example: :ATC:SCE:STAT:1:TRA 230\r
    if track != None:
        if -180 <= track <= 180:
            message = message_header + ":TRA " + str(track) + "\r\n"
            send(message)
        else:
            print("please set Track from -180 to 180 degrees.")

    # Navigation Integrity Category
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{: NIC}SP<numeric>CR
    # Description: This command sets the NIC of the specified intruder. The <intruder number> specifies the
    #              number of the intruder in the channel grouping specified.
    # Numeric: 0 to 15 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:NIC 4\r
    if nic != None:
        if 0<=nic<=15:
            message = message_header + ":NIC " + str(nic) + "\r\n"
            send(message)
        else:
            print("please set Navigation Integrity Category from 0 to 15.")

    # Uplink Feedback
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{: UPLINK}SP<numeric>CR
    # Description: This command sets the uplink feedback encoding of the specified intruder. The <intruder
    #               number> specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 7 (decimal ASCII)
    #           Value    Score
    #             0        0
    #             1     1 to 13
    #             2     14 to 21
    #             3     22 to 25
    #             4     26 to 28
    #             5     29 to 30
    #             6       31
    #             7       32
    # Example: :ATC:SCE:STAT:1:UPLINK 2\r
    if uplinkFeedback != None:
        if 0 <= uplinkFeedback <= 7:
            message = message_header + ":UPLINK " + str(uplinkFeedback) + "\r\n"
            send(message)
        else:
            print("please set Uplink Feedback from 0 to 7")
            
    # OFFSET
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{: OFFSET}SP<numeric>CR
    # Description: This command sets the offset or delay of the specified intruder.
    #             The <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 65500 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:OFFSET 7\r
    if offset != None:
        if 0 <= offset <= 65500:
            message = message_header + ":OFFSET " + str(offset) + "\r\n"
            send(message)
        else:
            print("Please set offset from 0 to 65500")


# Send ground uplink matrix message
# data: one block message data of ground uplink which sent to target.
# fec: The FEC Parity per a RS (92, 72) code
# mso: the MSO of the specified intruder
# offset: the offset or delay of the specified intruder
# offmanual: Offset Manual Override, enables or disables the manual override of the offset for the specified intruder
# power: the power level of the squitter messages 
# intruderNumber: the number of intruder
# blockNumber: Gnn Uplink Matrix block number, from 1 to 6
def set_sta_fisb_matrix_msg(data=None, fec=None, mso=None, offset=None, offmanual=None, power=None, intruderNumber=None,
                            blockNumber=None):
    global target

    # ground uplink message header
    if intruderNumber == None:
        print("Warning: set the intruder number from 1 to 1500 in set_sta_fisb_matrix_msg")
        return
    else:
        if 0 < intruderNumber <= 1500:
            message_header = target + ":SCE:STAT:" + str(intruderNumber)
        else:
            print("The intruder number should be from 1 to 1500, your intruder number is:" + str(intruderNumber))
            return

    # sent message type
    message = message_header + ":PLCODE 14\r\n"
    send(message)

    # MSO
    # Command Syntax: {:ATC |:ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{: MSO}SP<numeric>CR
    # Description: This command sets the MSO of the specified intruder.
    #             The <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 3951 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:MSO 3\r

    if mso != None:
        if (0 <= mso <= 3951):
            message = message_header + ":MSO " + str(mso) + "\r\n"
            send(message)
        else:
            print("Please set MSO from 0 to 3951")
    # OFFSET MANUAL OVERRIDE
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{:OFFMANUAL}SP{ON | OFF}CR
    # Description: This command enables or disables the manual override of the offset for the specified intruder.
    #             The <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Example: :ATC:SCE:STAT:1:OFFMANUAL ON\r

    if offmanual != None:
        if (offmanual == True):
            message = message_header + ":OFFMANUAL ON\r\n"
            send(message)
        elif (offmanual == False):
            message = message_header + ":OFFMANUAL OFF\r\n"
            send(message)
        else:
            print("Please set Offset Manual Override True of Flase (1-True, 0-Flase)")

    # POWER
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{:SQPWR}SP<numeric>CR
    # Description: This command sets the power level of the squitter messages.
    # Numeric:   High Power Mode: 1 to -69 dBm (decimal ASCII)
    #           Low Power Mode: -20 to -90 dBm (decimal ASCII)
    #           UAT
    #           -110 to 5 dBm (decimal ASCII)
    # Default:   -50 dBm
    #           -20 dBm (UAT)
    # Example: :ATC:SCE:STAT:1:SQPWR -60\r
    # {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:POW | :POWER}SP{HI | LO | VLO}CR
    # Example: :ATC:SCE:POW HI\r

    if power != None:
        if (-110 <= power <= 5):
            message = message_header + ":SQPWR " + str(power) + "\r\n"
            send(message)
        else:
            print("Please set power from -110 to 5")

    #    if data != None:
    #    # send 6 message blocks
    #        for i in range(6):
    #            message = message_header + ":RSB:" + str(i) + ":PLMSG " + data[i][:144] + "\r\n"
    #            send(message)
    #
    #            message = message_header + ":RSB:" + str(i) + ":PLFEC " + data[i][144:] + "\r\n"
    #            send(message)

    # send  message data for this blocks
    if blockNumber != None:
        if 0 < blockNumber <= 6:
            message_header = message_header + ":RSB:" + str(int(blockNumber))
        else:
            print("The block number is from 1 to 6")
            # the block number is not correct, the data and fec could set
            return
    elif data != None:
        print("The block number need be set for each ground uplink message data block")
        return

    if data != None:
        if len(data) == GNDUPLINK_DATA_LEN * 2:
            for data_char in data:
                if data_char not in DATA_CONTENT:
                    print(str(data_char) + " is not gnduplink:" + str(DATA_CONTENT))
                    return
            message = message_header + ":PLMSG " + data + "\r\n"
            send(message)
        else:
            print("The basic gnduplink data lenth should be:" + str(GNDUPLINK_DATA_LEN))

    if fec != None:
        if len(fec) == GNDUPLINK_FEC_LEN * 2:
            for data_char in fec:
                if data_char not in DATA_CONTENT:
                    print(str(data_char) + " is not FEC for gnduplink:" + str(DATA_CONTENT))
                    return
            message = message_header + ":PLFEC " + fec + "\r\n"
            send(message)
        else:
            print("The FEC for gnduplink data lenth should be:" + str(GNDUPLINK_FEC_LEN))

    # OFFSET
    # Command Syntax: {:ATC|:ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{: OFFSET}SP<numeric>CR
    # Description: This command sets the offset or delay of the specified intruder.
    #             The <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 65500 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:OFFSET 7\r

    if offset != None:
        if (0 <= offset <= 65500):
            message = message_header + ":OFFSET " + str(offset) + "\r\n"
            send(message)
        else:
            print("Please set offset from 0 to 65500")



# send long ADSB message
# data: one block message data of long ADS-B which sent to target.
# fec: The FEC Parity per a RS (48, 34) code
# offset: the offset or delay of the specified intruder
# offmanual: Offset Manual Override, enables or disables the manual override of the offset for the specified intruder
# power: the power level of the squitter messages 
# intruderNumber: the number of intruder
# 
def set_sta_long_adsb(data=None, fec=None, mso=None, offset=None, offmanual=None, power=None, intruderNumber=None):
    global target

    # static long adsb header
    if intruderNumber == None:
        print("Warning: set the intruder number from 1 to 1500 in the procedure set_sta_long_adsb")
        return
    else:
        if 0 < intruderNumber <= 1500:
            message_header = target + ":SCE:STAT:" + str(intruderNumber)
        else:
            print("The intruder number should be from 1 to 1500, your instruder number is:" + str(intruderNumber))
            return

    # sent message type
    message = message_header + ":PLCODE 12\r\n"
    send(message)

    # MSO
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{: MSO}SP<numeric>CR
    # Description: This command sets the MSO of the specified intruder.
    #             The <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 3951 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:MSO 3\r

    if mso != None:
        if (0 <= mso <= 3951):
            message = message_header + ":MSO " + str(mso) + "\r\n"
            send(message)
        else:
            print("Please set MSO from 0 to 3951")

    # OFFSET MANUAL OVERRIDE
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{:OFFMANUAL}SP{ON | OFF}CR
    # Description: This command enables or disables the manual override of the offset for the specified intruder.
    #             The <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Example: :ATC:SCE:STAT:1:OFFMANUAL ON\r
    if offmanual != None:
        if (offmanual == True):
            message = message_header + ":OFFMANUAL ON\r\n"
            send(message)
        elif (offmanual == False):
            message = message_header + ":OFFMANUAL OFF\r\n"
            send(message)
        else:
            print("Please set Offset Manual Override True of Flase (1-True, 0-Flase)")

    # Power
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{:SQPWR}SP<numeric>CR
    # Description: This command sets the power level of the squitter messages.
    # Numeric: High Power Mode: 1 to -69 dBm (decimal ASCII)
    #         Low Power Mode: -20 to -90 dBm (decimal ASCII)
    #         UAT -110 to 5 dBm (decimal ASCII)
    # Default: -50 dBm
    #         -20 dBm (UAT)
    # Example: :ATC:SCE:DYN:1:SQPWR -60\r
    # Example: :ATC:SCE:POW HI\r
    # {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:POW | :POWER}SP{HI | LO | VLO}CR
    if power != None:
        if (-110 <= power <= 5):
            message = message_header + ":SQPWR " + str(power) + "\r\n"
            send(message)
        else:
            print("Please set power from -110 to 5")

    if data != None:
        if len(data) == LONG_ADSB_DATA_LEN * 2:
            for data_char in data:
                if data_char not in DATA_CONTENT:
                    print(str(data_char) + " is not long ADS-B:" + str(DATA_CONTENT))
                    return
            message = message_header + ":PLMSG " + data + "\r\n"
            send(message)
        else:
            print("The long ADS-B data lenth should be:" + str(LONG_ADSB_DATA_LEN))

    if fec != None:
        if len(fec) == LONG_ADSB_FEC_LEN * 2:
            for data_char in fec:
                if data_char not in DATA_CONTENT:
                    print(str(data_char) + " is not FEC for long ADS-B:" + str(DATA_CONTENT))
                    return
            message = message_header + ":PLFEC " + fec + "\r\n"
            send(message)
        else:
            print("The FEC for long ADS-B data lenth should be:" + str(LONG_ADSB_FEC_LEN))
            
    # OFFSET
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{: OFFSET}SP<numeric>CR
    # Description: This command sets the offset or delay of the specified intruder.
    #             The <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 65500 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:OFFSET 7\r

    if offset != None:
        if (0 <= offset <= 65500):
            message = message_header + ":OFFSET " + str(offset) + "\r\n"
            send(message)
        else:
            print("Please set offset from 0 to 65500")


# send Basic ADSB message
# data: message data include the FEC parity
# mso: MSO
# offset
# power: power
# 
def set_sta_basic_adsb(data=None, fec=None, mso=None, offset=None, offmanual=None, power=None, intruderNumber=None):
    global target

    # static basic adsb header

    if intruderNumber == None:
        print("Warning: set the intruder number from 1 to 1500 in the procedure set_sta_basic_adsb")
        return
    else:
        if 0 < intruderNumber <= 1500:
            message_header = target + ":SCE:STAT:" + str(intruderNumber)
        else:
            print("The intruder number should be from 1 to 1500, your instruder number is:" + str(intruderNumber))
            return

    # sent message typE
    message = message_header + ":PLCODE 11\r\n"
    send(message)

    # MSO
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{: MSO}SP<numeric>CR
    # Description: This command sets the MSO of the specified intruder.
    #             The <intruder number> specifies thenumber of the intruder in the channel grouping specified.
    # Numeric: 0 to 3951 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:MSO 3\r

    if mso != None:
        if (0 <= mso <= 3951):
            message = message_header + ":MSO " + str(mso) + "\r\n"
            send(message)
        else:
            print("Please set MSO from 0 to 3951")

    # Offset Manual Override
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{:OFFMANUAL}SP{ON | OFF}CR
    # Description: This command enables or disables the manual override of the offset for the specified intruder.
    #             The <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Example: :ATC:SCE:STAT:1:OFFMANUAL ON\r
    if offmanual != None:
        if (offmanual == True):
            message = message_header + ":OFFMANUAL ON\r\n"
            send(message)
        elif (offmanual == False):
            message = message_header + ":OFFMANUAL OFF\r\n"
            send(message)
        else:
            print("Please set Offset Manual Override True of Flase (1-True, 0-Flase)")

    # POWER
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{:SQPWR}SP<numeric>CR
    # Description: This command sets the power level of the squitter messages.
    # Numeric: High Power Mode: 1 to -69 dBm (decimal ASCII)
    #         Low Power Mode: -20 to -90 dBm (decimal ASCII)
    #         UAT -110 to 5 dBm (decimal ASCII)
    # Default: -50 dBm
    #         -20 dBm (UAT)
    # Example: :ATC:SCE:DYN:1:SQPWR -60\r
    # Example: :ATC:SCE:POW HI\r
    # {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:POW | :POWER}SP{HI | LO | VLO}CR
    if power != None:
        if (-110 <= power <= 5):
            message = message_header + ":SQPWR " + str(power) + "\r\n"
            send(message)
        else:
            print("Please set power from -110 to 5 dBm")

    if data != None:
        if len(data) == BASIC_ADSB_DATA_LEN * 2:
            for data_char in data:
                if data_char not in DATA_CONTENT:
                    print(str(data_char) + " is not basic ADS-B:" + str(DATA_CONTENT))
                    return
            message = message_header + ":PLMSG " + data + "\r\n"
            send(message)
        else:
            print("The basic ADS-B data lenth should be:" + str(BASIC_ADSB_DATA_LEN))

    if fec != None:
        if len(fec) == BASIC_ADSB_FEC_LEN * 2:
            for data_char in fec:
                if data_char not in DATA_CONTENT:
                    print(str(data_char) + " is not FEC for basic ADS-B:" + str(DATA_CONTENT))
                    return
            message = message_header + ":PLFEC " + fec + "\r\n"
            send(message)
        else:
            print("The FEC for basic ADS-B data lenth should be:" + str(BASIC_ADSB_FEC_LEN))

    # OFFSET
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{: OFFSET}SP<numeric>CR
    # Description: This command sets the offset or delay of the specified intruder.
    #             The <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 65500 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:OFFSET 7\r

    if offset != None:
        if (0 <= offset <= 65500):
            message = message_header + ":OFFSET " + str(offset) + "\r\n"
            send(message)
        else:
            print("Please set offset from 0 to 65500")


# Set the scenario page for the UAT
#   reset: if this vaule is true, resets the Scenario Menu to no active intruder, interrogator (ground station)and video data block.
#   run_time: the total scenario time
#   channel: channel commands that are used to define UAT scenario.
#   dynamic_quantity_rx1: dynamic intruder quantity on rx1
#   dynamic_quantity_rx2: dynamic intruder quantity on rx2
#   static_quantity_rx1: static intruder quantity on rx1
#   static_quantity_rx2: static intruder quantity on rx2
#   capture: turns on or off the capture of Mode S messages
#   static_test_mode: turns on or off the Static Test Mode
#   utc_time: turns on or off the UTC time from the GPS signal
#   mso_step: the increment used to define the Message Start Opportunities for each UAT ADS-B message defined
#   mask: enables/disables the capture of messages
#   test_mode_type: specifies the UAT test mode type as the UAT Test Mode Definitions below.
#                    Normal 		  			  => 1,
#                    Overlap      				  => 2,
#                    DME_fruit_12us_Spacing		  => 3,
#                    Retrigger_long_adsb          => 4
#                    Retrigger_long_groundlink    => 5,
#                    Pulse_interference           => 6,
#                    Ground_link_invalid_mso      => 7,
#                    Air_message_invalid_mso      => 8,
#                    Doppler_test                 => 9,
#                    Modulation_frequency         => 10,
#                    DME_fruit_30us_Spacing       => 11,
#                    Doppler_modulation           => 12,
#                    Receiver_selectivity         => 13
#   iq_filter: UAT Test Mode I/Q FILTER MAGNITUDE
#   horizontal_spacing: the horizontal spacing for the UAT test Mode Selected in 10 ns steps
#   sweep_mode:  turns on or off the sweeping UAT mode
#   sweep_step:  specifies the sweep step
#   sweep_interval: specifies the sweep interval
def set_scenario(reset=None, run_time=None, channel=None, dynamic_quantity_rx1=None, dynamic_quantity_rx2=None,
                 static_quantity_rx1=None, static_quantity_rx2=None, cpature=None, utc_time=None, mso_step=None,
                 mask=None, test_mode_type=None, iq_filter=None, horizontal_spacing=None, sweep_mode=None,
                 sweep_step=None, sweep_interval=None, static_test_mode=None):
    global logEnable
    config = configparser.ConfigParser()
    config.read('TargetConfiguration/config.ini')
    config.sections()
    try:
        logEnable = config.get('logSwitch', 'logEnable')
        if (logEnable == "ON" and os.path.exists(logFileName)):
            os.remove(logFileName)
    except Exception as err:
        logEnable = "OFF"
        print("The log switch id : %s" % (logEnable))

    global target
    # header for senairo
    message_header = target + ":SCE"
    # reset the scenario, it's better to reset first time.
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:RES | :RESET}CR
    # Description: This command resets the Scenario Menu to no active intruder, interrogator (ground station)
    # and video data block.
    # Example: :ATC:SCE:RES\r
    if reset != None:
        if reset == True:
            message = message_header + ":RES\r\n"
            send(message)

    # UAT Test Mode Type
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:TYPE}SP<numeric>CR
    # Description: This command specifies the UAT test mode type. This command is valid only for UAT scenario.
    # Numeric: 1 to 13 (decimal ASCII)
    # Value  Test Mode Type
    # 1  Normal
    # 2  Overlapping
    # 3  DME Fruit 12 spacing
    # 4  Retrigger Long ADS-B Message
    # 5  Retrigger Long Ground Link Message
    # 6  1090 Pulse Interference
    # 7  Ground Link Message Invalid MSO
    # 8  Airborne Message Invalid MSO
    # 9  Doppler Test
    # 10 Modulation Frequency
    # 11 DME Fruit 30 μs spacing
    # 12 Doppler and Modulation Frequency
    # 13 Receiver Selectivity
    # Default: 1 (Normal)
    # Example: :ATC:SCE:UAT:STEST:TYPE 3\r

    test_mode_type_dict = {
        'Normal': 1,
        'Overlap': 2,
        'DME_fruit_12us_Spacing': 3,
        'Retrigger_long_adsb': 4,
        'Retrigger_long_groundlink': 5,
        'Pulse_interference': 6,
        'Ground_link_invalid_mso': 7,
        'Air_message_invalid_mso': 8,
        'Doppler_test': 9,
        'Modulation_frequency': 10,
        'DME_fruit_30us_Spacing': 11,
        'Doppler_modulation': 12,
        'Receiver_selectivity': 13,
    }

    if test_mode_type != None:
        if test_mode_type in test_mode_type_dict:
            message = message_header + ":UAT:STEST:TYPE " + str(test_mode_type_dict[test_mode_type]) + "\r\n"
            send(message)
            # initial the intruder number to 0
            message=message_header + ":STAT:QUAN " + "0,0\r\n"
            send(message)
        else:
            print("Please set test mode in the test mode dictionary")
    

    # 
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:TI | :TIME}SP<numeric>CR
    # Description: This command sets the total scenario time.
    # Numeric: 1 to 6550 seconds (decimal ASCII)
    # Default: 6550 seconds after power up. After power up, last set time is remembered.
    # Example: :ATC:SCE:TI 651\r

    if run_time != None:
        if (1 <= run_time <= 6550):
            message = message_header + ":TI " + str(run_time) + "\r\n"
            send(message)
        else:
            print("Please set run time from 1 to 6550")

    # Channel Grouping
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:CH | :CHANNEL}SP{UATRX1 | UATRX2}CR
    # Description: This command allows grouping by channel commands that are used to define UAT scenario.
    #  This command must precede the commands used to define UAT Intruders that will be transmitted by the specified channel. 
    #  This command will be ignored when defining Intruders for TCAS type scenario. By default, the channel grouping is UATRX1.
    # Example: :ATC:SCE:CH UATRX1\r
    if channel != None:
        if (channel == 1):
            message = message_header + ":CH UATRX1\r\n"
            send(message)
        elif (channel == 2):
            message = message_header + ":CH UATRX2\r\n"
            send(message)
        else:
            print("Please set UAT channels is 1 or 2")

    # Intruders Quantity
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}{:QUAN| :QUANTITY}SP<numeric1>[,<numeric2>]CR
    # Description: This command sets the number of static or dynamic intruders depending on the scenario type selected. The field <numeric2> is
    #             optional. The absence means zero.
    # Numeric: 0 to 1500 (decimal ASCII)
    #         According to the scenario type selected, the numeric fields have the following meaning:
    #         Scenario Type           UAT                                       MULTI (Multi-Receiver)
    #           <numeric1>            The number of static or dynamic           The number of static or dynamic
    #                                 targets in the UAT RX1 channel.           1090 targets.
    #           <numeric2>            The number of static or dynamic           The number of static or dynamic
    #                                 targets in the UAT RX2 channel.           UAT targets.
    # Default: Scenario Type
    #           UAT             MULTI (Multi-Receiver)
    #           1500            568
    #           32              32
    # Example:   :ATC:SCE:DYN:QUAN 2\r
    #           :ATC:SCE:DYN:QUAN 2,1\r

    if dynamic_quantity_rx1 != None:
        if dynamic_quantity_rx1 < 0 or dynamic_quantity_rx1 > 32:
            print("Please set dynamic quantity RX1 from 0 to 32")
        else:
            if dynamic_quantity_rx2 == None:
                message = message_header + ":DYN:QUAN " + str(dynamic_quantity_rx1) + "\r\n"
                send(message)
            else:
                if dynamic_quantity_rx2 < 0 or dynamic_quantity_rx2 > 32:
                    print("Please set dynamic quantity RX2 from 0 to 32")
                else:
                    message = message_header + ":DYN:QUAN " + str(dynamic_quantity_rx1) + ',' + str(
                        dynamic_quantity_rx2) + "\r\n"
                    send(message)
    elif dynamic_quantity_rx2 != None:
        if dynamic_quantity_rx2 < 0 or dynamic_quantity_rx2 > 32:
            print("Please set dynamic quantity RX2 from 0 to 32")
        else:
            message = message_header + ":DYN:QUAN " + '0,' + str(dynamic_quantity_rx2) + "\r\n"
            send(message)

    # this is for static intruder
    if static_quantity_rx1 != None:
        if static_quantity_rx1 < 0 or static_quantity_rx1 > 1500:
            print("Please set static quantity from 0 to 1500")
        else:
            if static_quantity_rx2 == None:
                message = message_header + ":STAT:QUAN " + str(static_quantity_rx1) + "\r\n"
                send(message)
            else:
                if static_quantity_rx2 < 0 or static_quantity_rx2 > 1500:
                    print("Please set static quantity RX2 from 0 to 1500")
                else:
                    message = message_header + ":STAT:QUAN " + str(static_quantity_rx1) + ',' + str(
                        static_quantity_rx2) + "\r\n"
                    send(message)
    elif static_quantity_rx2 != None:
        if static_quantity_rx2 < 0 or static_quantity_rx2 > 1500:
            print("Please set static quantity RX2 from 0 to 1500")
        else:
            message = message_header + ":STAT:QUAN " + '0,' + str(static_quantity_rx2) + "\r\n"
            send(message)

    # Mode S Message Capture
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:CAP | :CAPTURE}SP{ON | OFF}CR
    # Description: This command turns on or off the capture of Mode S messages.
    # Default: Off
    # Example: :ATC:SCE:CAP ON\r
    if cpature != None:
        if (cpature == True):
            message = message_header + ":CAP ON\r\n"
            send(message)
        elif (cpature == False):
            message = message_header + ":CAP OFF\r\n"
            send(message)
        else:
            print("Please set Capture is True or False")

    # Static Test Mode
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STEST}SP{ON | OFF}CR
    # Description: This command turns on or off the Static Test Mode.
    #             The Static Test Mode allows running the scenario without taking into account the time duration of the scenario. 
    #             When the Static Test Mode is enabled, the Mode S static and dynamic intruders maintain the position acquired 
    #             when the maximum time duration is reached.
    # Default: Off (Reset command turns off this parameter.)
    # Example: :ATC:SCE:STEST ON\r
    if static_test_mode != None:
        if (static_test_mode == True):
            message = message_header + ":STEST ON\r\n"
            send(message)
        elif (static_test_mode == False):
            message = message_header + ":STEST OFF\r\n"
            send(message)
        else:
            print("Please set Static Test Mode is True or False")

    # Scenario UTC Time GPS
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UTCGPS}SP{ON | OFF}CR
    # Description: This command turns on or off the UTC time from the GPS signal. When the UTC Time GPS
    # is disabled, the UTC time is obtained from the Unit clock.
    # Default: On
    # Example: :ATC:SCE:UTCGPS ON\r
    if utc_time != None:
        if (utc_time == True):
            message = message_header + ":UTCGPS ON\r\n"
            send(message)
        elif (utc_time == False):
            message = message_header + ":UTCGPS OFF\r\n"
            send(message)
        else:
            print("Please set Scenario UTC Time GPS is True or False")

    # MSO Step
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:MSOSTEP}SP<numeric>]CR
    # Description: This command sets the increment used to define the Message Start Opportunities for each UAT ADS-B message defined.
    #             This command requires Unit UAT Hardware.
    # Numeric: 1 to 100 (decimal ASCII)
    # Example: :ATC:SCE:MSOSTEP 3\r
    if mso_step != None:
        if (1 <= mso_step <= 100):
            message = message_header + ":MSOSTEP " + str(mso_step) + "\r\n"
            send(message)
        else:
            print("Please set mso step from 1 to 100")

    # MASK
    # Command Syntax: {:ATC | :ATC5000NG}{:RCV | :RCVR}{:MA | :MASK}SP<numeric>CR
    # Description: This command enables/disables the capture of messages.
    # A “1” for the receiver associated Bit means that the receiver is enabled.
    # Numeric: FFF (hexadecimal ASCII Byte)
    # Bit  Receiver
    # 0x01 UUT DF Messages
    # 0x02 UUT ATCRBS Replies
    # 0x04 UUT UF Messages
    # 0x08 UUT ATCRBS Interrogations
    # 0x10 Unit DF messages
    # 0x20 Unit ATCRBS Replies
    # 0x40 Unit UF Messages
    # 0x80 Unit ATCRBS Interrogations
    # Default: No mask (All receivers off)
    # Example::ATC:RCV:MA 44\
    if mask != None:
        if (0 <= mask <= 255):
            message = target + ":RCV:MA " + str(mask) + "\r\n"
            send(message)
        else:
            print("Please set mask for 1 to 255")

    # UAT Test Mode I/Q FILTER MAGNITUDE
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:IQFILTER | :IQF}SP<numeric>CR
    # Description: This command sets I/Q filter magnitude for the UAT test Mode Selected.
    #             This command is valid only for UAT scenarios. 
    #             This command is valid for the UAT Test Mode (Modulation Frequency and Doppler Modulation Frequency).
    # Numeric: 0 to 3 (decimal ASCII).
    # Default: 0 (No Filter)
    # Example: :ATC:SCE:UAT:STEST:IQF 2\r
    if iq_filter != None:
        if (0 <= iq_filter <= 3):
            message = message_header + ":UAT:STEST:IQF " + str(iq_filter) + "\r\n"
            send(message)
        else:
            print("Please set I/Q filter from 0 to 3")

    # UAT Test Mode Horizontal Spacing
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:HSPACING | :HSP} SP<numeric>CR
    # Description: This command sets the horizontal spacing for the UAT test Mode Selected in 10 ns steps.
    # Numeric: 600 to 960 ns (decimal ASCII).
    # Default: 960.
    # Example: :ATC:SCE:UAT:STEST:HSP 601\r
    # NOTE: This command is valid only for UAT scenario.
    #      This command is valid for the UAT Test Mode (Modulation Frequency and Doppler Modulation Frequency).
    if horizontal_spacing != None:
        if (600 <= horizontal_spacing <= 960):
            message = message_header + ":UAT:STEST:HSP " + str(horizontal_spacing) + "\r\n"
            send(message)
        else:
            print("Please set horizontal spacing from 600 to 960")

    # Sweep Mode
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:SWEEP}SP{ON | OFF}CR
    # Description: This command turns on or off the sweeping UAT mode.
    # Default: Off
    # Example: :ATC:SCE:SWEEP ON\r
    # NOTE: This command is valid only for UAT scenario type
    if sweep_mode != None:
        if (sweep_mode == True):
            message = message_header + ":SWEEP ON\r\n"
            send(message)
        elif (sweep_mode == False):
            message = message_header + ":SWEEP OFF\r\n"
            send(message)
        else:
            print("Please set sweep mode is True or False")

    # Sweep Step
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:SWEEP}{:STEP}SP<numeric>CR
    # Description: This command specifies the sweep step.
    # Numeric: 1 to 200 ms (decimal ASCII)
    # Default: 1 ms
    # Example: :ATC:SCE:SWEEP:STEP 2\r
    # NOTE: This command is valid only for UAT scenario type.
    if sweep_step != None:
        if (1 <= sweep_step <= 200):
            message = message_header + ":SWEEP:STEP " + str(sweep_step) + "\r\n"
            send(message)
        else:
            print("Please set sweep step from 1 to 200")

    # Sweep Interval
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:SWEEP}{:INTERVAL}SP<numeric>CR
    # Description: This command specifies the sweep interval.
    # Numeric: 194 to 994 ms (decimal ASCII)
    # Default: 194 ms
    # Example: :ATC:SCE:SWEEP:INTERVAL 201\r
    # NOTE: This command is valid only for UAT scenario type.
    if sweep_interval != None:
        if (1 <= sweep_interval <= 200):
            message = message_header + ":SWEEP:INTERVAL " + str(sweep_interval) + "\r\n"
            send(message)
        else:
            print("Please set sweep interval from 194 to 994")


# Set the scenario page for the UAT Doppler and Modulation Frequency
def set_doppler_modulation_freq(frequency=None, shift=None, modulation=None):
    global target

    # header for senairo
    message_header = target + ":SCE:UAT:STEST:"

    # UAT Test Mode Doppler Frequency
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:DOPPLER}SP<numeric>CR
    # Description: This command sets the carrier frequency for the Doppler Test. This command is valid only for
    # UAT scenario.
    # Numeric: 1.335 to 85.45 kHz (decimal ASCII)
    # Default: 20.0 kHz
    # Example: :ATC:SCE:UAT:STEST:DOPPLER 10.0\r
    if frequency != None:
        if 1.335 <= frequency <= 85.45:
            message = message_header + "DOPPLER " + str(frequency) + "\r\n"
            send(message)
        else:
            print("Please set doppler frequency from 1.335 to 85.45")

    # UAT Test Mode Doppler Shift
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:DSHIFT}SP{PLUS | + | MINUS| -}CR
    # Description: This command sets the shift for the Doppler Test.
    # Default: PLUS
    # Example: :ATC:SCE:UAT:STEST:DSHIFT PLUS\r
    # NOTE: This command is valid only for UAT scenario type.
    if frequency != None:
        if shift == '+':
            message = message_header + "DSHIFT " + "PLUS\r\n"
            send(message)
        elif shift == '-':
            message = message_header + "DSHIFT " + "MINUS\r\n"
            send(message)
        else:
            print("Please set doppler Shift is '+' or '-'")

    # UAT Test Mode Modulation Frequency
    # Command Syntax: {:ATC |:ATC5000NG}{:SCE |:SCENARIO}{:UAT}{:STEST}{:MODULATION|:MOD}SP<numeric>CR
    # Description: This command sets the modulation frequency for the UAT test mode selected. This command is
    #             valid only for UAT scenario. This command is valid for the UAT Test Mode (Modulation
    #             Frequency and Doppler Modulation Frequency).
    # Numeric: 156.25 to 683.59 kHz (decimal ASCII).
    # Default: 312.5 kHz
    # Example: :ATC:SCE:UAT:STEST:MOD
    if modulation != None:
        if 156.25 <= modulation <= 683.59:
            message = message_header + "MOD " + str(modulation) + "\r\n"
            send(message)
        else:
            print("Please set modulation from 156.25 to 683.59kHz")


# Set the scenario page for the UAT Receiver Selectivity
# parameter
#   power: Defines the pulsed interference Power Level.
#   frequency: Defines the pulsed interference frequency.
def set_receiver_selectivity(power=None, frequency=None):
    global target

    # header for senairo
    message_header = target + ":SCE:UAT:STEST:"

    # UAT Test Mode Pulse Power
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:POWER | :POW}
    # SP<numeric>CR
    # Description: This command sets the power level for the UAT test Mode Selected.
    # Numeric: 1 to -98 dBm (decimal ASCII)
    # Default: -20 dBm
    # Example: :ATC:SCE:UAT:STEST:POW -22\r
    # NOTE: This command is valid only for UAT scenario.
    #       This command is valid for the UAT Test Mode (DME Fruit 12 μs spacing, 1090 Pulse
    #       Interference and DME Fruit 30 μs spacing).
    if power != None:
        if -98 <= power <= 1:
            message = message_header + "POW " + str(power) + "\r\n"
            send(message)
        else:
            print("Please set power from -98 to 1dBm")

    # UAT Test Mode Frequency
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:FREQUENCY | :FREQ}
    #                SP<numeric>CR
    # Description: This command sets the frequency for the UAT Test Mode Selected.
    # Numeric: 952 to 1223 MHz (decimal ASCII).
    # Default: 978.0 MHz
    # Example: :ATC:SCE:UAT:STEST:FREQ 980.0\r
    # NOTE: This command is valid only for UAT scenario.
    #       This command is valid for the UAT Test Mode (DME Fruit 12 μs spacing and DME Fruit 30 μs
    #       spacing).
    if frequency != None:
        if 952 <= frequency <= 1223:
            message = message_header + "FREQ " + str(frequency) + "\r\n"
            send(message)
        else:
            print("Please set frequency from 952 to 1223MHz")


# Set up UAT DME Fruit 12us spacing Test scenario
# parameter
#   power: Defines the pulsed interference Power Level.
#   frequency: Defines the pulsed interference frequency.
def set_DME_Fruit_12us_spacing(power=None, frequency=None):
    global target

    # header for scenario
    message_header = target + ":SCE:UAT:STEST:"

    # UAT Test Mode Pulse Power
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:POWER | :POW}
    # SP<numeric>CR
    # Description: This command sets the power level for the UAT test Mode Selected.
    # Numeric: 1 to -98 dBm (decimal ASCII)
    # Default: -20 dBm
    # Example: :ATC:SCE:UAT:STEST:POW -22\r
    # NOTE: This command is valid only for UAT scenario.
    #       This command is valid for the UAT Test Mode (DME Fruit 12 μs spacing, 1090 Pulse
    #       Interference and DME Fruit 30 μs spacing).
    if power != None:
        if -98 <= power <= 1:
            message = message_header + "POW " + str(power) + "\r\n"
            send(message)
        else:
            print("Please set power from -98 to 1dBm")

    # UAT Test Mode Frequency
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:FREQUENCY | :FREQ}
    #                SP<numeric>CR
    # Description: This command sets the frequency for the UAT Test Mode Selected.
    # Numeric: 952 to 1223 MHz (decimal ASCII).
    # Default: 978.0 MHz
    # Example: :ATC:SCE:UAT:STEST:FREQ 980.0\r
    # NOTE: This command is valid only for UAT scenario.
    #       This command is valid for the UAT Test Mode (DME Fruit 12 μs spacing and DME Fruit 30 μs
    #       spacing).
    if frequency != None:
        if 952 <= frequency <= 1223:
            message = message_header + "FREQ " + str(frequency) + "\r\n"
            send(message)
        else:
            print("Please set frequency from 952 to 1223MHz")


# Set up UAT DME Fruit 30us spacing Test scenario
# parameter
#   power: Defines the pulsed interference Power Level.
#   frequency: Defines the pulsed interference frequency.
def set_DME_Fruit_30us_spacing(power=None, frequency=None):
    global target

    # header for scenario
    message_header = target + ":SCE:UAT:STEST:"

    # UAT Test Mode Pulse Power
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:POWER | :POW}
    # SP<numeric>CR
    # Description: This command sets the power level for the UAT test Mode Selected.
    # Numeric: 1 to -98 dBm (decimal ASCII)
    # Default: -20 dBm
    # Example: :ATC:SCE:UAT:STEST:POW -22\r
    # NOTE: This command is valid only for UAT scenario.
    #       This command is valid for the UAT Test Mode (DME Fruit 12 ?s spacing, 1090 Pulse
    #       Interference and DME Fruit 30 ?s spacing).
    if power != None:
        if -98 <= power <= 1:
            message = message_header + "POW " + str(power) + "\r\n"
            send(message)
        else:
            print("Please set power from -98 to 1dBm")

    # UAT Test Mode Frequency
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:FREQUENCY | :FREQ}
    #                SP<numeric>CR
    # Description: This command sets the frequency for the UAT Test Mode Selected.
    # Numeric: 952 to 1223 MHz (decimal ASCII).
    # Default: 978.0 MHz
    # Example: :ATC:SCE:UAT:STEST:FREQ 980.0\r
    # NOTE: This command is valid only for UAT scenario.
    #       This command is valid for the UAT Test Mode (DME Fruit 12 ?s spacing and DME Fruit 30 ?s
    #       spacing).
    if frequency != None:
        if 952 <= frequency <= 1223:
            message = message_header + "FREQ " + str(frequency) + "\r\n"
            send(message)
        else:
            print("Please set frequency from 952 to 1223MHz")

# Set up 1090 Pulse Interference Test scenario
# parameter
#   power: Defines the pulsed interference Power Level.
#   frequency: Defines the pulsed interference frequency.
def set_1090_Pulse_Interference(power=None, frequency=None):
    global target

    # header for scenario
    message_header = target + ":SCE:UAT:STEST:"

    # UAT Test Mode Pulse Power
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:POWER | :POW}
    # SP<numeric>CR
    # Description: This command sets the power level for the UAT test Mode Selected.
    # Numeric: 1 to -98 dBm (decimal ASCII)
    # Default: -20 dBm
    # Example: :ATC:SCE:UAT:STEST:POW -22\r
    # NOTE: This command is valid only for UAT scenario.
    #       This command is valid for the UAT Test Mode (DME Fruit 12 ?s spacing, 1090 Pulse
    #       Interference and DME Fruit 30 ?s spacing).
    if power != None:
        if -98 <= power <= 1:
            message = message_header + "POW " + str(power) + "\r\n"
            send(message)
        else:
            print("Please set power from -98 to 1dBm")

    # UAT Test Mode Frequency
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:UAT}{:STEST}{:FREQUENCY | :FREQ}
    #                SP<numeric>CR
    # Description: This command sets the frequency for the UAT Test Mode Selected.
    # Numeric: 952 to 1223 MHz (decimal ASCII).
    # Default: 978.0 MHz
    # Example: :ATC:SCE:UAT:STEST:FREQ 980.0\r
    # NOTE: This command is valid only for UAT scenario.
    #       This command is valid for the UAT Test Mode (DME Fruit 12 ?s spacing and DME Fruit 30 ?s
    #       spacing).
    if frequency != None:
        if 952 <= frequency <= 1223:
            message = message_header + "FREQ " + str(frequency) + "\r\n"
            send(message)
        else:
            print("Please set frequency from 952 to 1223MHz")

# Set up UAT Retrigger Long ADS-B Msg Test scenario
def set_Retrigger_Long_ADSB_msg(power=None, dataset_size=None):
    global target

    # header for scenario
    message_header = target + ":SCE:STAT:1:"
    # POWER
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{:SQPWR}SP<numeric>CR
    # Description: This command sets the power level of the squitter messages.
    # Numeric: High Power Mode: 1 to -69 dBm (decimal ASCII)
    #         Low Power Mode: -20 to -90 dBm (decimal ASCII)
    #         UAT -110 to 5 dBm (decimal ASCII)
    # Default: -50 dBm
    #         -20 dBm (UAT)
    # Example: :ATC:SCE:DYN:1:SQPWR -60\r
    # Example: :ATC:SCE:POW HI\r
    # {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:POW | :POWER}SP{HI | LO | VLO}CR
    if power != None:
        if (-110 <= power <= 5):
            message = message_header + ":SQPWR " + str(power) + "\r\n"
            send(message)
        else:
            print("Please set power from -110 to 5 dBm")

    # Retrigger Long ADS-B Message Dataset Size
    # Command Syntax : {:ATC|:ATC5000NG}{:SCE|:SCENARIO}{:STAT:|:STATIC:}<intruder number>{:DSSIZE}
    #                  SP{48|64|80|83|107}CR
    # Description: This command sets the dataset size for the ADS-N long message data used for UAT Special
    #              Test mode (Retrigger Long ADS-B Message) of the specified intruder. The <intruder number>
    #               specifies the number og the intruder in the channel grouping specified.
    # Default: 48
    # Example: :ATC:SCE:STAT:1:DSSIZE 64\r
    if dataset_size != None:
        if dataset_size == 48 or dataset_size == 64 or dataset_size == 80 or dataset_size == 83 or dataset_size == 107:
            message = message_header + "DSSIZE " + str(dataset_size) + "\r\n"
            send(message)
        else:
            print("Please set the dataset size, whose value can only be 48,64,80,83 or 107.")


# Set up UAT Retrigger Long Ground Link Msg Test scenario
def Set_Retrigger_Long_Ground_Link_Msg(intruderNumber=None, applicationDataValid=None, latitude=None, longitude=None,
                                       positionValid=None, slotID=None, tisbSiteID=None, UTCCoupled=None,
                                       iFrameQuantity=None,
                                       iFrameData=None, iFrameType=None, power=None, mso=None):
    global target

    # ground uplink message header
    if intruderNumber == None:
        print("Warning: set the intruder number from 1 to 1500 in Set_Retrigger_Long_Ground_Link_Msg")
        return
    else:
        if 0 < intruderNumber <= 1500:
            message_header = target + ":SCE:STAT:" + str(intruderNumber) + ":UAT:GUS:"
        else:
            print("The intruder number should be from 1 to 1500, your intruder number is:" + str(intruderNumber))
            return

    # Application data Valid
    # Command Syntax: {:ATC|:ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC:}<intruder number>{:UAT}
    #                 {:GUS}{:ADVALID}SP{ON | OFF}CR
    # Description: This command enables or disables the Application data valid condition of the specified
    #              intruder. The <intruder number> specifies the number of the intruder in the channel grouping
    #              specified.
    # Default: Off
    # Example: :ATC:SCE:STAT:1:UAT:GUS:ADVALID ON\r
    if applicationDataValid != None:
        message = message_header + "ADVALID " + applicationDataValid + "\r\n"
        send(message)
    else:
        message = message_header + "ADVALID OFF" + "\r\n"
        send(message)

    # Ground Station Latitude
    # Command Syntax: {:ATC|:ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC:}<intruder number> {:UAT}
    #                 {:GUS}{:LAT}SP<numeric>CR
    # Description: This command sets the latitude of the ground station. The <intruder number> specifies the
    #               number of the intruder in the channel grouping specified.
    # Numeric: -90 to 90 degrees (double ASCII)
    # Example: :ATC:SCE:STAT:1:UAT:GUS:LAT 82.3321\r
    if latitude != None:
        if (-90 <= latitude <= 90):
            message = message_header + "LAT " + str(latitude) + "\r\n"
            send(message)
        else:
            print("Please set latitude from -90 to 90 degrees")

    # Ground Station Longitude
    # Command Syntax: {:ATC|:ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC:}<intruder number> {:UAT}
    #                 {:GUS}{:LONG}SP<numeric>CR
    # Description: This command sets the longitude of the ground station. The <intruder number> specifies the
    #                   number of the intruder in the channel grouping specified.
    # Numeric: -180 to 180 degrees (double ASCII)
    # Example: :ATC:SCE:STAT:1:UAT:GUS:LONG 24.6698\r
    if longitude != None:
        if (-180 <= longitude <= 180):
            message = message_header + "LONG " + str(longitude) + "\r\n"
            send(message)
        else:
            print("Please set Longitude from -180 to 180 degrees")

    # Position Val id
    # Command Syntax: {:ATC|:ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC:}<intruder number>{:UAT}
    #                 {:GUS}{:POSVALID}SP{ON | OFF}CR
    # Description: This command enables or disables the Position valid condition of the specified intruder. The
    #              <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Default: Off
    # Example: :ATC:SCE:STAT:1:UAT:GUS:POSVALID ON\r
    if positionValid != None:
        message = message_header + "POSVALID " + positionValid + "\r\n"
        send(message)
    else:
        message = message_header + "POSVALID OFF" + "\r\n"
        send(message)

    # Slot ID
    # Command Syntax: {:ATC|:ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC:}<intruder number>{:UAT}
    #                 {:GUS}{: SLOTID}SP<numeric>CR
    # Description: This command sets the time slot where the Ground Uplink Message transmission occurs.
    #              The <intruder number> specifies the number of the intruder in the channel grouping
    #              specified.
    # Numeric: 0 to 31 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:UAT:GUS:SLOTID 3\r
    if slotID != None:
        if (0 <= slotID <= 31):
            message = message_header + "SLOTID " + str(slotID) + "\r\n"
            send(message)
        else:
            print("Please set Slot ID from 0 to 31")

    # TIS-B Site ID
    # Command Syntax: {:ATC|:ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC:}<intruder number>{:UAT}
    #                 {:GUS}{: TISBID}SP<numeric>CR
    # Description: This command sets the TIS-B Site ID of the specified intruder. The <intruder number>
    #              specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 31 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:UAT:GUS:TISBID 2\r
    if tisbSiteID != None:
        if (0 <= tisbSiteID <= 31):
            message = message_header + "TISBID " + str(tisbSiteID) + "\r\n"
            send(message)
        else:
            print("Please set TIS-B Site ID from 0 to 31")

    # UTC Coupled Condition
    # Command Syntax: {:ATC|:ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}
    #                 <intruder number>{:UTC}SP{ON | OFF}CR
    # Description: This command enables or disables the UTC coupled condition of the specified intruder. The
    #              <intruder number> specifies the number of the intruder in the channel grouping specified.
    # Example: :ATC:SCE:STAT:1:UTC ON\r

    if UTCCoupled != None:
        message = ":ATC:SCE:STAT:" + str(intruderNumber) + ":UTC " + UTCCoupled + "\r\n"
        send(message)
    else:
        message = ":ATC:SCE:STAT:" + str(intruderNumber) + ":UTC OFF" + "\r\n"
        send(message)

    # Information Frames Quantity
    # Command Syntax: {:ATC|:ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC:}<intruder number>{:UAT}
    #                 {:GUS}{:IFRAME}{:NIFRAMES}SP<numeric>CR
    # Description: This command sets the quantity of information frames for the selected intruder. The <intruder
    #              number> specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 255 (decimal ASCII)
    # Example: :ATC:SCE:STAT:1:UAT:GUS:IFRAME:NIFRAMES 6\r
    if iFrameQuantity != None:
        message = message_header + "IFRAME:NIFRAMES " + str(iFrameQuantity) + "\r\n"
        send(message)
    else:
        print("Please set Information Frames Quantity from 0 to 255")

    # Information Frame Data
    # Command Syntax: {:ATC|:ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC:}<intruder number>{:UAT}
    #                 {:GUS}{:IFRAME}<frame no>{:IFDATA}SP{{TEXT,<text>}|{HEX,<numeric>}|{FILE,<pathname>}}CR
    # Description: This command sets the frame data content for the selected intruder. The <intruder number>
    #               specifies the number of the intruder in the channel grouping specified. The <frame no>
    #               specifies the number of the information frame. The source of the frame data content is from
    #               an alphanumeric string (TEXT), a hexadecimal string (HEX) or a file (FILE).
    #               {TEXT,<text>}:<text> text ASCII
    #               {HEX,<numeric>}:<numeric> hexadecimal ASCII
    #               {FILE,<pathname>}: <pathname> text ASCII. For a correct pathname replace the “:” character next to the driver
    #                letter with the “..” character.
    # Example: :ATC:SCE:STAT:2:UAT:GUS:IFRAME:1:IFDATA TEXT,Tail #1\r
    #          :ATC:SCE:STAT:2:UAT:GUS:IFRAME:1:IFDATA HEX,FA123753\r
    #          :ATC:SCE:STAT:2:UAT:GUS:IFRAME:1:IFDATA FILE,D..UATData.txt\r

    if iFrameData != None:
        message = message_header + "IFRAME:1:IFDATA HEX," + iFrameData + "\r\n"
        send(message)
    else:
        print("Please set Information Frame Data, The source of the frame data content is \
                from an alphanumeric string (TEXT), a hexadecimal string (HEX) or a file (FILE).")

    # Information Frame Type
    # Command Syntax: {:ATC|:ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC:}<intruder number>{:UAT}
    #                 {:GUS}{:IFRAME}<frame no>{:IFTYPE}SP<numeric>CR
    # Description: This command sets the frame data format for the selected intruder. The <intruder number>
    #              specifies the number of the intruder in the channel grouping specified.
    # Numeric: 0 to 15 (decimal ASCII)
    # Example: :ATC:SCE:STAT:2:UAT:GUS:IFRAME:2:IFTYPE 3\r
    if iFrameType != None:
        message = message_header + ":IFRAME:1:IFTYPE " + str(iFrameType) + "\r\n"
        send(message)
    else:
        print("please set Information Frame Type from 0 to 15")

    # POWER
    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{:SQPWR}SP<numeric>CR
    # Description: This command sets the power level of the squitter messages.
    # Numeric: High Power Mode: 1 to -69 dBm (decimal ASCII)
    #         Low Power Mode: -20 to -90 dBm (decimal ASCII)
    #         UAT -110 to 5 dBm (decimal ASCII)
    # Default: -50 dBm
    #         -20 dBm (UAT)
    # Example: :ATC:SCE:DYN:1:SQPWR -60\r
    # Example: :ATC:SCE:POW HI\r
    # {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:POW | :POWER}SP{HI | LO | VLO}CR
    if power != None:
        if (-110 <= power <= 5):
            message = ":ATC:SCE:STAT:" + str(intruderNumber) + ":SQPWR " + str(power) + "\r\n"
            send(message)
        else:
            print("Please set power from -110 to 5 dBm")

        # MSO
        # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO{:STAT: | :STATIC: | :DYN: | :DYNAMIC:}<intruder number>{: MSO}SP<numeric>CR
        # Description: This command sets the MSO of the specified intruder.
        #             The <intruder number> specifies thenumber of the intruder in the channel grouping specified.
        # Numeric: 0 to 3951 (decimal ASCII)
        # Example: :ATC:SCE:STAT:1:MSO 3\r
        if mso != None:
            if (0 <= mso <= 3951):
                message = ":ATC:SCE:STAT:" + str(intruderNumber) + ":MSO " + str(mso) + "\r\n"
                send(message)
            else:
                print("Please set MSO from 0 to 3951")


# Start/Stop the scenario
def scenario_start(start=None, stop=None):
    global target

    # header for senairo
    message_header = target + ":SCE:"

    if start != None:
        message = message_header + "START\r\n"
        send(message)

    if stop != None:
        message = message_header + "STOP\r\n"
        send(message)


def set_test_mode(type=None):
    global target

    # header for senairo
    message_header = target + ":SCE:"

    if type != None:
        if type == 'TCAS':
            message = message_header + "TYPE TCAS\r\n"
            send(message)
        elif type == 'XPDR':
            message = message_header + "TYPE XPDR\r\n"
            send(message)
        elif type == 'UAT':
            message = message_header + "TYPE UAT\r\n"
            send(message)
        elif type == 'MULTI':
            message = message_header + "TYPE MULTI\r\n"
            send(message)
        else:
            print("Please set scenario type to 'TCAS', 'XPDR', 'UAT' or 'MULTI'")


def set_static_test_mode(mode='ON'):
    global target

    # header for setting static test mode command.
    message_header = target + ":SCE:"

    # Command Syntax: {:ATC | :ATC5000NG}{:SCE | :SCENARIO}{:STEST}SP{ON | OFF}CR
    if mode == 'OFF':
        message = message_header + "STEST OFF\r\n"
    else:
        message = message_header + "STEST ON\r\n"

    send(message)


# A function that randomly generate uat message data.
# msgType: uat message type which can only be 1,2 or 3.
#   1 presents Basic Adsb message
#   2 presents Long Adsb message
#   3 presents ground uplink message
#
def generate_random_uat_msg(msgType=BASIC_ADSB_MSG_TYPE):
    if msgType == BASIC_ADSB_MSG_TYPE:
        uat_data_msg = str(0) + str(random.randrange(0, 8)) + ''.join(
            [random.choice(DATA_CONTENT) for i in range((BASIC_ADSB_DATA_LEN - 1) * 2)])
    elif msgType == LONG_ADSB_MSG_TYPE:
        uat_data_msg = ''.join([random.choice(DATA_CONTENT) for i in range(LONG_ADSB_DATA_LEN * 2)])
    elif msgType == GNDUPLINK_MSG_TYPE:
        uat_data_msg = ''.join([random.choice(DATA_CONTENT) for i in range(GNDUPLINK_DATA_LEN * 2)])
    else:
        print(
            "Please input the right msgType number, which can only be BASIC_ADSB_MSG_TYPE | LONG_ADSB_MSG_TYPE | GNDUPLINK_MSG_TYPE.")
        return
    return uat_data_msg


#########################################################################################
#   End of Public Function 
#########################################################################################

#########################################################################################
#   Internal Function 
#########################################################################################

# This function is used to create socket connect to host per the (host, naeme)
# host: host ip
# port: port number
def connet(host, port):
    global target
    global uat_socket

    try:
        uat_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        # print ("Socket successfully created")
    except socket.error as err:
        print("socket creation failed with error %s" % (err))
        return

    # print ("Socket Connection")

    try:
        uat_socket.connect((host, port))
    except socket.error as err:
        print("socket connection failed with error %s" % (err))
        uat_socket.close()
        return

    uat_socket.sendall(b"*IDN?\r\n")
    data = uat_socket.recv(256)
    # print ("Successfully connected to target:%s on ip:port == %s:%d" %(data, host, port))


# This function is used to send message to host (TTG/ATC/RGS)
def send(message):
    global uat_socket
    global logEnable
    if logEnable == 'ON':
        # log data to file
        file = open(os.path.join(os.getcwd(), logFileName), 'a')
        file.write(message)
        file.close()
    else:
        print(message)
    uat_socket.sendall(bytes(message, encoding="utf8"))
    #os.system("pause")

    # data = uat_socket.recv(256)
    # print ("Message from target: %s" %data)
