# -*- coding: utf-8 -*-
"""
Created on 3/25/2020

@author: H118396
         <PERSON>
         CNS QUALIFICATION TEST GROUP
         
DESCRIPTION:
Measures Radiated Output Power per DO185B section *******.

Description from DO 385 *******
The power-gain product or Effective Radiated Power (ERP) in the forward direction
associated with each radiated transmission pulse shall (1000) be a maximum of +56 dBm
(400 W), and a minimum of +52 dBm (160 W), assuming full power operation.

The power-gain product is related to Total Radiated Power (TRP), a variable used in the
interference limiting algorithms, according to the following expression:
TRP = P *G * (BW 360 deg)
where P is the net power delivered to the RF reference point, G is the peak azimuth antenna
gain at 0 degrees elevation relative to a matched quarter wave stub ( PG is the powergain
product), and BW is the 3 dB azimuth beamwidth in degrees. The specified limit on
radiated power is intended to prevent an excessive power transmission from causing
premature interference limiting and in turn an unnecessary reduction in the dynamic range
of the whisper-shout sequence.

INPUTS: Boonton Power Meter,RGS2000, VISA object
OUTPUTS: N/A

HISTORY:
08/21/2020  MRS  Added main routine and coments, updated the Power Meter routines
                 to use the new methond to find pulse peaks.

"""


#Required Libraries
import time
import math

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers.RGS2000NG import RGS2000NG  
from TXDLib.Handlers import B4500CPwrMeter

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################


def setup(rm,pw):
    """ Setup the Power Meter """

    rm.logMessage(0,"*Radiated Output Power -Start")  
    
    """ Basic Power Meter setup """
    pw.Reset()
    #Check for Errors
    print("ERROR?: ",pw.getError())
    ### PUT METER IN PULSE MODE
    pw.autoset()         #Initialize to defaults
    time.sleep(5)
    #Mode and Input Channel
    pw.setCalculateMode('PULSE')
    pw.setCalculateUnits('dBm')
    pw.setCalculate1_on('ON')
    

def trigger(rm,pw):
    """ Set the Power Meter trigger, timebase and finds peaks. Retruns the number of pulses """
    
    #Trigger
    pw.basicWrite("TRIGger:POSition MIDDLE")
    pw.basicWrite("TRIGger:SOURce CH1")
    pw.basicWrite("TRIGger:SLOPe POS")
    pw.basicWrite("TRIGger:MODE NORMAL")  #Importantae!
    pw.basicWrite("TRIGger:LEV -20.0")    #Importantae!
    
    #TimeBase
    pw.basicWrite("DISPlay:MODE GRAPH")
    TimeBase = '10e-6'                 #Importantae!
    pw.setTimeBase(TimeBase)
    print("TimeBase-Pulse: ",pw.getTimeBase())
    print("TSPAN: ",pw.basicQuery("DISPlay:TSPAN?"))
    pw.setFrequency1('1.030e9') 
    
    #Find Peaks, Get the power for each pulse
    nPulse = pw.findpeaks('-20')      #actual level for max in dBm
     
    rm.logMessage(0,"*Radiated Output Power: Pulses: " + str(nPulse))  
    
    return nPulse

def measurePP(pw, pulse_num):
    """ Returns peak power (dBm) of pulse number """
    pw.setpulsepositions(pulse_num)        # set markers for pulse i
    time.sleep(1)
    pwr = pw.getpwrmeasuremet().split(',')
    return float(pwr[3])


def calcERP(cald_element_list):
    """ Effective Radiated Power Equation with corrected element power (dBm) passed in as array/list """
    pwr = 0.00
    for e in cald_element_list:
        # Sum dBm to Watts
        pwr += (0.001 * 10**(e/10))
    # Watts back to dBm
    pwr = 10 * math.log10(pwr * 1000)
    return pwr

def chanDeviation(cald_element_list):
    """ Maximum channel deviation calculation with corrected element power (dBm) passed in as an array/list """
    return (max(cald_element_list) - min(cald_element_list))

##############################################################################
################# MAIN     ##################################################
##############################################################################


def Test_2_3_3_1(rm,rgs,pw):
    """"*** DO-185E/385, Radiated Output Power, Sect ******* ***"""

    rm.logMessage(0,"*** DO-185E/385, Radiated Output Power, Sect ******* ***")
    
    #initialize results
    res = [-1.0,-1.0,-1.0,-1.0,-1.0]    
    
    #Load Default Qual Scenario
    rgs.stopScen() #stop scenario if already running.
    rgs.loadScen('Brglt10ModeS.csv')
    time.sleep(10)
    # Start Scenario
    rgs.startScen()
    time.sleep(20)

    #Power Measurements
    setup(rm,pw)
    trigger(rm,pw)
    #plots the results
    #pw.plotpeaks('-20')     #disable this plot when running from test stand

    res[0] = measurePP(pw, 0)
    res[1] = measurePP(pw, 1)
    res[2] = measurePP(pw, 2)
    res[3] = measurePP(pw, 3)
    res[4] = measurePP(pw, 4)
    
    rm.logMessage(0,"Results: " + str(res))
     
    rm.logMessage(0,"Test_******* - Done")    
    rgs.stopScen() #stop scenario if already running.
    
    return res
    
 


#run as main from command line
if __name__ == "__main__":
    
    #initialize RM
    rm = ate_rm()
 
    #Initiazlie the RGS
    rgs = RGS2000NG(rm)

    pwr_obj = B4500CPwrMeter(rm)
    pwr_obj.Reset() 
    
    res = Test_2_3_3_1(rm, rgs, pwr_obj)
    
    pwr_obj.close()
    rgs.close()
    rm.cleanup()

