# -*- coding: utf-8 -*-
"""
Created on 5/5/2021

@author: H118396
         <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
DESCRIPTION:
Bearing Accuracy Per DO185 section *******.4.2.

Description from DO 385 *******.6.2
    -Accuracy +/- 10 Deg Elevation
     Active surveillance bearing error shall not exceed 9 degrees RMS (or 27 deg peak) over all azimuth angles for
     elevation angles < +/- 10 deg using ModeC and ModeS replies.
    
    -Accuracy > +/- 10 Deg Elevation (up to 20 deg)
     For elevation anges > +/- 10 deg up to +/-20 deg, the bearing error shall not exceed 15 RMS (or 45 deg peak). 
     
     NOTES: Four Scenario Files are loaded to perform this test:
            Brgtt10ModeC     -- 4 intruders, elevation < 10 deg, ModeC
            Brggt10ModeC    -- 4 intruders, elevation > 10 deg, ModeC
            Brglt10ModeS    -- 4 intruders, elevation < 10 deg, ModeS 
            Brggt10ModeS    -- 4 intruders, elevation > 10 deg, ModeC
            
            Angles for the static intruders are at +45,135,225,315. 
            Own Altitude is 8000ft.

INPUTS: RM, RGS2000, ARINC_Client
OUTPUTS: N/A

HISTORY:
05/5/2021  MRS  Initial Release

"""


#Required Libraries
import time
import math

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers.RGS2000NG import RGS2000NG 
from TXDLib.Handlers.ARINC_Client import ARINC_Client 


##############################################################################
################# FUNCTIONS ##################################################
##############################################################################


def readTCASData(rm,ARINC):
    """ Reads data via ARINC client, returns 4 lists containing data
        in the order intruders, range, altitude, bearing. """
    #Read the Number of TCAS Intruders
    intr = ARINC.TCAS_Read(b"READ,TCAS_OUT,INT")
    rm.logMessage(0,"Number of Intruders: " + str(intr))

    #Get a List of All TCAS Intruders
    data = ARINC.TCAS_Read(b"READ,TCAS_OUT,ALL")
    #rm.logMessage(0,"Intruder Return String: " + str(data))
    #Decode the Return string into separate lists
    itr,rng,alt,brg = ARINC.TCAS_Decode(data)
    return itr, rng, alt, brg

def compute_brg_error(bearing):
    """ Just compute the bearing error based on quadrant, return positive angle."""    
    brg_error = 0.0

    if bearing < 0.0:
        bearing = bearing + 360.0
    
    #intruder angles at: 45, 135, 225 and 315
    if bearing > 0.0:
       brg_error = 45.0 - bearing
    if bearing > 90.0: 
        brg_error = 135.0 - bearing
    if bearing > 180.0:
        brg_error = 225.0 - bearing
    if bearing > 270.0:
        brg_error = 315.0 - bearing

    return brg_error


def compute_BearingAccuracy(rm,ARINC):
    """ Reads Intruder data 'count' times, computes bearing accuracy, returns bearing avg and max error. """
    #Note: we don't know the order that TCAS is reporting intruders
    count = 25       #number of intruder samples
    avg0 = 0.0       #intruder #1, bearing avg
    avg1 = 0.0       #intruder #2, bearing avg
    avg2 = 0.0       #intruder #3, bearing avg
    avg3 = 0.0       #intruder #4, bearing avg
    max0 = 0.0       #intruder #1, bearing max
    max1 = 0.0       #intruder #2, bearing max
    max2 = 0.0       #intruder #3, bearing max
    max3 = 0.0       #intruder #4, bearing max

    
    #get intruder data, 50x, compute averages and max values
    for i in range(count):
        #get intruder data
        itr, rng, alt, brg = readTCASData(rm,ARINC)
        #compute avergages and max
        avg0 = avg0 + brg[0]
        avg1 = avg1 + brg[1]
        avg2 = avg2 + brg[2]
        avg3 = avg3 + brg[3]
        #print(avg0,avg1,avg2,avg3)
        if abs(brg[0]) > max0: max0 = brg[0]
        if abs(brg[1]) > max1: max1 = brg[1]
        if abs(brg[2]) > max2: max2 = brg[2]
        if abs(brg[3]) > max3: max3 = brg[3]
        time.sleep(2)  #two seconds between samples
    
    #compute averages
    avg0 = (avg0/count)
    avg1 = (avg1/count)
    avg2 = (avg2/count)
    avg3 = (avg3/count)
    #compute bearing error
    avg0err = compute_brg_error(avg0)
    avg1err = compute_brg_error(avg1)
    avg2err = compute_brg_error(avg2)
    avg3err = compute_brg_error(avg3)
    rm.logMessage(0,"Avg0err: " + str(avg0err))        
    rm.logMessage(0,"Avg1err: " + str(avg1err))        
    rm.logMessage(0,"Avg2err: " + str(avg2err))        
    rm.logMessage(0,"Avg3err: " + str(avg3err))

    #change Maximums to Positive Angles
    max0err = compute_brg_error(max0)
    max1err = compute_brg_error(max1)
    max2err = compute_brg_error(max2)
    max3err = compute_brg_error(max3)
    rm.logMessage(0,"Max0err: " + str(max0err))        
    rm.logMessage(0,"Max1err: " + str(max1err))        
    rm.logMessage(0,"Max2err: " + str(max2err))        
    rm.logMessage(0,"Max3err: " + str(max3err))

    
    #return max of averages and maximums
    avg = max(avg0err,avg1err,avg2err,avg3err)
    mxx = max(max0err,max1err,max2err,max3err)

    return avg,mxx
       
    
##############################################################################
################# MAIN     ##################################################
##############################################################################


def Test_2_2_4_6_4_2(rm,rgs,ARINC):
    """"*** DO-185E/385, Bearing Accuracy, Sect *******.4.2 ***"""

    rm.logMessage(0,"*** DO-185E/385, Bearing Accuracy, Sect *******.4.2***")
    
    #initialize results
    brg_avg = [-1.0,-1.0,-1.0,-1.0]    
    brg_max = [-1.0,-1.0,-1.0,-1.0]    

    rgs.stopScen() #stop scenario if any running.
    """
    #Load ModeC Bearing Scenario (< +/- 10 deg)
    rgs.loadScen('Brglt10ModeC.csv')
    time.sleep(5)
    # Start Scenario
    rgs.startScen()
    time.sleep(20)   #long wait
    brg_avg[0],brg_max[0] = compute_BearingAccuracy(rm,ARINC)
    rm.logMessage(0,"Bearing AVG and MAX: " + str(brg_avg[0]) + ", " + str(brg_max[0]))
    rgs.stopScen() #stop scenario 
    
    #Load ModeC Bearing Scenario (> +/- 10 deg)
    rgs.loadScen('Brggt10ModeC.csv')
    time.sleep(5)
    # Start Scenario
    rgs.startScen()
    time.sleep(20)
    brg_avg[1],brg_max[1] = compute_BearingAccuracy(rm,ARINC)
    rm.logMessage(0,"Bearing AVG and MAX: " + str(brg_avg[1]) + ", " + str(brg_max[1]))
    rgs.stopScen() #stop scenario
    """
    #Load ModeS Bearing Scenario (< +/- 10 deg)
    rgs.loadScen('Brglt10ModeS.csv')
    time.sleep(5)
    # Start Scenario
    rgs.startScen()
    time.sleep(20)
    brg_avg[2],brg_max[2] = compute_BearingAccuracy(rm,ARINC)
    rm.logMessage(0,"Bearing AVG and MAX: " + str(brg_avg[2]) + ", " + str(brg_max[2]))
    rgs.stopScen() #stop scenario
    
    #Load ModeS Bearing Scenario (> +/- 10 deg)
    rgs.loadScen('Brggt10ModeS.csv')
    time.sleep(5)
    # Start Scenario
    rgs.startScen()
    time.sleep(20)
    brg_avg[3],brg_max[3] = compute_BearingAccuracy(rm,ARINC)
    rm.logMessage(0,"Bearing AVG and MAX: " + str(brg_avg[3]) + ", " + str(brg_max[3]))
    rgs.stopScen() #stop scenario


    rm.logMessage(0,"Bearing AVG Error " + str(brg_avg))
    rm.logMessage(0,"Bearing Max Error " + str(brg_max))


    rm.logMessage(0,"Test_*******.4.2 - Done")    
   
    #return concatenated lists
    return brg_avg + brg_max
    
 


#run as main from command line
if __name__ == "__main__":
    
    #SetUP Resource Manager
    rm = ate_rm()
   
    #Initialize the ARINC Server
    ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")

    #Initiazlie the RGS
    rgs = RGS2000NG(rm)
  

    Test_2_2_4_6_4_2(rm, rgs, ARINC)
    
    
    #Close the Connection
    ARINC.close_ARINC_Client()
    #Kill the Server
    ARINC.kill_ARINC_Server()
    
    #Close RGS
    rgs.close()


