def TCASPulseTimingSetup(aterm):
    ''' Setup Scope for TCAS pulse measurements'''
    aterm.logMessage(1, "Procedure Started")

    aterm.instruments["oscope"].chanDisplay(1,0)
    aterm.instruments["oscope"].chanDisplay(2,1)
    aterm.instruments["oscope"].chanDisplay(3,1)
    aterm.instruments["oscope"].chanDisplay(4,1)
    aterm.instruments["oscope"].chanDisplay(5,1)
    aterm.instruments["oscope"].chanDisplay(6,0)
    aterm.instruments["oscope"].chanInvert(2,1)
    aterm.instruments["oscope"].chanInvert(3,1)
    aterm.instruments["oscope"].timeScale(4,"us")
    aterm.instruments["oscope"].voltDiv(2,5,"mv")
    aterm.instruments["oscope"].voltDiv(3,5,"mv")
    aterm.instruments["oscope"].voltDiv(4,500,"mv")
    aterm.instruments["oscope"].voltDiv(5,500,"mv")
    aterm.instruments["oscope"].setTimePosition(0.000016)
    aterm.instruments["oscope"].chanOffset(2,10,"mv")
    aterm.instruments["oscope"].chanOffset(3,10,"mv")
    aterm.instruments["oscope"].chanOffset(4,1,"v")
    aterm.instruments["oscope"].chanOffset(5,1,"v")
    aterm.instruments["oscope"].setChannelTermination(2,50)
    aterm.instruments["oscope"].setChannelTermination(3,50)

    aterm.instruments["oscope"].setEdgeTrig(4,1,"FALL","DC")

    aterm.instruments["oscope"].createMeasurement(1,"RISETIME")
    aterm.instruments["oscope"].createMeasurement(2,"FALLTIME")
    aterm.instruments["oscope"].createMeasurement(3,"PWIDTH")
    aterm.instruments["oscope"].createMeasurement(4,"PERIOD")
    aterm.instruments["oscope"].createMeasurement(5,"RISETIME")
    aterm.instruments["oscope"].createMeasurement(6,"FALLTIME")
    aterm.instruments["oscope"].createMeasurement(7,"PWIDTH")
    aterm.instruments["oscope"].createMeasurement(8,"PERIOD")

    aterm.instruments["oscope"].setMeasurementSource(1,2)
    aterm.instruments["oscope"].setMeasurementSource(2,2)
    aterm.instruments["oscope"].setMeasurementSource(3,2)
    aterm.instruments["oscope"].setMeasurementSource(4,2)
    aterm.instruments["oscope"].setMeasurementSource(5,3)
    aterm.instruments["oscope"].setMeasurementSource(6,3)
    aterm.instruments["oscope"].setMeasurementSource(7,3)
    aterm.instruments["oscope"].setMeasurementSource(8,3)

    aterm.logMessage(1, "Procedure Ended")

def XPDRPulseTimingSetup(aterm):
    ''' Setup Scope for XPDR pulse measurements'''
    aterm.logMessage(1, "Procedure Started")

    aterm.instruments["oscope"].chanDisplay(1,0)
    aterm.instruments["oscope"].chanDisplay(2,1)
    aterm.instruments["oscope"].chanDisplay(3,0)
    aterm.instruments["oscope"].chanDisplay(4,1)
    aterm.instruments["oscope"].chanDisplay(5,0)
    aterm.instruments["oscope"].chanDisplay(6,0)
    aterm.instruments["oscope"].chanInvert(2,1)
    aterm.instruments["oscope"].timeScale(4,"us")
    aterm.instruments["oscope"].voltDiv(2,5,"mv")
    aterm.instruments["oscope"].voltDiv(4,500,"mv")
    aterm.instruments["oscope"].setTimePosition(0.000016)
    aterm.instruments["oscope"].chanOffset(2,10,"mv")
    aterm.instruments["oscope"].chanOffset(4,1,"v")
    aterm.instruments["oscope"].setChannelTermination(2,50)

    aterm.instruments["oscope"].setEdgeTrig(4,1,"FALL","DC")

    aterm.instruments["oscope"].createMeasurement(1,"RISETIME")
    aterm.instruments["oscope"].createMeasurement(2,"FALLTIME")
    aterm.instruments["oscope"].createMeasurement(3,"PWIDTH")
    aterm.instruments["oscope"].createMeasurement(4,"PERIOD")

    aterm.instruments["oscope"].setMeasurementSource(1,2)
    aterm.instruments["oscope"].setMeasurementSource(2,2)
    aterm.instruments["oscope"].setMeasurementSource(3,2)
    aterm.instruments["oscope"].setMeasurementSource(4,2)

    aterm.logMessage(1, "Procedure Ended")

def DMEPulseTimingSetup(aterm):
    ''' Setup Scope for XPDR pulse measurements'''
    aterm.logMessage(1, "Procedure Started")

    aterm.instruments["oscope"].chanDisplay(1,0)
    aterm.instruments["oscope"].chanDisplay(2,1)
    aterm.instruments["oscope"].chanDisplay(3,0)
    aterm.instruments["oscope"].chanDisplay(4,1)
    aterm.instruments["oscope"].chanDisplay(5,0)
    aterm.instruments["oscope"].chanDisplay(6,0)
    aterm.instruments["oscope"].chanInvert(2,1)
    aterm.instruments["oscope"].timeScale(4,"us")
    aterm.instruments["oscope"].voltDiv(2,10,"mv")
    aterm.instruments["oscope"].voltDiv(4,500,"mv")
    aterm.instruments["oscope"].setTimePosition(0.000016)
    aterm.instruments["oscope"].chanOffset(2,0,"mv")
    aterm.instruments["oscope"].chanOffset(4,1,"v")
    aterm.instruments["oscope"].setChannelTermination(2,1000000)

    aterm.instruments["oscope"].setEdgeTrig(4,1,"RISE","DC")

    aterm.instruments["oscope"].createMeasurement(1,"RISETIME")
    aterm.instruments["oscope"].createMeasurement(2,"FALLTIME")
    aterm.instruments["oscope"].createMeasurement(3,"PWIDTH")
    aterm.instruments["oscope"].createMeasurement(4,"PERIOD")

    aterm.instruments["oscope"].setMeasurementSource(1,2)
    aterm.instruments["oscope"].setMeasurementSource(2,2)
    aterm.instruments["oscope"].setMeasurementSource(3,2)
    aterm.instruments["oscope"].setMeasurementSource(4,2)

    aterm.logMessage(1, "Procedure Ended")

def TCASPhaseAngleSetup(aterm):
    ''' Setup Scope for TCAS pulse measurements'''
    aterm.logMessage(1, "Procedure Started")

    aterm.instruments["oscope"].chanDisplay(1,1)
    aterm.instruments["oscope"].chanDisplay(2,1)
    aterm.instruments["oscope"].chanDisplay(3,1)
    aterm.instruments["oscope"].chanDisplay(4,1)
    aterm.instruments["oscope"].chanDisplay(5,1)
    aterm.instruments["oscope"].chanDisplay(6,0)
    aterm.instruments["oscope"].chanInvert(2,1)
    aterm.instruments["oscope"].chanInvert(3,1)
    aterm.instruments["oscope"].timeScale(4,"us")
    aterm.instruments["oscope"].voltDiv(1,200,"mv")
    aterm.instruments["oscope"].voltDiv(2,5,"mv")
    aterm.instruments["oscope"].voltDiv(3,5,"mv")
    aterm.instruments["oscope"].voltDiv(4,500,"mv")
    aterm.instruments["oscope"].voltDiv(5,500,"mv")
    aterm.instruments["oscope"].setTimePosition(0.000016)
    aterm.instruments["oscope"].chanOffset(1,700,"mv")
    aterm.instruments["oscope"].chanOffset(2,10,"mv")
    aterm.instruments["oscope"].chanOffset(3,10,"mv")
    aterm.instruments["oscope"].chanOffset(4,1,"v")
    aterm.instruments["oscope"].chanOffset(5,1,"v")
    aterm.instruments["oscope"].setChannelTermination(1,50)
    aterm.instruments["oscope"].setChannelTermination(2,50)
    aterm.instruments["oscope"].setChannelTermination(3,50)

    aterm.instruments["oscope"].setEdgeTrig(4,1,"FALL","DC")

    aterm.instruments["oscope"].cursorDisplay("ON")
    aterm.instruments["oscope"].setCursorPos(2.4, 10.9, "us")
    aterm.instruments["oscope"].setCursorSource(1)
    