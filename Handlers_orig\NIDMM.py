''' Instrument handler class for NI 4065 DMM '''

import time, nidmm, hightime

class NIDMM():
    def __init__(self, ate_rm):
        NIDMM_IP = "PXI1Slot5"
        self.arraySize = 0
        try:
            self.resourceManager = ate_rm
            self.NIDMM = nidmm.Session(NIDMM_IP)
            self.resourceManager.logMessage(0, "Resource Opened")
        except:
            self.resourceManager.logMessage(3, "Resource Failed to Open")
            raise

    ''' Provide DMM's ID information such as manufacturer, model, serial number '''
    def ident(self):
        manufacturer = self.NIDMM.instrument_manufacturer
        model = self.NIDMM.instrument_model
        serialNo = self.NIDMM.serial_number
        self.resourceManager.logMessage(1, 
                                        "Manufacturer: {} Model: {} Serial #: {}".format(str(manufacturer), str(model), str(serialNo)))
        return (manufacturer, model, serialNo)

    def reset(self):
        self.NIDMM.reset()
        self.resourceManager.logMessage(2, "Reset sent")

    def close(self):
        self.NIDMM.close()
        self.resourceManager.logMessage(2, "Resource Closed")

    ''' Runs self test. Zero indicates success.
        WARNING: will reset device '''
    def selfTest(self):
        self.resourceManager.logMessage(1, "Running Self Test...")
        result = self.NIDMM.self_test()
        self.resourceManager.logMessage(1, "Self Test completed, returned " + str(result))
        return result

    ''' Configures common properties of DMM measurement.

        Supported functions and ranges for NI 4065:
        *--------------------*------------------------------------------------------------*
        |      FUNCTION      |                          RANGES                            |
        ---------------------*------------------------------------------------------------*
        |   "DC_VOLTS"       | 0.1, 1, 10, 100, 300 (V)                                   |
        |   "AC_VOLTS"       | 0.2, 2, 20, 300      (V)                                   |
        |   "DC_CURRENT"     | 0.01, 0.1, 1, 3      (A)                                   |
        |   "AC_CURRENT"     | 0.01, 0.1, 0.5, 3    (A)                                   |
        |   "TWO_WIRE_RES"   | 100, 1000, 10000, 100000, 1000000, 10000000, 100000000 (Ω) |
        |   "FOUR_WIRE_RES"  | 100, 1000, 10000, 100000, 1000000 (Ω)                      |
        |   "DIODE"          | 10 V at 100 µA, 3.5 V at 1 mA                              |
        *--------------------*------------------------------------------------------------*
        If an invalid RANGE is entered for a FUNCTION, the RANGE is rounded up 
        to the next valid value.
        DMM performs an Auto Range when rangeVal set to -1.0

        Device resolution for NI 4065 up to 6.5 digits '''
    def configMeasurement(self, function, rangeVal=-1.0, resolution=5.5):
        self.NIDMM.configure_measurement_digits(nidmm.Function[function], rangeVal, resolution)
        self.resourceManager.logMessage(1,
                                        "DMM CONFIGURED -- Function: {} Range: {} Resolution: {} digits".format(str(function),
                                                                                                                str(self.NIDMM.range),
                                                                                                                str(self.NIDMM.resolution_digits)))
    
    ''' Performs single measurement and returns measured value
        Timeout is calculated automatically by default, but can be manually set.
        Valid timeout range is 0–86400000 ms '''
    def read(self, maxTime=hightime.timedelta(milliseconds=-1)):
        measuredVal = self.NIDMM.read(maxTime)
        self.resourceManager.logMessage(1, "Measured " + str(measuredVal))
        return measuredVal

    ''' Configures DMM settings for Multi Point Reads '''
    def configMultiPoint(self, sampleCount):
        self.NIDMM.configure_multi_point(1, sampleCount, nidmm.SampleTrigger["IMMEDIATE"])
        self.arraySize = sampleCount
        self.resourceManager.logMessage(1,
                                        "DMM CONFIGURED -- Sample Count: {}".format(self.NIDMM.sample_count))

    ''' Performs sampleCount measurements and returns array of measurements
        Timeout is calculated automatically by default, but can be manually set.
        Valid timeout range is 0–86400000 ms '''
    def readMultiPoint(self, maxTime=hightime.timedelta(milliseconds=-1)):
        self.NIDMM.initiate()
        dataArray = self.NIDMM.read_multi_point(self.arraySize, maxTime)
        self.resourceManager.logMessage(1, "Took " + str(len(dataArray)) + " measurements")
        return dataArray
       