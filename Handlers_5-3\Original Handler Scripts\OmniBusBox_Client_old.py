# -*- coding: utf-8 -*-
"""
Created on Fri Nov  6 14:54:08 2020

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS Qualification Test Group

Description:
    This class interfaces directly with the OmniBusBox.dll
    The routine works as follows:
        Initialize the card and cores (init),
        change TX voltages, if desired,
        start the ARINC (A429 and A708/Discrete) Cores (start),
        read and/or write any data (A429_read A429_write, DIO_read,DIO_write, A708_read,A708_write),
        stop the ARINC and Discrete IO (stop).

Inputs:  see class methods
Outputs: see class methods

History:


11/6/2020    MRS  Initial Release
12/14/2020   MRS  Added Discrete and A708 IO.
01/11/2021   MRS  Added Set TX Voltage commands.
02/12/2021   MBD  Separated stopping IO from deallocating dll.
"""

#from ctypes import *
import ctypes
import time


class OmniBusBox_Client():
    def __init__(self, ate_rm, cmd_str):

        # Add conneciton to Resource Manager, ignore linting errors
        self.resourceManager = ate_rm
        self.cmd_str = cmd_str
        print(self.cmd_str)
        self.mydll = ctypes.cdll.LoadLibrary(self.cmd_str)
        self.resourceManager.logMessage(0,"OmniBusBox DLL Loaded")

        # Function prototypes
        self.dll_read = self.mydll.A708_read
        self.dll_read.argtypes = [ctypes.POINTER(ctypes.c_ushort)]
        self.dll_read.restype = ctypes.c_int
        self.dll_write = self.mydll.A708_write
        self.dll_write.argtypes = [ctypes.POINTER(ctypes.c_ushort)]
        self.dll_write.restype = ctypes.c_int
        a32 = ctypes.c_ushort * 100
        self.arr1 = a32()
        self.arr2 = a32()



##################################################
# Basic Commands
##################################################
    def init(self,xchan,label,data,rate,speed):
        """
        Initializes all 16 Receivers and 1 Transmitter.
        Must be called first,
        xchan is the transmit channel (1 thru 16),
        label is the transmit label (as hex integer),
        data is the transmit data (as hex integer),
        rate is the update rate in msec (as integer).
        speed is High Speed (==1) or Low Speed (== 0).
          Note: Initialize A708 for Xmit Ch1 and Rcv Ch0
        """
        self.mydll.init(xchan,label,data,rate,speed)
        self.echo_str = "Chan: " + str(xchan) + " Label: " + str(label) + " Data " + hex(data) + " Rate: " + str(rate) + "Speed): " + str(speed)
        self.resourceManager.logMessage(1,self.echo_str)


    def set_tx_voltage_on(self,xchan,highvolt,lowvolt,nullvolt):
        """
        Sets parametric voltages for specified Tx Chan.
        xchan is the transmit channel,
        highvolt,lowvolt,nullvolt are differential amplitudes in mili-volts
        """
        self.mydll.set_tx_voltage_on(xchan,highvolt,lowvolt,nullvolt)
        self.echo_str = "Chan: " + str(xchan) + " HighVolt(mV): " + str(highvolt) + " LowVolt(mv): " + str(lowvolt) + " NullVolt(mv): " + str(nullvolt)
        self.resourceManager.logMessage(1,self.echo_str)

    def set_tx_voltage_default(self,xchan):
        """
        Restores the Tx Chan to default voltage settings.
        """
        self.mydll.set_tx_voltage_default(xchan)
        self.echo_str = "Chan: " + str(xchan) + " SetToDefault Voltages "
        self.resourceManager.logMessage(1,self.echo_str)


    def start(self):
        """Starts the A429 I/O."""
        self.mydll.start()
        self.resourceManager.logMessage(1,"ARINC IO Started")


    def stop(self):
        """Stops the A429 I/O"""
        self.mydll.stop()
        self.resourceManager.logMessage(1,"ARINC IO Stopped")


    def close(self):
        """Stops the A429 I/O if running, closes the card."""
        self.mydll.stop()
        self.mydll.close()
        self.resourceManager.logMessage(2,"ARINC IO Closed")

        del self.mydll
        self.resourceManager.logMessage(2, "OmniBusBox DLL Unloaded")


    def A429_write(self,xchan,data):
        """This routine writes data to the Transmit chan.
        The card should be initialized and started.
        xchan is the transmit channel (1 thru 16),
        data is the transmit data (as hex integer).
        """
        self.mydll.A429_write(xchan,data)
        self.echo_str = "Wrote to Chan: " + str(xchan) + " Data: " + hex(data)
        self.resourceManager.logMessage(1,self.echo_str)


    def A429_read(self,rchan):
        """This routine reads data from the Receive Channel.
        rchan is receive channel (1 thru 16),
        a 32-bit hex word is returned representing the data and label.
        """
        data1 = self.mydll.A429_read(rchan)
        self.echo_str = "data: " + hex(data1)
        self.resourceManager.logMessage(1,self.echo_str)
        return data1

    def DIO_write(self,pin,data):
        """This routine writes data to a Discrite IO pin.
        The card should be initialized and started.
        Pin is the discrete output pin (1 thru 16),
        data is the value for the Pin, (either 0 or 1).
        """
        self.mydll.DIO_write(pin,data)
        self.echo_str = "Wrote to Pin: " + str(pin) + " Data: " + hex(data)
        self.resourceManager.logMessage(1,self.echo_str)


    def DIO_read(self,pin):
        """This routine reads data from a Discrete IO pin.
        Pin is receive pin (1 thru 16),
        the return value should be either a 0 or 1.
        """
        data1 = self.mydll.DIO_read(pin)
        self.echo_str = "Pin: " + str(pin) + " Data: " + hex(data1)
        self.resourceManager.logMessage(1,self.echo_str)
        return data1

    def A708_write(self,Data):
        """This routine writes 100 16bit words out on the A708 Bus, Data is a list
        of 100 ushort values, it returns frame number for the write."""

        self.echo_str = "A708 WRITE"
        self.resourceManager.logMessage(1,self.echo_str)

        for i in range(100):
            self.arr1[i] = Data[i]

        res1 = self.dll_write(self.arr1)
        return res1


    def A708_read(self):
        """This routine reads 100 16bit words of A708 data, it returns the array
        data and a count of words read."""

        self.echo_str = "A708 READ"
        self.resourceManager.logMessage(1,self.echo_str)

        for i in range(100):
            self.arr2[i] = 0

        res2 = self.dll_read(self.arr2)
        return list(self.arr2)