# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Receiver Characteristics, Section *******, Step 7 (Mode S Low Level 
             Reply).
             
             "Interrogate the transponder with a standard Mode S Only All Call
             interrogation at RF Level of -81 dBm.  
             Determine the reply ratio."
             
INPUTS:      RM,ATC,PathLoss
OUTPUTS:     'ReplyRatio' reply ratio at -81dBm for ModeS

HISTORY:

02/06/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review
02/25/2021   MRS    Updates for new Handlers and Lobster.
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# MAIN     ##################################################
##############################################################################
def Test_2_3_2_1_Step7(rm,atc,PathLoss):
    """DO-181E, Receiver Characteristics, Sect *******, Step 7"""

    rm.logMessage(2,"***DO-181E, Receiver Characteristics, Sect *******, Step 7 ***\r\n")
    
  
    #Initialize Reply Ratio
    ReplyRatio = 0.0                                 #Values read by TestStand
       
    #Set Up Transponder -- MODE S
    atc.transponderModeS()       
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF   
    atc.gwrite(":ATC:XPDR:PRF 50")    #Pulse Repatition
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
   
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)

    atc.waitforstatus()            
    rm.logMessage(0,"Test_2_3_2_1_Step7 - Begin Power Loop")    

    #Set the Power Level to -81
    P1 = -81.0 + PathLoss
    rm.logMessage(1,"ATC Power Level: " + str(P1))
    cmd = ":ATC:XPDR:POW " + str(P1)
    atc.gwrite(cmd)
    rm.logMessage(0,"Pwr: " + cmd)
    
    #long wait
    time.sleep(15.0)
            
    replyrate = atc.getPercentReply(2)       
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1

    #ModeS Reply Rate - Bot
    ReplyRatio = replyrate[1]
        
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
       
    
    rm.logMessage(0,"Test_2_3_2_1_Step7 - Done " + str(ReplyRatio))    
    rm.logMessage(2,"Done, closing session")
    
    return ReplyRatio

##############################################################################
#run as main from command line
if __name__ == "__main__":

    #SetUP Resource Manager
    rm = ate_rm()
 
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    
 
    res = Test_2_3_2_1_Step7(rm,atc_obj,-12.0)
    
    
    atc_obj.close()

    


