##==============================================================================
## RF FPGA Revision ID
RFFPGA_REV_ID = int(0xEC9D)

##==============================================================================
## TCAS
TCAS_LO                        = int(0x00000000)
TCAS_HI                        = int(0x00006fff)

## TX Control Register (HSID1657)
TCAS_TXREG_LD                  = int(0x00000000)
## RX Control Register (HSID1665)
TCAS_RXREG_LD                  = int(0x00000008)
## Delay Register (HSID1695)
TCAS_DLYREG_LD                 = int(0x00000010)
## Interrogation FIFO clear
TCAS_INTER_FIFO_CLR            = int(0x00000018)
## Squitter FIFO clear
TCAS_SQUIT_FIFO_CLR            = int(0x00000020)
## Interrogation FIFOs Staus
TCAS_INTER_FIFO_ST             = int(0x00000028)
## Squitter FIFOs Staus
TCAS_SQUIT_FIFO_ST             = int(0x00000030)
## DPSK Data FIFO
TCAS_DPSK_LO                   = int(0x00000038)
TCAS_DPSK_HI                   = int(0x00000070)
## End of Interrogation Interrupt clear
TCAS_EOICLR                    = int(0x00000078)
## Interrogation clear
TCAS_INTER_CLR                 = int(0x00000080)
## Interrogation Sequencer
TCAS_INTER_SEQ_LD              = int(0x00000088)
## Video Quantizer +2dB Register
TCAS_VID_Q_P2DB                = int(0x00000090)
## Video Quantizer +3dB Register
TCAS_VID_Q_P3DB                = int(0x00000098)
## Video Quantizer +6dB Register
TCAS_VID_Q_P6DB                = int(0x000000a0)
## Interrogation MTL Register
TCAS_INTER_MTL                 = int(0x000000a8)
## Interrogation MTL +6dB Register
TCAS_INTER_MTL_P6DB            = int(0x000000b0)
## Interrogation MTL -6dB Register
TCAS_INTER_MTL_M6DB            = int(0x000000b8)
## Interrogation MTL -1dB Register
TCAS_INTER_MTL_M1DB            = int(0x000000c0)
## Interrogation MTL -20dB Register
TCAS_INTER_MTL_M20DB           = int(0x000000c8)
## Interrogation MTL -9dB Register
TCAS_INTER_MTL_M9DB            = int(0x000000d0)
## Interrogation MTL +13dB Register
TCAS_INTER_MTL_P13DB           = int(0x000000d8)
## Squitter MTL Register
TCAS_SQUIT_MTL                 = int(0x000000e0)
## Loopback Select Register
TCAS_LOOPBACK                  = int(0x000000e8)
## Log Sum Compress Level Register
TCAS_LOGSUM_CL                 = int(0x000000f0)
## Log Sum Slope Register
TCAS_LOGSUM_S                  = int(0x000000f8)
## Interrogation Reply Data FIFO - Mode S
TCAS_INTER_D_FIFO_MS_LO        = int(0x00000100)
TCAS_INTER_D_FIFO_MS_HI        = int(0x000008f8)
## Interrogation Reply Bearing FIFO - Mode S
TCAS_INTER_B_FIFO_MS_LO        = int(0x00000900)
TCAS_INTER_B_FIFO_MS_HI        = int(0x000010f8)
## Interrogation Reply Data FIFO - Mode A/C
TCAS_INTER_D_FIFO_AC_LO        = int(0x00001100)
TCAS_INTER_D_FIFO_AC_HI        = int(0x000018f8)
## Interrogation Reply Bearing FIFO - Mode A/C
TCAS_INTER_B_FIFO_AC_LO        = int(0x00001900)
TCAS_INTER_B_FIFO_AC_HI        = int(0x000020f8)
## Squitter BF0 Data FIFO
TCAS_SQUIT_D_BF0_FIFO_LO       = int(0x00002100)
TCAS_SQUIT_D_BF0_FIFO_HI       = int(0x000028f8)
## Squitter BF0 Bearing FIFO
TCAS_SQUIT_B_BF0_FIFO_LO       = int(0x00002900)
TCAS_SQUIT_B_BF0_FIFO_HI       = int(0x000030f8)
## Squitter BF1 Data FIFO
TCAS_SQUIT_D_BF1_FIFO_LO       = int(0x00003100)
TCAS_SQUIT_D_BF1_FIFO_HI       = int(0x000038f8)
## Squitter BF1 Bearing FIFO
TCAS_SQUIT_B_BF1_FIFO_LO       = int(0x00003900)
TCAS_SQUIT_B_BF1_FIFO_HI       = int(0x000040f8)
## Squitter BF2 Data FIFO
TCAS_SQUIT_D_BF2_FIFO_LO       = int(0x00004100)
TCAS_SQUIT_D_BF2_FIFO_HI       = int(0x000048f8)
## Squitter BF2 Bearing FIFO
TCAS_SQUIT_B_BF2_FIFO_LO       = int(0x00004900)
TCAS_SQUIT_B_BF2_FIFO_HI       = int(0x000050f8)
## Squitter BF3 Data FIFO
TCAS_SQUIT_D_BF3_FIFO_LO       = int(0x00005100)
TCAS_SQUIT_D_BF3_FIFO_HI       = int(0x000058f8)
## Squitter BF3 Bearing FIFO
TCAS_SQUIT_B_BF3_FIFO_LO       = int(0x00005900)
TCAS_SQUIT_B_BF3_FIFO_HI       = int(0x000060f8)
## Interrogation EDAC
TCAS_INTER_EDAC                = int(0x00006100)
## Squitter EDAC
TCAS_SQUIT_EDAC                = int(0x00006108)
## Status register
TCAS_STAT                      = int(0x00006110)
## Jitter Timer
TCAS_JITTER                    = int(0x00006118)
## ATE Register
TCAS_ATE                       = int(0x00006120)
## SAMPLECAL Delay
TCAS_SAMPLECAL_DLY             = int(0x00006128)
## MSADJUST
TCAS_MSADJUST                  = int(0x00006130)
## ATADJUST
TCAS_ATADJUST                  = int(0x00006138)
## MSS Strobe Falling-Edge Counter
TCAS_MSS_STRB_CNT              = int(0x00006140)
## TCAS Transmitter Phase Increment (HSID3261)
TCAS_PHASE_INCR                = int(0x00006148)
## TCAS Transmitter E1 & E2 Attenuation and Phase - S1 (HSID3263, HSID3264)
TCAS_ATTEN_PHASE_S1            = int(0x00006150)
## TCAS Transmitter E1 & E2 Attenuation and Phase - S2 (HSID3263, HSID3264)
TCAS_ATTEN_PHASE_S2            = int(0x00006158)
## TCAS Transmitter E1 & E2 Attenuation and Phase - P1 (HSID3263, HSID3264)
TCAS_ATTEN_PHASE_P1            = int(0x00006160)
## TCAS Transmitter E1 & E2 Attenuation and Phase - P2 (HSID3263, HSID3264)
TCAS_ATTEN_PHASE_P2            = int(0x00006168)
## TCAS Transmitter E1 & E2 Attenuation and Phase - P3 (HSID3263, HSID3264)
TCAS_ATTEN_PHASE_P3            = int(0x00006170)
## TCAS Transmitter E1 & E2 Attenuation and Phase - P4 (HSID3263, HSID3264)
TCAS_ATTEN_PHASE_P4            = int(0x00006178)
## TCAS Transmitter E1 & E2 Attenuation and Phase - P6 (HSID3263, HSID3264)
TCAS_ATTEN_PHASE_P6            = int(0x00006180)

##==============================================================================
## Transponder
XPDR_LO                        = int(0x00007000)
XPDR_HI                        = int(0x00007fff)

## Mode S Interrogation Data (HSID2399)
XPDR_MS_IDATA_LO               = int(0x00007000)
XPDR_MS_IDATA_HI               = int(0x00007028)
## Mode S Reply Data (HSID3325)
XPDR_MS_RDATA_LO               = int(0x00007030)
XPDR_MS_RDATA_HI               = int(0x00007038)
## Mode S Control Register (HSID2394)
XPDR_MS_CTRL                   = int(0x00007040)
## Mode S Interrupt Clear (HSID3363)
XPDR_CLEAR_INTR                = int(0x00007048)
## Buffer A SW Clear (HSID3365)
XPDR_SW_CLR_BUF_A              = int(0x00007050)
## Buffer B SW Clear (HSID3366)
XPDR_SW_CLR_BUF_B              = int(0x00007058)
## Buffer C SW Clear (HSID3367)
XPDR_SW_CLR_BUF_C              = int(0x00007060)
## Mode S Status Register (HSID2408)
XPDR_MS_STATUS                 = int(0x00007068)
## Mode S Reply Timer A (HSID3350)
XPDR_REPLY_TIMER_A             = int(0x00007070)
## Mode S Reply Timer B (HSID3351)
XPDR_REPLY_TIMER_B             = int(0x00007078)
## Mode S Reply Timer C (HSID3352)
XPDR_REPLY_TIMER_C             = int(0x00007080)
## DF Squitter Enable Register (HSID2438)
XPDR_DF_SQ_EN                  = int(0x00007088)
## DF Squitter FIFO Control Register (HSID3353)
XPDR_DF_SQ_FIFO_CTL            = int(0x00007090)
## DF Squitter FIFO Status Register (HSID3354)
XPDR_DF_SQ_FIFO_STS            = int(0x00007098)
## DF Squitter FIFO (HSID2436)
XPDR_DF_SQ_FIFO_LO             = int(0x000070a0)
XPDR_DF_SQ_FIFO_HI             = int(0x00007898)
## DF-24 reply timer
XPDR_DF24_REPLY_TIMER          = int(0x000078a0)
## ATCRBS Reply Data (HSID2397)
XPDR_ATCRBS_REPLY              = int(0x000078a8)
## ATCRBS Modulation Data (HSID3369)
XPDR_ATCRBS_MOD                = int(0x000078b0)
## ATCRBS Reply Rate Limit (HSID3327)
XPDR_RATE_LIMIT                = int(0x000078b8)
## XPDR Reset Control (HSID3371)
XPDR_SW_PLD_RESET              = int(0x000078c0)
## Mode S Squitter Reply Data (HSID2405)
XPDR_SQ_RDATA_LO               = int(0x000078c8)
XPDR_SQ_RDATA_HI               = int(0x000078d0)
## Control Register (HSID2393)
XPDR_CTRL                      = int(0x000078d8)
## Squitter Control Register (HSID3323)
XPDR_SQ_CTRL                   = int(0x000078e0)
## Mode S Own Address (HSID2401)
XPDR_OWN_ADDRESS               = int(0x000078e8)
## XPDR Status Register (HSID2407)
XPDR_STATUS                    = int(0x000078f0)
## Threshold Register (HSID2410)
XPDR_THRESHOLD                 = int(0x000078f8)
## Programmable Delay (HSID2412)
XPDR_PROG_DELAY                = int(0x00007900)
## Cable Delay (HSID2413)
XPDR_CABLE_DELAY               = int(0x00007908)
## XPDR Transmitter Phase Increment (HSID3159)
XPDR_PHASE_INCR                = int(0x00007910)
## XPDR Transmitter TOP E1 & E2 Attenuation and Phase (HSID3161)
XPDR_ATTEN_PHASE_TOP           = int(0x00007918)
## XPDR Transmitter BOT E1 & E2 Attenuation and Phase (HSID3267)
XPDR_ATTEN_PHASE_BOT           = int(0x00007920)
## XPDR Transmission Monitoring Delay Adjustment Register (HSID3331)
XPDR_TX_MON_DLY                = int(0x00007928)
## XPDR Transmission Monitoring Log Data FIFO Control Register (HSID3334)
XPDR_LOG_FIFO_CTL              = int(0x00007930)
## XPDR Transmission Monitoring Log Data FIFO Status Register (HSID3336)
XPDR_LOG_FIFO_STS              = int(0x00007940)
## XPDR Transmission Monitoring Log Data FIFO (HSID3338)
XPDR_LOG_FIFO_LO               = int(0x00007948)
XPDR_LOG_FIFO_HI               = int(0x00007b40)

##==============================================================================
## UAT
UAT_LO                         = int(0x00008000)
UAT_HI                         = int(0x0000bfff)

## Debug Register
UAT_DBG                        = int(0x00008000)
## UAT Receive Sequence Count Register (HSID3104)
UAT_SEQ_CNT_REG                = int(0x00008008)
## UAT Time-Stamp Register (HSID3312)
UAT_TIME_STAMP                 = int(0x00008010)
## UAT RX Antenna Switch TOP/BOT Control (HSID3095)
UAT_RX_ANT_SW                  = int(0x00008018)
## UAT Status (HSID3106)
UAT_STATUS                     = int(0x00008020)
## UAT Score Threshold (HSID3097)
UAT_THRESHOLD_REG              = int(0x00008028)
## UAT ADSB MCB Control (HSID3358)
UAT_ADSB_MCB_CTL               = int(0x00008030)
## UAT GU MCB Control (HSID3360)
UAT_GU_MCB_CTL                 = int(0x00008038)
## UAT ADSB MCB Status (HSID3359)
UAT_ADSB_MCB_STS               = int(0x00008040)
## UAT ADSB MCB (HSID3108)
UAT_ADSB_MCB_LO                = int(0x00008048)
UAT_ADSB_MCB_HI                = int(0x00009040)
## UAT GU MCB Status (HSID3361)
UAT_GU_MCB_STS                 = int(0x00009080)
## UAT GS MCB (HSID3110)
UAT_GU_MCB_LO                  = int(0x00009088)
UAT_GU_MCB_HI                  = int(0x0000b400)
## Self Test Control Register (HSID3112)
UAT_ST_CTL                     = int(0x0000b408)
## Self Test Data Memory (HSID3114)
UAT_ST_DATA_LO                 = int(0x0000b410)
UAT_ST_DATA_HI                 = int(0x0000b438)

##==============================================================================
## DDC
DDC_LO                         = int(0x0000c000)
DDC_HI                         = int(0x0000cfff)

## ADC E[1-4] MAX/OF Register (HSID3091)
DDC_ADC_MAX_OF_REG             = int(0x0000c000)
## ADC Test Pattern Register (HSID3294)
DDC_ADC_TST_REG                = int(0x0000c008)
## DAC Test Pattern Register (HSID3293)
DDC_DAC_TST_REG                = int(0x0000c010)
## TCAS Mixer
DDC_TCAS_MIXER                 = int(0x0000c018)
## XPDR Mixer
DDC_XPDR_MIXER                 = int(0x0000c020)
## UAT Mixer
DDC_UAT_MIXER                  = int(0x0000c028)
## DME Mixer
DDC_DME_MIXER                  = int(0x0000c030)
## Beam Forming Phase Shift 90/0 deg (HSID3297)
DDC_BFP_90DG_0DG               = int(0x0000c038)
## Beam Forming Phase Shift 270/180 deg (HSID3298)
DDC_BFP_270DG_180DG            = int(0x0000c040)

##==============================================================================
## DME
DME_LO                         = int(0x0000d000)
DME_HI                         = int(0x0000dfff)

## DME Configuration Register (HSID3008)
DME_CFG_REG                    = int(0x0000d000)
## DME Synthesizer Lock Time Register (HSID3009)
DME_LOCK_TIM_REG               = int(0x0000d008)
## DME Aural Ident Enable Register (HSID3010)
DME_AURAL_IDENT                = int(0x0000d010)
## DME Maximum Range Register (HSID3046)
DME_MAX_RANGE                  = int(0x0000d018)
## DME Observation Point Trigger Delay Register (HSID3023)
DME_TRIG_DLY                   = int(0x0000d020)
## DME Transmit and Receive Register (HSID3011)
DME_TNR_REG                    = int(0x0000d028)
## DME Transmit P1 Register (HSID3012)
DME_TP1_REG                    = int(0x0000d030)
## DME Transmit P2 Register (HSID3013)
DME_TP2_REG                    = int(0x0000d038)
## DME Transmit Valid Register (HSID3014)
DME_TP1VLD_REG                 = int(0x0000d040)
## DME Transmit Bracket Register (HSID3018)
DME_TX_BRKT_REG                = int(0x0000d048)
## DME Noise Threshold Register (HSID3015)
DME_NOISE_THRESHOLD            = int(0x0000d050)
## DME Pulse Decoder Timing Register (HSID3016)
DME_PULS_DEC_TMG               = int(0x0000d058)
## DME RX 6dB Offset Register (HSID3017)
DME_RX_6DB_OFFSET              = int(0x0000d060)
## DME Receiver Pulse-Width Timing Register (HSID3019)
DME_RX_PW                      = int(0x0000d068)
## DME Reply P2 Register (HSID3047)
DME_RP2                        = int(0x0000d070)
## DME Reply P1-P2 Peak Difference Register (HSID3048)
DME_P1P2_MAX_DIFF              = int(0x0000d078)
## DME Reply P2 Track Register (HSID3049)
DME_P2_TRACK                   = int(0x0000d080)
## DME Reply P2 Track Offset Register (HSID3050)
DME_P2_TRACK_OFFSET            = int(0x0000d088)
## DME Dynamic Noise Threshold Register (HSID3051)
DME_DYN_NOISE_THRESHOLD        = int(0x0000d090)
## DME Dynamic Pulse Decoder Timing Register (HSID3052)
DME_DYN_PULS_DEC_TMG           = int(0x0000d098)
## DME Dynamic RX 6dB Offset Register (HSID3053)
DME_DYN_RX_6DB_OFFSET          = int(0x0000d0a0)
## DME Dynamic Receiver Pulse-Width Timing Register (HSID3054)
DME_DYN_RX_PW                  = int(0x0000d0a8)
## DME Dynamic Reply P2 Register (HSID3055)
DME_DYN_RP2                    = int(0x0000d0b0)
## DME Dynamic Reply P1-P2 Peak Difference Register (HSID3056)
DME_DYN_P1P2_MAX_DIFF          = int(0x0000d0b8)
## DME Dynamic Reply P2 Track Register (HSID3057)
DME_DYN_P2_TRACK               = int(0x0000d0c0)
## DME Dynamic Reply P2 Track Offset Register (HSID3058)
DME_DYN_P2_TRACK_OFFSET        = int(0x0000d0c8)
## DME TX Bank Switch Register (HSID3005)
DME_TX_BANK_SW                 = int(0x0000d0d0)
## DME Transmitter Phase Increment (HSID3156)
DME_PHASE_INCR                 = int(0x0000d0d8)
## DME Transmitter E1 Attenuation and Phase (HSID3255)
DME_ATTEN_PHASE                = int(0x0000d0e0)
## DME Transmit Synthesizer Command Queue (HSID3201)
DME_TX_SYN_CMD_Q_LO            = int(0x0000d0e8)
DME_TX_SYN_CMD_Q_HI            = int(0x0000d100)
## DME Receive Synthesizer Command Queue (HSID3203)
DME_RX_SYN_CMD_Q_LO            = int(0x0000d108)
DME_RX_SYN_CMD_Q_HI            = int(0x0000d120)
## DME Range Receiver FIFO Control Register (HSID3248)
DME_RRFIFO_CONTROL             = int(0x0000d128)
## DME Dynamic Range Receiver FIFO Control Register (HSID3245)
DME_DYN_RRFIFO_CONTROL         = int(0x0000d130)
## DME Status Register (HSID3045)
DME_STATUS                     = int(0x0000d140)
## DME Self-Test Range Generation (HSID3025)
DME_ST_RANGE_GEN               = int(0x0000d148)
## DME Self-Test Pulse Pair Generation (HSID3026)
DME_ST_PP_GEN                  = int(0x0000d150)
## DME FIFO Status Register (HSID3247)
DME_RRFIFO_STATUS              = int(0x0000d180)
## DME Range Receiver FIFO (HSID3020)
DME_RRFIFO_LO                  = int(0x0000d188)
DME_RRFIFO_HI                  = int(0x0000d380)
## DME Dynamic FIFO Status Register (HSID3021)
DME_DYN_RRFIFO_STATUS          = int(0x0000d3c0)
## DME Dynamic Range Receiver FIFO (HSID3022)
DME_DYN_RRFIFO_LO              = int(0x0000d3c8)
DME_DYN_RRFIFO_HI              = int(0x0000d5c0)

##==============================================================================
## Board Monitoring
BRD_MON_LO                     = int(0x0000e000)
BRD_MON_HI                     = int(0x0000efff)

## BIT-MUX and XADC Channels samples
BRD_MON_ADC_CH_LO              = int(0x0000e000)
BRD_MON_ADC_CH_HI              = int(0x0000e078)
## BIT-MUX Dwell Time
BRD_MON_DWELL_TIME             = int(0x0000e080)

##==============================================================================
## SPI
SPI_LO                         = int(0x0000f000)
SPI_HI                         = int(0x0000ffff)

## SPI Command FIFO
SPI_COM_FIFO_LO                = int(0x0000f000)
SPI_COM_FIFO_HI                = int(0x0000f3f8)
## SPI Receive FIFO
SPI_REC_FIFO_LO                = int(0x0000f400)
SPI_REC_FIFO_HI                = int(0x0000f7f8)
## SPI Control Register
SPI_CONTROL_REG                = int(0x0000f800)
## SPI Status Register
SPI_STATUS_REG                 = int(0x0000f808)
## DigiPot Control and Status Register
SPI_DIGIPOT_REG                = int(0x0000f810)

##==============================================================================
## Calibration
CAL_LO                         = int(0x00010000)
CAL_HI                         = int(0x00010fff)

## Calibration Cofiguration Register (HSID3121)
CAL_CFG_REG                    = int(0x00010000)
## Calibration Timing Check Register (HSID3280)
CAL_TIME_CHK_REG               = int(0x00010008)
## Calibration Transmitter Phase Increment (HSID3269)
CAL_PHASE_INCR_REG             = int(0x00010010)
## Calibration Transmitter P1 Pulse E1 & E2 Attenuation and Phase (HSID3123)
CAL_ATTEN_PHASE_P1_REG         = int(0x00010018)
## Calibration Transmitter P2 Pulse E1 & E2 Attenuation and Phase (HSID3125)
CAL_ATTEN_PHASE_P2_REG         = int(0x00010020)
## Calibration Tx CAL_ON T1, T2 Timing Register (HSID3131)
CAL_TX_ON_REG                  = int(0x00010028)
## Calibration Tx P1 Bracket T15, T16 Timing Register (HSID3137)
CAL_BRKT_P1_REG                = int(0x00010030)
## Calibration Tx P2 Bracket T17, T18 Timing Register (HSID3273)
CAL_BRKT_P2_REG                = int(0x00010038)
## Calibration Tx P1 AM MOD T3, T4 Timing Register (HSID3133)
CAL_AM_MOD_P1_REG              = int(0x00010040)
## Calibration Tx P2 AM MOD T5, T6 Timing Register (HSID3135)
CAL_AM_MOD_P2_REG              = int(0x00010048)
## Calibration Rx Monitor Sampling Start Time (P1: t9, P2: t10) Register (HSID3143)
CAL_RX_SAMPLE_TM_REG           = int(0x00010050)
## Calibration Tx Monitor Sampling Start Time (P1: t11, P2: t12) Register (HSID3139)
CAL_TX_MON_SAMPLE_TM_REG       = int(0x00010058)
## Calibration Antenna Nulling ADC Sampling Start Time (P1: t13, P2: t14) Register (HSID3141)
CAL_NULL_SAMPLE_TM_REG         = int(0x00010060)
## Calibration P2 / not(P1) Select Time T19 Register (HSID3275)
CAL_SEL_P1N_P2_TM_REG          = int(0x00010068)
## 1030MHz / 1090MHz Tx Cal Data FIFO (HSID3147)
CAL_TX_DATA_MEM_LO             = int(0x00010100)
CAL_TX_DATA_MEM_HI             = int(0x000101f8)
## 1090MHz Rx Cal Data FIFO (HSID3149)
CAL_RX_DATA_MEM_LO             = int(0x00010200)
CAL_RX_DATA_MEM_HI             = int(0x000102f8)
## Calibration Nulling ADC Sample Memory (HSID3234)
CAL_NULL_DATA_MEM_LO           = int(0x00010300)
CAL_NULL_DATA_MEM_HI           = int(0x000103f8)
## Calibration Nulling ADC Control & Status Register (HSID3226)
CAL_NULL_CTRL_REG              = int(0x00010400)
## BIAS Control & Status Register (HSID3206)
CAL_BIAS_CTRL_STAT_REG         = int(0x00010440)
## BIAS Stage Control Register (HSID1682)
CAL_BIAS_STAGE_CTRL_REG        = int(0x00010448)
## BIAS DAC Command Queue FIFO (3:0) (HSID3210)
CAL_BIAS_DAC_CMD_Q_LO          = int(0x00010450)
CAL_BIAS_DAC_CMD_Q_HI          = int(0x00010468)
## BIAS ADC Sample Memory (HSID3208)
CAL_BIAS_ADC_SAMPLE_MEM_LO     = int(0x00010800)
CAL_BIAS_ADC_SAMPLE_MEM_HI     = int(0x00010bf8)

##==============================================================================
## NVM
NVM_LO                         = int(0x00011000)
NVM_HI                         = int(0x00012fff)

## NVM Sub-Sector Shadow 4K Memory (HSID3237)
NVM_SSS_MEM_LO                 = int(0x00011000)
NVM_SSS_MEM_HI                 = int(0x00011ff8)
## NVM Control Register (HSID3236)
NVM_CTRL_REG                   = int(0x00012000)
## NVM Reset and Recovery Register (HSID3330)
NVM_RESET_REG                  = int(0x00012008)
## NVM Status and Control Shadow Register (HSID3240)
NVM_SCS_REG                    = int(0x00012010)

##==============================================================================
## Interrupt and Flow Control
IFC_LO                         = int(0x00013000)
IFC_HI                         = int(0x00013fff)

## Interrupt Status Register (HSID3197)
IFC_INT_STS                    = int(0x00013000)
## Interrupt Enable Register (HSID3315)
IFC_INT_EN                     = int(0x00013008)

##==============================================================================
## DBG_BIT
DBG_BIT_LO                     = int(0x00014000)
DBG_BIT_HI                     = int(0x00036fff)

## BIT Test Register
DBG_BIT_TEST_REG               = int(0x00014000)
## BIT Test Register Write Count
DBG_BIT_TEST_REG_WR_CNT        = int(0x00014008)
## BIT Test Register Read Count
DBG_BIT_TEST_REG_RD_CNT        = int(0x00014010)
## BIT Test FIFO A Status
DBG_BIT_TEST_FIFO_A_STS        = int(0x00014040)
## BIT Test FIFO A
DBG_BIT_TEST_FIFO_A_LO         = int(0x00014048)
DBG_BIT_TEST_FIFO_A_HI         = int(0x00014840)
## BIT Test FIFO B Status
DBG_BIT_TEST_FIFO_B_STS        = int(0x00014880)
## BIT Test FIFO B
DBG_BIT_TEST_FIFO_B_LO         = int(0x00014888)
DBG_BIT_TEST_FIFO_B_HI         = int(0x00015080)
## BIT Test FIFO C Status
DBG_BIT_TEST_FIFO_C_STS        = int(0x000150c0)
## BIT Test FIFO C
DBG_BIT_TEST_FIFO_C_LO         = int(0x000150c8)
DBG_BIT_TEST_FIFO_C_HI         = int(0x000158c0)
## BIT Test FIFO D Status
DBG_BIT_TEST_FIFO_D_STS        = int(0x00015900)
## BIT Test FIFO D
DBG_BIT_TEST_FIFO_D_LO         = int(0x00015908)
DBG_BIT_TEST_FIFO_D_HI         = int(0x00016100)
## BIT Test Circular Buffer A Sequencer
DBG_BIT_TEST_CBUFF_A_SEQCR     = int(0x00016108)
## BIT Test Circular Buffer A Message Length
DBG_BIT_TEST_CBUFF_A_MSG_L     = int(0x00016110)
## BIT Test Circular Buffer A Message Count
DBG_BIT_TEST_CBUFF_A_MSG_C     = int(0x00016118)
## BIT Test Circular Buffer A Status
DBG_BIT_TEST_CBUFF_A_STS       = int(0x00016140)
## BIT Test Circular Buffer A
DBG_BIT_TEST_CBUFF_A_LO        = int(0x00016148)
DBG_BIT_TEST_CBUFF_A_HI        = int(0x00016940)
## MSI_TST[3:0] Interrupt Test Register (HSID3317)
DBG_BIT_MSI_TST                = int(0x00016948)
## INT_TST[4:0] Interrupt Test Register (HSID3318)
DBG_BIT_INT_TST                = int(0x00016950)
## INT/MSI #0 Test - Elapsed Time Counter (HSID3319)
DBG_BIT_INT0_TST_TIMER         = int(0x00016958)
## INT/MSI #1 Test - Elapsed Time Counter (HSID3319)
DBG_BIT_INT1_TST_TIMER         = int(0x00016960)
## INT/MSI #2 Test - Elapsed Time Counter (HSID3319)
DBG_BIT_INT2_TST_TIMER         = int(0x00016968)
## INT/MSI #3 Test - Elapsed Time Counter (HSID3319)
DBG_BIT_INT3_TST_TIMER         = int(0x00016970)
## INT #4 Test - Elapsed Time Counter (HSID3319)
DBG_BIT_INT4_TST_TIMER         = int(0x00016978)
## RF ATE Serial Configuration Register (HSID3230)
DBG_BIT_RF_ATE_SERIAL          = int(0x00016980)
## RF ATE Obeservation (Data) Signal Bus Select Register (HSID3233)
DBG_BIT_RF_OBS_SEL             = int(0x00016988)
## RF Switch and Select Control Register (HSID3303, HSID3304)
DBG_BIT_RF_SW_CTRL             = int(0x00016990)
## BIT Selection and Status Register
DBG_BIT_SEL_STAT_REG           = int(0x00016998)
## BIT Observation FIFO
DBG_BIT_OP_FIFO_LO             = int(0x000169a0)
DBG_BIT_OP_FIFO_HI             = int(0x00036998)
## Endian Test Register
DBG_BIT_ENDIAN_REG             = int(0x000369a0)
## Scratch Pad
DBG_BIT_SCRATCHPAD_LO          = int(0x000369a8)
DBG_BIT_SCRATCHPAD_HI          = int(0x00036aa0)

