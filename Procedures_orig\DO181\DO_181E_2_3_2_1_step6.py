# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Receiver Characteristics, Section *******, Step 6 (Mode S Dynamic 
             Range).
             
             "Interrogate the transponder with a standard Mode S Only All Call
             interrogation at RF Levels of MTL + 3, -51 and -21 dBm steps.  
             Determine the reply ratio."
             
INPUTS:      RM,ATC,PathLoss
OUTPUTS:     'ReplyRatios' array of reply ratios at specified power levels.

HISTORY:

02/06/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review
02/25/2021   MRS    Updates for new Handlers and Lobster.
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# MAIN     ##################################################
##############################################################################
def Test_2_3_2_1_Step6(rm,atc,PathLoss):
    """ DO-181E, Receiver Characteristics, Sect *******, Step 6"""
    
    rm.logMessage(2,"*** DO-181E, Receiver Characteristics, Sect *******, Step 6 ***\r\n")
    
    #Initialize Power Levels and Reply Ratios
    Pwr_Levels = ['-73.0','-50.0','-21']     #interrogation power levels
    ReplyRatios = [0.0,0.0,0.0]              #Values read by TestStand

    #Adjust Power Levels by RF Path Loss
    Pwr_Levels[0] = str((float(Pwr_Levels[0]) + PathLoss))
    Pwr_Levels[1] = str((float(Pwr_Levels[1]) + PathLoss))
    Pwr_Levels[2] = str((float(Pwr_Levels[2]) + PathLoss))

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE S
    atc.transponderModeS()       
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF
    atc.gwrite(":ATC:XPDR:PRF 50")    #Pulse Repatition
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
    
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    
    atc.waitforstatus()            
    rm.logMessage(0,"Test_2_3_2_1_Step6 - Begin Power Loop")    
    
    #Check the Reply Rate for Mode S
    #loop thru the three power levels
    k=0
    for P in Pwr_Levels:
        cmd = ':ATC:XPDR:POW ' + P
        atc.gwrite(cmd)
        time.sleep(15)
        
        #atc.waitforstatus()            
        
        replyrate = atc.getPercentReply(2)
        # fix for erroneous reply rate
        count = 0
        while replyrate[1] == -1.0 and count < 10:
            replyrate = atc.getPercentReply(2)
            count = count + 1            
            
        ReplyRatios[k] = replyrate[1]                  #ModeS Bot
        k=k+1       
        
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    rm.logMessage(0,"Test_2_3_2_1_Step6 - Done: " + str(ReplyRatios))    
    rm.logMessage(2,"Done, closing session")
   
    return ReplyRatios


##############################################################################
#run as main from command line
if __name__ == "__main__":

    #SetUP Resource Manager
    rm = ate_rm()
 
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    
 
    res = Test_2_3_2_1_Step6(rm,atc_obj,-12.0)
    
    
    atc_obj.close()

    


