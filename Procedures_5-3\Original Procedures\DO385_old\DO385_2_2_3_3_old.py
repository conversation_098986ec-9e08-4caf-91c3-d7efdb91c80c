# -*- coding: utf-8 -*-
"""
Created on 3/25/2020

@author: H118396
         <PERSON>
         CNS QUALIFICATION TEST GROUP
         
DESCRIPTION:
Validates TCAS Mode S interrogation does not exceed spectrum designated in DO-185B
Section ******* , required per DO-185 section *******.

INPUTS: Spectrum Analyzer VISA object
OUTPUTS: N/A

HISTORY:


"""

import time

#Required Libraries
import time
import math

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers.RGS2000NG import RGS2000NG  
from TXDLib.Handlers import B4500CPwrMeter



def createLimit(spec_obj):
    limit = [
        "-9.000000000E+07", "-5.000000000E+01", "0.000000000E+00",
        "-6.000000000E+07", "-5.000000000E+01", "1.000000000E+00",
        "-6.000000000E+07", "-4.700000000E+01", "1.000000000E+00",
        "-5.000000000E+07", "-4.700000000E+01", "1.000000000E+00",
        "-5.000000000E+07", "-4.300000000E+01", "1.000000000E+00",
        "-4.000000000E+07", "-4.300000000E+01", "1.000000000E+00",
        "-4.000000000E+07", "-3.900000000E+01", "1.000000000E+00",
        "-3.000000000E+07", "-3.900000000E+01", "1.000000000E+00",
        "-3.000000000E+07", "-3.100000000E+01", "1.000000000E+00",
        "-2.000000000E+07", "-3.100000000E+01", "1.000000000E+00",
        "-2.000000000E+07", "-1.900000000E+01", "1.000000000E+00",
        "-1.000000000E+07", "-1.900000000E+01", "1.000000000E+00",
        "-1.000000000E+07", "-1.500000000E+01", "1.000000000E+00",
        "-8.000000000E+06", "-1.500000000E+01", "1.000000000E+00",
        "-8.000000000E+06", "-1.100000000E+01", "1.000000000E+00",
        "-6.000000000E+06", "-1.100000000E+01", "1.000000000E+00",
        "-6.000000000E+06", "-6.000000000E+00", "1.000000000E+00",
        "-4.000000000E+06", "-6.000000000E+00", "1.000000000E+00",
        "-4.000000000E+06", "0.100000000E+00", "1.000000000E+00",
        "4.000000000E+06", "0.010000000E+00", "1.000000000E+00",
        "4.000000000E+06", "-6.000000000E+00", "1.000000000E+00",
        "6.000000000E+06", "-6.000000000E+00", "1.000000000E+00",
        "6.000000000E+06", "-1.100000000E+01", "1.000000000E+00",
        "8.000000000E+06", "-1.100000000E+01", "1.000000000E+00",
        "8.000000000E+06", "-1.500000000E+01", "1.000000000E+00",
        "1.000000000E+07", "-1.500000000E+01", "1.000000000E+00",
        "1.000000000E+07", "-1.900000000E+01", "1.000000000E+00",
        "2.000000000E+07", "-1.900000000E+01", "1.000000000E+00",
        "2.000000000E+07", "-3.100000000E+01", "1.000000000E+00",
        "3.000000000E+07", "-3.100000000E+01", "1.000000000E+00",
        "3.000000000E+07", "-3.900000000E+01", "1.000000000E+00",
        "4.000000000E+07", "-3.900000000E+01", "1.000000000E+00",
        "4.000000000E+07", "-4.300000000E+01", "1.000000000E+00",
        "5.000000000E+07", "-4.300000000E+01", "1.000000000E+00",
        "5.000000000E+07", "-4.700000000E+01", "1.000000000E+00",
        "6.000000000E+07", "-4.700000000E+01", "1.000000000E+00",
        "6.000000000E+07", "-5.000000000E+01", "1.000000000E+00",
        "9.000000000E+07", "-5.000000000E+01", "1.000000000E+00",
        ]
    spec_obj.LimitSetLine(limit)
    time.sleep(1)

def re_init_specAn(specAn_obj):
    specAn_obj.Reset()
    specAn_obj.SweepContSet("ON")

def specAnSetup(spec_obj):
    spec_obj.CenterFreqSet(1030, "MHz")
    spec_obj.SpanSet(80, "MHz")
    spec_obj.ResBandwidthSet(100, "kHz")
    spec_obj.SweepTimeSet(10, "s")
    
    # Set to single sweep capture
    time.sleep(0.5)
    spec_obj.SweepContSet("OFF")
    time.sleep(10)

    # Zoom in and get Peak at 1030MHz
    spec_obj.SpanSet(5, "MHz")
    ref_level = float(spec_obj.GetMaxPeakPower())
    spec_obj.setRefLevel(ref_level, "dBm")

    # Zoom out
    spec_obj.SpanSet(80, "MHz")
    time.sleep(.5)


def passFail(spec_obj):
    return spec_obj.LimitResult()

def cleanUp(spec_obj):
    spec_obj.LimitSetState(0)
    spec_obj.SweepContSet("ON")

# re_init_specAn(spec_obj)
# specAnSetup(spec_obj)
# createLimit(spec_obj)
# passFail(spec_obj)
# cleanUp(spec_obj)