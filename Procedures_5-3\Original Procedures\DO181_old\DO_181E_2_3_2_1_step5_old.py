# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Receiver Characteristics, Section *******, Step 5 (Mode S 
             Sensitivity).
             
             "Using a standard Mode S Only All Call interrogationn (UF11).  
             Determine the required minimum RF signal level required to 
             produce 90% reply efficiency."
             
INPUTS:      RM,ATC,PathLoss
OUTPUTS:     'PwrLvl' RF Level for ModeS at 90% efficiency.

HISTORY:

02/06/2020   MRS    Initial Release.
05/02/2020   MRS    Cleanup based on Review
02/25/2021   MRS    Updates for new Handlers and Lobster.
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# MAIN     ##################################################
##############################################################################
def Test_2_3_2_1_Step5(rm,atc,PathLoss):
    """ DO-181E, Receiver Characteristics, Sect *******, Step 5 """
        
    rm.logMessage(2,"*** DO-181E, Receiver Characteristics, Sect *******, Step 5 ***\r\n")
    
    #Initialize Power Levels
    Init_PowelLevel = -60.0                      #initial Interrogation Power Level
    PwrLvl = 0.0                                 #Values read by TestStand
    
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE S
    atc.transponderModeS()   
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF    
    atc.gwrite(":ATC:XPDR:PRF 50")    #Pulse Repatition
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
   
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)    #takes a long time for ModeS
    
    atc.waitforstatus() 
    time.sleep(5)           
    rm.logMessage(0,"Test_2_3_2_1_Step5 - Begin Power Loop")    
    
    #Set Initial Power Level
    cmd = ':ATC:XPDR:POW ' + str(Init_PowelLevel)
    atc.gwrite(cmd)
    time.sleep(5)    
    #atc.waitforstatus()            
        
    #decrease power levels unit %replies < 90%
    PwrLvl = Init_PowelLevel
    for i in range(0,15):
        time.sleep(1)
        #atc.waitforstatus()
       
        replyrate = atc.getPercentReply(2)
        count = 0
        while replyrate[1] == -1.0 and count < 10:
            replyrate = atc.getPercentReply(2)
            count = count + 1   
    
        val = replyrate[1]                      #ModeS!
        
        if val > 89.0:                                         #%replies
          PwrLvl = PwrLvl - 1.0
          cmd = ':ATC:XPDR:POW ' + str(PwrLvl)
          #print(cmd)
          atc.gwrite(cmd)
          time.sleep(2)
                  
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    #Compensate for PathLoss
    PwrLvl = PwrLvl - PathLoss
    
    rm.logMessage(0,"Test_2_3_2_1_Step5 - Done: " + str(PwrLvl))      
    rm.logMessage(2,"Done, closing session")
    
    return PwrLvl

##############################################################################
#run as main from command line
if __name__ == "__main__":

    #SetUP Resource Manager
    rm = ate_rm()
 
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    
 
    res = Test_2_3_2_1_Step5(rm,atc_obj,-12.0)
       
    atc_obj.close()

    


