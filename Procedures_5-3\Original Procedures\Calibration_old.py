'''Sample API for encoding, decoding JSON files'''
import json, time, os, glob, math, csv, statistics, socket, base64
from datetime import date, datetime

# Write RF CAL request into expected data format
def rfCal_write(aterm, requestId, operation, top_antenna, P1E1, P1E2, P2E1, P2E2, CalPhInc, rfCalBurstSpacing=0, rfCalBurstNumber=0):
    aterm.logMessage(1, "Procedure Started")
    current_directory = os.getcwd()
    os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\request")
    data = {
            "rf_calibration_request": {
                "requestId" : requestId,               # decimal number, 32-bit, [0, 4294967295]]
                "rfCalibrationOperation" : operation,  # txPowerLeveling, txNulling, rxPowerLeveling, rxNulling, rxHighPowerNullPhase, rxLowPowerPhase, rxPhaseOffset, custom
                "rfCalibrationParams" : top_antenna,   # D0 - top/bottom 1/0
                "rfCalibrationWsIqP1E1" : P1E1,        # 64-bit value
                "rfCalibrationWsIqP1E2" : P1E2,        # 64-bit value
                "rfCalibrationWsIqP2E1" : P2E1,        # 64-bit value
                "rfCalibrationWsIqP2E2" : P2E2,        # 64-bit value
                "rfCalibrationCalPhaseIncr" : CalPhInc, # 64-bit value, applies to the custom operation only
                "rfCalibrationBurstSpacing": rfCalBurstSpacing,
                "rfCalibrationBurstNumber": rfCalBurstNumber
            }
    }
    filename = "rf_cal_tst_msg_" + str(requestId) + ".json"
    with open(filename, "w") as write_file:
        json.dump(data, write_file)
    time.sleep(0.5)
    for removable_file in glob.glob("*.json_processed"):
        os.remove(removable_file)
    os.chdir(current_directory)
    aterm.logMessage(1, "Procedure Ended")

# Write RF CAL request into expected data format
def rfCal_write_direct(aterm, requestId, operation, top_antenna, P1E1, P1E2, P2E1, P2E2, CalPhInc, rfCalBurstSpacing=0, rfCalBurstNumber=0):
    aterm.logMessage(1, "Procedure Started")
    UDP_IP = "************"
    UDP_PORT = 5121

    sock = socket.socket(socket.AF_INET, # Internet
                      socket.SOCK_DGRAM) # UDP

    sock.bind((UDP_IP, UDP_PORT))
    r = {
            "rf_calibration_request": {
                "requestId" : requestId,               # decimal number, 32-bit, [0, 4294967295]]
                "rfCalibrationOperation" : operation,  # txPowerLeveling, txNulling, rxPowerLeveling, rxNulling, rxHighPowerNullPhase, rxLowPowerPhase, rxPhaseOffset, custom
                "rfCalibrationParams" : top_antenna,   # D0 - top/bottom 1/0
                "rfCalibrationWsIqP1E1" : P1E1,        # 64-bit value
                "rfCalibrationWsIqP1E2" : P1E2,        # 64-bit value
                "rfCalibrationWsIqP2E1" : P2E1,        # 64-bit value
                "rfCalibrationWsIqP2E2" : P2E2,        # 64-bit value
                "rfCalibrationCalPhaseIncr" : CalPhInc, # 64-bit value, applies to the custom operation only
                "rfCalibrationBurstSpacing": rfCalBurstSpacing,
                "rfCalibrationBurstNumber": rfCalBurstNumber
            }
    }
    r = str.encode(json.dumps(r))

    sock.sendto(r,('*************',UDP_PORT))
    i=0
    while i<1:
        data = sock.recv(2048)
        data = data.decode('utf-8').split('\x00')[0]
        json_response = json.loads(data)
        try:
            data = (json_response['rf_calibration_response']['rfCalibrationDiodeData'])
            i+=1
        except KeyError:
            pass
    sock.close()
    aterm.logMessage(1, "Procedure Ended")
    return json_response

"""
    txNulling rf Calibration Stage
    This stage runs a pre-defined stage in the RFIO software which means a preset PhaseIncr
    register, config register, and timing registers specifically for the Tx Nulling
    rf Calibration stage.
"""
def txNulling(aterm, requestId, top_antenna, P1E1, P1E2, P2E1, P2E2, CalPhInc):
    rfCal_write(aterm, requestId, "txNulling", top_antenna, P1E1, P1E2, P2E1, P2E2, CalPhInc)

"""
    custom rf Calibration Stage
    This stage is a scratchpad that allows the user to play with the various settings of
    phase increment, configuration, and tx timing registers. The defaults in RFIO are setup in NVM
    to match those of txNulling, however these can be overwritten by using the "overwrite*" commands.
"""
def custom(aterm, requestId, top_antenna, P1E1, P1E2, P2E1, P2E2, CalPhInc):
    rfCal_write(aterm, requestId, "custom", top_antenna, P1E1, P1E2, P2E1, P2E2, CalPhInc)

# Passess request ID, antenna top/bot, WS Phase values, CalPhInc (frequeny), Spacing between words (ms), number of pulses
def run_rf_cal_loop(aterm, requestId, top_antenna, P1E1, P1E2, P2E1, P2E2, CalPhInc, rfCalBurstSpacing, rfCalBurstNumber):
    return rfCal_write_direct(aterm, requestId, "custom", top_antenna, P1E1, P1E2, P2E1, P2E2, CalPhInc, rfCalBurstSpacing, rfCalBurstNumber)

"""
    data that returns specified JSON parameter data for the response with the
    requestId specified
"""
def return_rf_cal_fifo_data(aterm, requestId, fifo_parameter, json_response):
    aterm.logMessage(1, "Procedure Started")

    data = base64.b64decode(json_response['rf_calibration_response'][fifo_parameter])
    dataarr = bytearray(data)
    flippedData = bytearray(data)
    index = 0
    while (index<len(dataarr)):
        for j in range(0,8):
            flippedData[index+j]=dataarr[(7-j)+index]
        index+=8
    data=flippedData.hex()

    aterm.logMessage(1, "Procedure Ended")
    return data

"""
    reads the tx monitoring fifo for the latest rf calibration response
    with the matching responseId given in the parameter
"""
def rf_cal_read_tx_mon_fifo(aterm, requestId, json_response):
    return return_rf_cal_fifo_data(aterm, requestId, 'rfCalibrationTxMonData', json_response)

"""
    reads the tx nulling fifo for the latest rf calibration response
    with the matching responseId given in the parameter
"""
def rf_cal_read_tx_null_fifo(aterm, requestId, json_response):
    return return_rf_cal_fifo_data(aterm, requestId, 'rfCalibrationDiodeData', json_response)

"""
    reads the rx fifo for the latest rf calibration response
    with the matching responseId given in the parameter
"""
def rf_cal_read_rx_fifo(aterm, requestId, json_response):
    return return_rf_cal_fifo_data(aterm, requestId, 'rfCalibrationRxData', json_response)

"""
    overwrite functionality for the custom rf calibration custom stage
    configuration register (defined starting in HSID3120)
    Will replace the RAM copy of what's in the NVM part with the contents
    specified by the register_contents parameter.
"""
def overwrite_cfg_register_old(aterm, requestId, top_antenna, register_contents):
    aterm.logMessage(1, "Procedure Started")
    current_directory = os.getcwd()
    os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\request")
    data = {
             "cfg_override_request" :
            {
                "requestId" : requestId,
                "cfgOverrideOperation" : "rfCalibrationCfgReg",
                "cfgOverrideParams" : top_antenna,
                "cfgOverrideRegisterContents" : register_contents
            }
    }
    filename = "rf_cal_overrite_cfg_msg_" + str(requestId) + ".json"
    with open(filename, "w") as write_file:
        json.dump(data, write_file)
    time.sleep(0.5)
    for removable_file in glob.glob("*.json_processed"):
        os.remove(removable_file)
    os.chdir(current_directory)
    aterm.logMessage(1, "Procedure Ended")
"""
    overwrite functionality for the custom rf calibration custom stage
    configuration register (defined starting in HSID3120)
    Will replace the RAM copy of what's in the NVM part with the contents
    specified by the register_contents parameter.
    Uses direct UDP connection instead of scope tool
"""
def overwrite_cfg_register(aterm, requestId, top_antenna, register_contents):
    aterm.logMessage(1, "Procedure Started")
    UDP_IP = "************"
    UDP_PORT = 5121

    sock = socket.socket(socket.AF_INET, # Internet
                      socket.SOCK_DGRAM) # UDP

    sock.bind((UDP_IP, UDP_PORT))
    
    r = {
             "cfg_override_request" :
            {
                "requestId" : requestId,
                "cfgOverrideOperation" : "rfCalibrationCfgReg",
                "cfgOverrideParams" : top_antenna,
                "cfgOverrideRegisterContents" : register_contents
            }
    }
    r = str.encode(json.dumps(r))
    sock.sendto(r,('*************',UDP_PORT))
    sock.close()
    aterm.logMessage(1, "Procedure Ended")

"""
    overwrite functionality for the custom rf calibration custom stage
    tx timing registers (defined starting in HSID3130, 9 total register that define t1-t19)
    Will replace the RAM copy of what's in the NVM part with the contents
    specified by the register_contents parameter.
"""
def overwrite_cal_tmg_register(aterm, requestId, tmg_reg, register_contents):
    aterm.logMessage(1, "Procedure Started")
    current_directory = os.getcwd()
    os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\request")
    data = {
             "cfg_override_request" :
            {
                "requestId" : requestId,
                "cfgOverrideOperation" : "rfCalibrationTmgReg",
                "cfgOverrideParams" : (tmg_reg & 0xF) << 2,
                "cfgOverrideRegisterContents" : register_contents
            }
    }
    filename = "rf_cal_overrite_tmg_msg_" + str(requestId) + ".json"
    with open(filename, "w") as write_file:
        json.dump(data, write_file)
    time.sleep(0.5)
    for removable_file in glob.glob("*.json_processed"):
        os.remove(removable_file)
    os.chdir(current_directory)
    aterm.logMessage(1, "Procedure Ended")

"""
    overwrites the rf cal custom stage RAM data for the
    RFFPGA_CAL_TX_ON_REG register (HSID3131)
"""
def overwrite_rf_cal_tx_on(aterm, requestId, register_contents):
    overwrite_cal_tmg_register(aterm, requestId, 0, register_contents)

"""
    overwrites the rf cal custom stage RAM data for the
    RFFPGA_CAL_BRKT_P1_REG register (HSID3137)
"""
def overwrite_cal_brkt_p1_reg(aterm, requestId, register_contents):
    overwrite_cal_tmg_register(aterm, requestId, 1, register_contents)

"""
    overwrites the rf cal custom stage RAM data for the
    RFFPGA_CAL_BRKT_P2_REG register (HSID3273)
"""
def overwrite_cal_brkt_p2_reg(aterm, requestId, register_contents):
    overwrite_cal_tmg_register(aterm, requestId, 2, register_contents)

"""
    overwrites the rf cal custom stage RAM data for the
    RFFPGA_CAL_AM_MOD_P1_REG (HSID3133)
"""
def overwrite_cal_am_mod_p1_reg(aterm, requestId, register_contents):
    overwrite_cal_tmg_register(aterm, requestId, 3, register_contents)

"""
    overwrites the rf cal custom stage RAM data for the
    RFFPGA_CAL_AM_MOD_P2_REG (HSID3135)
"""
def overwrite_cal_am_mod_p2_reg(aterm, requestId, register_contents):
    overwrite_cal_tmg_register(aterm, requestId, 4, register_contents)

"""
    overwrites the rf cal custom stage RAM data for the
    RFFPGA_CAL_RX_SAMPLE_TM_REG (HSID3143)
"""
def overwrite_cal_rx_sample_tm_reg(aterm, requestId, register_contents):
     overwrite_cal_tmg_register(aterm, requestId, 5, register_contents)

"""
    overwrites the rf cal custom stage RAM data for the
    RFFPGA_CAL_TX_MON_SAMPLE_TM_REG (HSID3139)
"""
def overwrite_cal_tx_mon_sample_tm_reg(aterm, requestId, register_contents):
    overwrite_cal_tmg_register(aterm, requestId, 6, register_contents)

"""
    overwrites the rf cal custom stage RAM data for the
    RFFPGA_CAL_NULL_SAMPLE_TM_REG (HSID3141)
"""
def overwrite_cal_null_sample_tm_reg(aterm, requestId, register_contents):
    overwrite_cal_tmg_register(aterm, requestId, 7, register_contents)

"""
    overwrites the rf cal custom stage RAM data for the
    RFFPGA_CAL_SEL_P1N_P2_TM_REG (HSID3275)
"""
def overwrite_cal_sel_p1n_p2_tm_reg(aterm, requestId, register_contents):
    overwrite_cal_tmg_register(aterm, requestId, 8, register_contents)

# Chunk the string into fixed width.
def chunkstring(string, length):
    return (string[0+i:length+i] for i in range(0, len(string), length))


def generate_CAL_ATTEN_PHASE(aterm, Attenuator, Phase):
    aterm.logMessage(1, "Procedure Started")
    WS_BITS = 16
    PHASE_BITS = 16
    WS_RANGE = math.pow(2,WS_BITS) - 1
    PHASE_RANGE = math.pow(2,PHASE_BITS) - 1
    TX_Attenuator_Scaled = min(max(int(round(math.pow(10,Attenuator/20)*WS_RANGE,0)),0),WS_RANGE)
    Actual_Attenuation = 20*math.log10(TX_Attenuator_Scaled/WS_RANGE)
    TX_Phase_Scaled = int((Phase%360)*PHASE_RANGE/360)
    Actual_Phase = TX_Phase_Scaled*360/PHASE_RANGE
    aterm.logMessage(1,'   W/S (dB): ' + str(Actual_Attenuation) + ' ,Phase (deg) = ' + str(Actual_Phase))
    intWord = int(hex(TX_Phase_Scaled)[2:].zfill(4) + hex(TX_Attenuator_Scaled)[2:].zfill(4),16)
    aterm.logMessage(1,'   CAL_ATTEN_PHASE Word: ' + str(intWord))
    aterm.logMessage(1, "Procedure Ended")
    return(intWord,Actual_Attenuation,Actual_Phase)

def generate_CAL_PHASE_INCR(aterm, fIF, fs):
    aterm.logMessage(1, "Procedure Started")
    # fIF is required output frequency
    #fs is DAC sampling frequency
    #print('   DAC sampling frequency: ' + str(fs))
    #print('   DAC IF frequency: ' + str(fIF))
    #setFreq = max(min(int((math.pow(2,32)-1)*fIF/fs),math.pow(2,32)-1),0)
    setFreq = max(min(int((math.pow(2,32)-1)*fIF/fs),math.pow(2,32)-1),0)
    #print('   CAL_PHASE_INCR Word: ' + hex(setFreq))
    #print('   CAL_PHASE_INCR Word: ' + str(setFreq))
    aterm.logMessage(1, "Procedure Ended")
    return setFreq

def generate_configuration_register(aterm, top_antenna, cal_brkt_en_e1_p1, cal_brkt_en_e1_p2, cal_brkt_en_e2_p1, cal_brkt_en_e2_p2, high_power, P1_monitor, P2_monitor, TX_E1_Mode_SW, TX_E2_Mode_SW, Cal_Freq_Sel, Self_Test):
    aterm.logMessage(1, "Procedure Started")
    '''top_antenna:
        1, connect TX to top antenna
        0, connect TX to bot antenna
    cal_brkt_en_e1_p1:
        1: Enable P1 pulse on E1
        0: Disable P1 pulse on E1
    cal_brkt_en_e1_p2:
        1: Enable P2 pulse on E1
        0: Disable P2 pulse on E1
    cal_brkt_en_e2_p1:
        1: Enable P1 pulse on E2
        0: Disable P1 pulse on E2
    cal_brkt_en_e2_p2:
        1: Enable P2 pulse on E2
        0: Disable P2 pulse on E2
    high_power:
        1, activate high-power transmit monitor path on both P1 and P2.
        0, activate low-power transmit monitor path on both P1 and P2.
    P1_monitor:
        0: No connect transmit monitor for P1 pulse.
        1: Connect transmit monitor to E1 for P1 pulse.
        2: Connect transmit monitor to E2 for P1 pulse.
    P2_monitor:
        0: No connect transmit monitor for P2 pulse.
        1: Connect transmit monitor to E1 for P2 pulse.
        2: Connect transmit monitor to E2 for P2 pulse.
    TX_E1_Mode_SW:
        Select Bank 0-4 of the E1 transmit mode switch.
    TX_E2_Mode_SW:
        Select Bank 0-1 of the E2 transmit mode switch. 0 = TCAS mode, 1 = RX 1090MHz Calibration
    Cal_Freq_Sel:
        0: TX 1090MHz CAL_FREQ_SEL, Selects proper DDC montior for Calibration
        1: TX 1030MHz CAL_FREQ_SEL, Selects proper DDC montior for Calibration
    Self_Test:
        0: P1 and P2 pulse cycle
        1: SELF-TEST Loopback sequence'''
    RESERVED = '0'
    CAL_RUN = '0'
    CAL_CLEAR = '0'
    CAL_ERROR = '0'
    RX_DDC_SRC_SEL_P2 = '0' # Used during Rx Cal Stage 5. 0 = Primary 1090MHz TCAS, 1 = Secondary 1090MHz TCAS.
    RX_DDC_SRC_SEL_P1 = '0' # Used during Rx Cal Stage 5. 0 = Primary 1090MHz TCAS, 1 = Secondary 1090MHz TCAS.
    TX_MON_DDC_SRC_SEL_P2 = '1' # 0 = Transmit Monitor E1 through RX DDC
    TX_MON_DDC_SRC_SEL_P1 = '0' # 0 = Transmit Monitor E1 through RX DDC
    SEC_E2_FE_MODE_P2 = '01' # Secondary 00 = RX, 01 = TX_TOP, 10 = TX_BOT
    SEC_E2_FE_MODE_P1 = '01' # Secondary 00 = RX, 01 = TX_TOP, 10 = TX_BOT
    PRIM_E1_FE_MODE_P2 = '01' # Primary 00 = RX, 01 = TX_TOP, 10 = TX_BOT
    PRIM_E1_FE_MODE_P1 = '01'# Primary 00 = RX, 01 = TX_TOP, 10 = TX_BOT
    if top_antenna==0:
        SEC_E2_FE_MODE_P2 = '10'
        SEC_E2_FE_MODE_P1 = '10'
        PRIM_E1_FE_MODE_P2 = '10'
        PRIM_E1_FE_MODE_P1 = '10'
    SEC_E2_RX_EN_P2 = '11' # Secondary RX enable (LNA bracket), 00 = RX mode, 01 = RX TOP Only, 10 = RX BOT only, 11 = RX LNA disable
    SEC_E2_RX_EN_P1 = '11' # Secondary RX enable (LNA bracket), 00 = RX mode, 01 = RX TOP Only, 10 = RX BOT only, 11 = RX LNA disable
    PRIM_E1_RX_EN_P2 = '11' # Primary RX enable (LNA bracket), 00 = RX mode, 01 = RX TOP Only, 10 = RX BOT only, 11 = RX LNA disable
    PRIM_E1_RX_EN_P1 = '11' # Primary RX enable (LNA bracket), 00 = RX mode, 01 = RX TOP Only, 10 = RX BOT only, 11 = RX LNA disable
    SEC_RX_BOT_SEL_SW_P2 = '1' # Secondary RX Top Bot Select (should be opposite the TX). 0 = RX Select Top, 1 = RX Select Bot
    SEC_RX_BOT_SEL_SW_P1 = '1' # Secondary RX Top Bot Select (should be opposite the TX). 0 = RX Select Top, 1 = RX Select Bot
    if top_antenna==0:
        SEC_RX_BOT_SEL_SW_P2 = '0'
        SEC_RX_BOT_SEL_SW_P1 = '0'
    TCAS_BOT_SEL_SW_P2 = '1' # RX  TCAS 1090MHz TOP/BOT Select Switch, 0 = TOP,1 = BOT. Should always be set to Bot for TX monitor.
    TCAS_BOT_SEL_SW_P1 = '1' # RX  TCAS 1090MHz TOP/BOT Select Switch, 0 = TOP,1 = BOT. Should always be set to Bot for TX monitor.
    CAL_BRKT_EN_E2_P2 = bin(cal_brkt_en_e2_p2)[2:].zfill(1) #E2 P2 Transmit Enable
    CAL_BRKT_EN_E2_P1 = bin(cal_brkt_en_e2_p1)[2:].zfill(1)  #E2 P1 Transmit Enable
    CAL_BRKT_EN_E1_P2 = bin(cal_brkt_en_e1_p2)[2:].zfill(1)  #E1 P2 Transmit Enable
    CAL_BRKT_EN_E1_P1 = bin(cal_brkt_en_e1_p1)[2:].zfill(1)  #E1 P1 Transmit Enable
    NULL_ADC_SEL_SW_P2 = '0' #Nulling ADC TOP/BOT Select Switch. 0 = Top Antenna, 1 = Bot Antenna
    NULL_ADC_SEL_SW_P1 = '0' #Nulling ADC TOP/BOT Select Switch. 0 = Top Antenna, 1 = Bot Antenna
    if top_antenna==0:
        NULL_ADC_SEL_SW_P2 = '1'
        NULL_ADC_SEL_SW_P1 = '1'
    TX_MON_SEL_SW_P2 = '010' # P2 TX Monitor Select 000: TX No Connect, 001: TX E1 High Power, 010 = TX E2 High Power, 011 = TX E1 Low Power, 100 = TX E2 Low Power
    TX_MON_SEL_SW_P1 = '001' # P1 TX Monitor Select 000: TX No Connect, 001: TX E1 High Power, 010 = TX E2 High Power, 011 = TX E1 Low Power, 100 = TX E2 Low Power
    # P1 Transmit Monitor
    if P1_monitor==0: # No Monitor
        TX_MON_SEL_SW_P1 = '000' # 000: TX No Connect
    else:
        if P1_monitor==1: # Monitor E1
            TX_MON_SEL_SW_P1 = '011' #011 = TX E1 Low Power,
            if high_power==1:
                TX_MON_SEL_SW_P1 = '001' #Assign LSB to high power
        else: # Monitor E2
            TX_MON_SEL_SW_P1 = '100' #011 = TX E2 Low Power,
            if high_power==1:
                TX_MON_SEL_SW_P1 = '010' #Assign LSB to high power
    # P2 Transmit Monitor
    if P2_monitor==0: # No Monitor
        TX_MON_SEL_SW_P2 = '000' # 000: TX No Connect
    else:
        if P2_monitor==1: # Monitor E1
            TX_MON_SEL_SW_P2 = '011' #011 = TX E1 Low Power,
            if high_power==1:
                TX_MON_SEL_SW_P2 = '001' #Assign LSB to high power
        else: # Monitor E2
            TX_MON_SEL_SW_P2 = '100' #011 = TX E2 Low Power,
            if high_power==1:
                TX_MON_SEL_SW_P2 = '010' #Assign LSB to high power

    RESERVED2 = '0'
    PRIM_E1_TX_SEL_SW = '001' # Primary Transmit mode selection switch. 000: Bank 1, 001: Bank 2, 010: Bank 3, 011: Bank 4, 100: Bank 5
    PRIM_E1_TX_SEL_SW = bin(TX_E1_Mode_SW)[2:].zfill(3)
    SEC_E2_TX_SEL_SW = '0' # Secondary Transmit mode selection switch. 0 = TCAS mode, 1 = RX 1090MHz Calibration
    SEC_E2_TX_SEL_SW = bin(TX_E2_Mode_SW)[2:].zfill(1)
    CAL_FREQ_SEL = '1' # Selects proper DDC monitor for calibration in 1030MHz and 1090Mhz modes. 0: TX 1090MHz, 1: TX 1030MHz.
    CAL_FREQ_SEL = bin(Cal_Freq_Sel)[2:].zfill(1)
    CAL_MODE = '01' # CALIBRATION MODE type: 00: NOP,  01: CALIBRATION sequence: P1 and P2 pulse cycle, 10: SELF-TEST Loopback sequence
    if Self_Test == 1:
        CAL_MODE = '10' #SELF-TEST Loopback sequence
    CAL_JITTER_DELAY = '****************'

    binCalConfig = (RESERVED + CAL_RUN + CAL_CLEAR + CAL_ERROR + RX_DDC_SRC_SEL_P2 + RX_DDC_SRC_SEL_P1 + TX_MON_DDC_SRC_SEL_P2 + TX_MON_DDC_SRC_SEL_P1 + SEC_E2_FE_MODE_P2
    + SEC_E2_FE_MODE_P1 + PRIM_E1_FE_MODE_P2 + PRIM_E1_FE_MODE_P1 + SEC_E2_RX_EN_P2 + SEC_E2_RX_EN_P1 + PRIM_E1_RX_EN_P2 + PRIM_E1_RX_EN_P1 + SEC_RX_BOT_SEL_SW_P2
    + SEC_RX_BOT_SEL_SW_P1 + TCAS_BOT_SEL_SW_P2 + TCAS_BOT_SEL_SW_P1 + CAL_BRKT_EN_E2_P2 + CAL_BRKT_EN_E2_P1 + CAL_BRKT_EN_E1_P2 + CAL_BRKT_EN_E1_P1 + NULL_ADC_SEL_SW_P2
    + NULL_ADC_SEL_SW_P1 + TX_MON_SEL_SW_P2 + TX_MON_SEL_SW_P1 + RESERVED2 + PRIM_E1_TX_SEL_SW + SEC_E2_TX_SEL_SW + CAL_FREQ_SEL + CAL_MODE + CAL_JITTER_DELAY)
    aterm.logMessage(1, "Procedure Ended")
    return(int(binCalConfig,2))

def extractCalPulses(aterm, json_response, customDir=None):
    aterm.logMessage(1, "Procedure Started")
    today = datetime.today()
    todayDate = today.strftime("%b-%d-%Y--%H-%M-%S")

    # Make new directory path to store csv files (it is assumed directory already exists)
    if customDir == None:
        csvPath = "C:\\Test Data\\TXD RF\\Calibration Pulses"
    else:
        csvPath = customDir

    # TX Nulling Pulse Data
    sampleNum    = ["Iteration"]
    p1Timestamps = ["P1 Timestamp"]
    p1Values     = ["P1 Nulling ADC Value"]
    p2Timestamps = ["P2 Timestamp"]
    p2Values     = ["P2 Nulling ADC Value"]

    sampleList = rf_cal_read_tx_null_fifo(aterm, 4, json_response)
    numSamples = int(len(sampleList) / 16)
    for i in range(0, numSamples):
        sample = sampleList[i * 16 : (i * 16) + 16]
        aterm.logMessage(1, str(i) + ": " + str(sample))
        sample = bin(int(sample, 16))[2:].zfill(64)
        if i < (16): # P1 samples 0-15, P2 16-31
            p1Timestamps.append(int(sample[5:16], 2))
            p1Values.append(int(sample[52:64], 2))
            sampleNum.append(i)
        else:
            p2Timestamps.append(int(sample[5:16], 2))
            p2Values.append(int(sample[52:64], 2))

    csvList = [sampleNum] + [p1Timestamps] + [p1Values] + [p2Timestamps] + [p2Values]
    csvList = zip(*csvList)
    csv_filename = csvPath + "\\TX Nulling Pulses_" + str(todayDate) + '.csv'
    with open(csv_filename, 'w', newline = '') as csvFile:
        writer = csv.writer(csvFile, delimiter = ',')
        for column in csvList:
            writer.writerow(column)

    # p1Mean = statistics.mean(p1Values[1:]) if len(p1Values[1:]) > 0 else None
    # p2Mean = statistics.mean(p2Values[1:]) if len(p2Values[1:]) > 0 else None

    # TX Monitor Pulse Data
    sampleTxNum     = ["Iteration"]
    p1TxTimestamps  = ["P1 Timestamp"]
    p1TxMagValues   = ["P1 TX Monitor Magnitude ADC"]
    p1TxPhaseValues = ["P1 TX Monitor Phase ADC"]
    p2TxTimestamps  = ["P2 Timestamp"]
    p2TxMagValues   = ["P2 TX Monitor Magnitude ADC"]
    p2TxPhaseValues = ["P2 TX Monitor Phase ADC"]

    sampleList = rf_cal_read_tx_mon_fifo(aterm, 4, json_response)
    numSamples = int(len(sampleList) / 16)
    for i in range(0, numSamples):
        sample = sampleList[i * 16 : (i * 16) + 16]
        aterm.logMessage(1, str(i) + ": " + str(sample))
        sample = bin(int(sample, 16))[2:].zfill(64)
        if i < (16): # P1 samples 0-15, P2 16-31
            p1TxTimestamps.append(int(sample[5:16], 2))
            p1TxPhaseValues.append(int(sample[36:56], 2))
            p1TxMagValues.append(int(sample[56:64], 2))
            sampleTxNum.append(i)
        else:
            p2TxTimestamps.append(int(sample[5:16], 2))
            p2TxPhaseValues.append(int(sample[36:56], 2))
            p2TxMagValues.append(int(sample[56:64], 2))

    csvList = [sampleTxNum] + [p1TxTimestamps] + [p1TxMagValues] + [p1TxPhaseValues] + [p2TxTimestamps] + [p2TxMagValues] + [p2TxPhaseValues]
    csvList = zip(*csvList)
    csv_filename = csvPath + "\\TX Monitor Pulses_" + str(todayDate) + '.csv'
    with open(csv_filename, 'w', newline = '') as csvFile:
        writer = csv.writer(csvFile, delimiter = ',')
        for column in csvList:
            writer.writerow(column)

    # p1TxMagMean = statistics.mean(p1TxMagValues[1:]) if len(p1TxMagValues[1:]) > 0 else None
    # p2TxMagMean = statistics.mean(p2TxMagValues[1:]) if len(p2TxMagValues[1:]) > 0 else None

    # p1TxPhaseMean = statistics.mean(p1TxPhaseValues[1:]) if len(p1TxPhaseValues[1:]) > 0 else None
    # p2TxPhaseMean = statistics.mean(p2TxPhaseValues[1:]) if len(p2TxPhaseValues[1:]) > 0 else None

    # RX Monitor Pulse Data
    sampleRxNum     = ["Iteration"]
    p1RxTimestamps  = ["P1 Timestamp"]
    p1RxMagValues   = ["P1 RX Monitor Magnitude ADC"]
    p1RxPhaseValues = ["P1 RX Monitor Phase ADC"]
    p2RxTimestamps  = ["P2 Timestamp"]
    p2RxMagValues   = ["P2 RX Monitor Magnitude ADC"]
    p2RxPhaseValues = ["P2 RX Monitor Phase ADC"]

    sampleList = rf_cal_read_rx_fifo(aterm, 4, json_response)
    numSamples = int(len(sampleList) / 16)
    for i in range(0, numSamples):
        sample = sampleList[i * 16 : (i * 16) + 16]
        aterm.logMessage(1, str(i) + ": " + str(sample))
        sample = bin(int(sample, 16))[2:].zfill(64)
        if i < (16): # P1 samples 0-15, P2 16-31
            p1RxTimestamps.append(int(sample[5:16], 2))
            p1RxPhaseValues.append(int(sample[36:56], 2))
            p1RxMagValues.append(int(sample[56:64], 2))
            sampleRxNum.append(i)
        else:
            p2RxTimestamps.append(int(sample[5:16], 2))
            p2RxPhaseValues.append(int(sample[36:56], 2))
            p2RxMagValues.append(int(sample[56:64], 2))

    csvList = [sampleRxNum] + [p1RxTimestamps] + [p1RxMagValues] + [p1RxPhaseValues] + [p2RxTimestamps] + [p2RxMagValues] + [p2RxPhaseValues]
    csvList = zip(*csvList)
    csv_filename = csvPath + "\\RX Monitor Pulses_" + str(todayDate) + '.csv'
    with open(csv_filename, 'w', newline = '') as csvFile:
        writer = csv.writer(csvFile, delimiter = ',')
        for column in csvList:
            writer.writerow(column)

    # p1RxMagMean = statistics.mean(p1RxMagValues[1:]) if len(p1RxMagValues[1:]) > 0 else None
    # p2RxMagMean = statistics.mean(p2RxMagValues[1:]) if len(p2RxMagValues[1:]) > 0 else None

    # p1RxPhaseMean = statistics.mean(p1RxPhaseValues[1:]) if len(p1RxPhaseValues[1:]) > 0 else None
    # p2RxPhaseMean = statistics.mean(p2RxPhaseValues[1:]) if len(p2RxPhaseValues[1:]) > 0 else None
    aterm.logMessage(1, "Procedure Ended")
    return (p1Timestamps[1:], p1Values[1:], p2Timestamps[1:], p2Values[1:], p1TxTimestamps[1:],
            p1TxMagValues[1:], p2TxMagValues[1:], p2TxTimestamps[1:], p1TxPhaseValues[1:],
            p2TxPhaseValues[1:], p1RxTimestamps[1:], p1RxMagValues[1:], p2RxTimestamps[1:],
            p2RxMagValues[1:], p1RxPhaseValues[1:], p2RxPhaseValues[1:])
    # return (p1Mean, p2Mean, p1TxMagMean, p2TxMagMean, p1TxPhaseMean, p2TxPhaseMean, p1RxMagMean, p2RxMagMean, p1RxPhaseMean, p2RxPhaseMean)


def setCustomCalConfig(aterm, top_antenna, cal_brkt_en_e1_p1, cal_brkt_en_e1_p2, cal_brkt_en_e2_p1, cal_brkt_en_e2_p2, high_power, P1_monitor, P2_monitor, TX_E1_Mode_SW, TX_E2_Mode_SW, Cal_Freq_Sel, Self_Test):
    aterm.logMessage(1, "Procedure Started")
    '''top_antenna:
        1, connect TX to top antenna
        0, connect TX to bot antenna
    cal_brkt_en_e1_p1:
        1: Enable P1 pulse on E1
        0: Disable P1 pulse on E1
    cal_brkt_en_e1_p2:
        1: Enable P2 pulse on E1
        0: Disable P2 pulse on E1
    cal_brkt_en_e2_p1:
        1: Enable P1 pulse on E2
        0: Disable P1 pulse on E2
    cal_brkt_en_e2_p2:
        1: Enable P2 pulse on E2
        0: Disable P2 pulse on E2
    high_power:
        1, activate high-power transmit monitor path on both P1 and P2.
        0, activate low-power transmit monitor path on both P1 and P2.
    P1_monitor:
        0: No connect transmit monitor for P1 pulse.
        1: Connect transmit monitor to E1 for P1 pulse.
        2: Connect transmit monitor to E2 for P1 pulse.
    P2_monitor:
        0: No connect transmit monitor for P2 pulse.
        1: Connect transmit monitor to E1 for P2 pulse.
        2: Connect transmit monitor to E2 for P2 pulse.
    TX_E1_Mode_SW:
        Select Bank 0-4 of the E1 transmit mode switch.
    TX_E2_Mode_SW:
        Select Bank 0-1 of the E2 transmit mode switch. 0 = TCAS mode, 1 = RX 1090MHz Calibration
    Cal_Freq_Sel:
        0: TX 1090MHz CAL_FREQ_SEL, Selects proper DDC montior for Calibration
        1: TX 1090MHz CAL_FREQ_SEL, Selects proper DDC montior for Calibration
    Self_Test:
        0: P1 and P2 pulse cycle
        1: SELF-TEST Loopback sequence'''
    if top_antenna == 1:
        aterm.logMessage(1, 'Custom Calibration for Top Antenna Updated:')
        aterm.logMessage(1, '   Default: ' + hex(0x0A55FFFF11150000))
    else:
        aterm.logMessage(1, 'Custom Calibration for Bottom Antenna Updated:')
        aterm.logMessage(1, '   Default: ' + hex(0x02AAFF3FD1150000))

    new_cfg_value = generate_configuration_register(aterm, top_antenna, cal_brkt_en_e1_p1, cal_brkt_en_e1_p2, cal_brkt_en_e2_p1, cal_brkt_en_e2_p2, high_power, P1_monitor, P2_monitor, TX_E1_Mode_SW, TX_E2_Mode_SW, Cal_Freq_Sel, Self_Test)
    aterm.logMessage(1, '   Updated: ' + hex(new_cfg_value))
    overwrite_cfg_register(aterm, 1, top_antenna, new_cfg_value)
    aterm.logMessage(1, "Procedure Ended")

def generateCalPulse(aterm, top_antenna, cal_brkt_en_e1_p1, cal_brkt_en_e1_p2, cal_brkt_en_e2_p1, cal_brkt_en_e2_p2, E1_P1_ws_dB, E1_P2_ws_dB, E2_P1_ws_dB, E2_P2_ws_dB, E1_P1_Phase, E1_P2_Phase, E2_P1_Phase, E2_P2_Phase, Frequency_MHz, DUC_MHz, LO_MHz, rfCalBurstSpacing, rfCalBurstNumber):
    aterm.logMessage(1, "Procedure Started")
    IF_Freq = DUC_MHz-(Frequency_MHz-LO_MHz)
    CalPhInc = generate_CAL_PHASE_INCR(aterm, IF_Freq, DUC_MHz)

    [P1E1, E1_P1_ws_dB_Actual, E1_P1_Phase_Actual] = generate_CAL_ATTEN_PHASE(aterm, E1_P1_ws_dB, E1_P1_Phase)
    [P1E2, E2_P1_ws_dB_Actual, E2_P1_Phase_Actual] = generate_CAL_ATTEN_PHASE(aterm, E2_P1_ws_dB, E2_P1_Phase)
    [P2E1, E1_P2_ws_dB_Actual, E1_P2_Phase_Actual] = generate_CAL_ATTEN_PHASE(aterm, E1_P2_ws_dB, E1_P2_Phase)
    [P2E2, E2_P2_ws_dB_Actual, E2_P2_Phase_Actual] = generate_CAL_ATTEN_PHASE(aterm, E2_P2_ws_dB, E2_P2_Phase)
    aterm.logMessage(1, 'TX Frequency: ' + str(Frequency_MHz) + 'MHz')
    if cal_brkt_en_e1_p1 == 1 or cal_brkt_en_e2_p1 == 1:
        aterm.logMessage(1, 'P1 Settings:')
        if cal_brkt_en_e1_p1 == 1:
            aterm.logMessage(1, '   E1 Attenuation = ' + str(round(E1_P1_ws_dB_Actual,2)) + 'dB, Phase = ' + str(round(E1_P1_Phase_Actual,2)) + 'deg. (' + hex(P1E1) + ')')
        if cal_brkt_en_e2_p1 == 1:
            aterm.logMessage(1, '   E2 Attenuation = ' + str(round(E2_P1_ws_dB_Actual,2)) + 'dB, Phase = ' + str(round(E2_P1_Phase_Actual,2)) + 'deg. (' + hex(P1E2) + ')')
    if cal_brkt_en_e1_p2 == 1 or cal_brkt_en_e2_p2 == 1:
        aterm.logMessage(1, 'P2 Settings:')
        if cal_brkt_en_e1_p2 == 1:
            aterm.logMessage(1, '   E1 Attenuation = ' + str(round(E1_P2_ws_dB_Actual,2)) + 'dB, Phase = ' + str(round(E1_P2_Phase_Actual,2)) + 'deg. (' + hex(P2E1) + ')')
        if cal_brkt_en_e2_p2 == 1:
            aterm.logMessage(1, '   E2 Attenuation = ' + str(round(E2_P2_ws_dB_Actual,2)) + 'dB, Phase = ' + str(round(E2_P2_Phase_Actual,2)) + 'deg. (' + hex(P2E2) + ')')
    # Command Transmission Sequence:
    json_response = run_rf_cal_loop(aterm, 4, top_antenna, P1E1, P1E2, P2E1, P2E2, CalPhInc, rfCalBurstSpacing, rfCalBurstNumber)
    aterm.logMessage(1, "Procedure Ended")
    return json_response
