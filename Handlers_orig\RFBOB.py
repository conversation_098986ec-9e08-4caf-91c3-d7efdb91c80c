'''Instrument class to control the RF switches and phase shifters
on the TXD RF Breakout Box 
    
Before using this class, check the following has been
done on the target board to ensure desired operation:

1. The FTDI chip's serial number on the board is programmed to RFBOB

'''

import sys
import time
import datetime
import ftd2xx as ftd

class RFBOB(object):
    def __init__(self, ate_rm):
        self.a = None
        self.c = None
        self.d = None
        self.log = ate_rm
        
    def connect(self):
        # Three ports needed, one for SPI, two for bitbang
        self.a = ftd.openEx(b'RFBOBA') # MPSEE port for SPI
        self.c = ftd.openEx(b'RFBOBC')
        self.d = ftd.openEx(b'RFBOBD')
        self.a.setBitMode(0xff,1)
        self.c.setBitMode(0xff,1)
        self.d.setBitMode(0xff,1)
        self.Cswitches = [0]
        self.Dswitches = [0]
        self.Abits = [0]
        print(bytes(self.Cswitches))
        self.a.write(bytes(self.Abits))
        self.Abits = [0]
        self.a.write(bytes(self.Abits))
        self.c.write(bytes(self.Cswitches))
        self.d.write(bytes(self.Dswitches))
        self.spi_init(self.a)

        self.log.logMessage(0, "Connected to RF Breakout Board")

    def disconnect(self):
        self.a.close()
        self.c.close()
        self.d.close()
        self.log.logMessage(2, "Disconnected from RF Breakout Board")

    ''' Private function to initialize SPI port
    NOTE: DO NOT USE OUTSIDE THIS FILE!'''
    def spi_init(self, handle):
        X=1
        LDAC = 0
        CLK = 1
        SYNC = 1
        CLR = 1
        DIN = 0
        port = [0]
        port[0] = CLK|(DIN<<1)|(LDAC<<2)|(SYNC<<3)|(CLR<<4)
        #print('CLK,DIN,LDAC,SYNC,CLR,TIME')
        #print(str(port[0]&1)+','+str((port[0]>>1)&1)+','+str((port[0]>>2)&1)+','+str((port[0]>>3)&1)+','+str((port[0]>>4)&1)+','+str(datetime.datetime.now()).split(':')[2])
        self.a.write(bytes(port))
        time.sleep(0.001)
        CMD = [1,1,1] #setup internal ref
        ADX = [1,1,1]
        BITS = [X,X,CMD[0],CMD[1],CMD[2],ADX[2],ADX[1],ADX[1],0,0,0,0,0,0,0,0,0,0,0,0,X,X,X,1] #set up bits for 0V (used in next write)
        self.writeDAC(CMD,ADX,BITS)
        CMD = [0,1,1] #write 0V bits to dac
        self.writeDAC(CMD,ADX,BITS)

    def writeDAC(self, CMD,ADX,BITS):
        LDAC = 0
        CLK = 1
        SYNC = 1
        CLR = 1
        DIN = 0
        port = [0]
        port[0] = CLK|(DIN<<1)|(LDAC<<2)|(SYNC<<3)|(CLR<<4)

        SYNC = 0
        port[0] = CLK|(DIN<<1)|(LDAC<<2)|(SYNC<<3)|(CLR<<4)
        #print(str(port[0]&1)+','+str((port[0]>>1)&1)+','+str((port[0]>>2)&1)+','+str((port[0]>>3)&1)+','+str((port[0]>>4)&1)+','+str(datetime.datetime.now().time()).split(':')[2])
        self.a.write(bytes(port))
        time.sleep(0.001)

        for i in range(24):
            CLK = 1
            DIN = BITS[i]
            port[0] = CLK|(DIN<<1)|(LDAC<<2)|(SYNC<<3)|(CLR<<4)
            #print(str(port[0]&1)+','+str((port[0]>>1)&1)+','+str((port[0]>>2)&1)+','+str((port[0]>>3)&1)+','+str((port[0]>>4)&1)+','+str(datetime.datetime.now().time()).split(':')[2])
            self.a.write(bytes(port))
            time.sleep(0.001)

            CLK = 0
            port[0] = CLK|(DIN<<1)|(LDAC<<2)|(SYNC<<3)|(CLR<<4)
            #print(str(port[0]&1)+','+str((port[0]>>1)&1)+','+str((port[0]>>2)&1)+','+str((port[0]>>3)&1)+','+str((port[0]>>4)&1)+','+str(datetime.datetime.now().time()).split(':')[2])
            self.a.write(bytes(port))
            time.sleep(0.001)

        SYNC = 1
        CLK = 1
        port[0] = CLK|(DIN<<1)|(LDAC<<2)|(SYNC<<3)|(CLR<<4)
        #print(str(port[0]&1)+','+str((port[0]>>1)&1)+','+str((port[0]>>2)&1)+','+str((port[0]>>3)&1)+','+str((port[0]>>4)&1)+','+str(datetime.datetime.now().time()).split(':')[2])
        self.a.write(bytes(port))
        time.sleep(0.001)

    
    ''' Write byte to control voltage 
    voltage=0-4095 DAC value
    channel either 0 (primary) or 1 (secondary)'''
    
    def setPhaseShiftDAC(self, voltage, channel):
        
        X = 1
        CMD = [0,1,1]
        ADX = [0,0,channel]
        
        BITS = [X,X,CMD[0],CMD[1],CMD[2],ADX[0],ADX[1],ADX[2],(voltage>>11)&1,(voltage>>10)&1,
        (voltage>>9)&1,(voltage>>8)&1,(voltage>>7)&1,(voltage>>6)&1,(voltage>>5)&1,(voltage>>4)&1,
        (voltage>>3)&1,(voltage>>2)&1,(voltage>>1)&1,voltage&1,X,X,X,X]
        
        self.writeDAC(CMD,ADX,BITS)

    ''' Set the state of the selected RF switch.
        There are 11 RF switches to choose from. 
        The below table maps the RF signal name to its integer form:
        *------------------------*------------------*
        |    RF SWITCH SIGNAL    | SWITCH INT VALUE |
        -------------------------*------------------*
        |   PRIM_TOP_BOT_SEL     |         0        |
        |   SEC_TOP_BOT_SEL      |         1        |
        |   SG_EN_N_0            |         2        |
        |   SG_EN_N_1            |         3        |
        |   SG_PRIM_TOP_BOT_SEL  |         4        |
        |   SG_SEC_TOP_BOT_SEL   |         5        |
        |   PRIM_ATTEN_EN        |         6        |
        |   SEC_ATTEN_EN         |         7        |
        |   SA_PRIM_SEC_SEL      |         8        |
        |   SA_FILT_EN           |         9        |
        |   AUX_PRIM_SEC_SEL     |         10       |
        *------------------------*------------------*
     '''
    def setSwitch(self, switch, setting):
        if (switch < 0 or switch > 10):
            raise ValueError("RF switch value must be between 0 and 10")
        if (setting < 0 or setting > 1):
            raise ValueError("Invalid RF switch setting: Must be 0(disable) or 1(enable)")
        if switch > 7:
            bitMask = 1 << (switch % 8)
            print(self.d.getDeviceInfo())
            if (setting == 0):
                self.Dswitches[0] &= (~bitMask)
            else:
                self.Dswitches[0] |= bitMask
            self.d.write(bytes(self.Dswitches))
        else:
            bitMask = 1 << switch
            print(self.c.getDeviceInfo())
            if (setting == 0):
                self.Cswitches[0] &= (~bitMask)
            else:
                self.Cswitches[0] |= bitMask
            self.c.write(bytes(self.Cswitches))
            
        self.log.logMessage(1, "Switch " + str(switch) + " set to " + str(setting))
        

    def messageStandard(self, message):
        self.log.logMessage(1, message)
