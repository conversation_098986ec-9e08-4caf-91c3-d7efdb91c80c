import time, os, csv
import numpy as np

def TCASCenterFreqPwr(aterm, CenterFrequency, CarrierPower, Sweep_Delay, <PERSON><PERSON>_Delay):
    ''' Return power at specified center frequency on spectrum analyzer'''
    aterm.logMessage(1, "Procedure Started")
    
    # Setup Spectrum Analyzer 
    aterm.instruments["SpecAn"].setDisplayLine(20)
    aterm.instruments["SpecAn"].enableDisplayLine()

    aterm.instruments["SpecAn"].MarkerMode("POSITION")

    aterm.instruments["SpecAn"].ResBandwidthSet(100, "khz")
    aterm.instruments["SpecAn"].VidBandwidthSet(100, "khz")
    aterm.instruments["SpecAn"].disableAverage()
    aterm.instruments["SpecAn"].setAttenuation(0)
    aterm.instruments["SpecAn"].sweepNumPoints(1001)
    aterm.instruments["SpecAn"].setRefLevel(0, "dBm")
    aterm.instruments["SpecAn"].TraceTypeSet("maxhold")
    aterm.instruments["SpecAn"].setDetector("POSITIVE")

    #aterm.instruments["SpecAn"].setExternalTrigger()
    #aterm.instruments["SpecAn"].setExternalTriggerSource("NEGATIVE")
    #aterm.instruments["SpecAn"].setExternalTriggerDelay(0.0000005)
    aterm.instruments["SpecAn"].SweepContSet('ON')

    ## 4.7.1.7
    delta = -4
    span = 8
    spec_dBm = 0
    aterm.instruments["SpecAn"].setDisplayLine(str(spec_dBm))
    SA_FREQ_MHZ_MIN = 1
    SA_FREQ_MHZ_MAX = 6700
    startFreq = min(max(CenterFrequency + delta, SA_FREQ_MHZ_MIN), SA_FREQ_MHZ_MAX)
    endFreq = max(min(startFreq + span, SA_FREQ_MHZ_MAX), SA_FREQ_MHZ_MIN)
    aterm.instruments["SpecAn"].setStartFreq(startFreq, "MHz")
    aterm.instruments["SpecAn"].setStopFreq(endFreq, "MHZ")
    time.sleep(Sweep_Delay)
    if (CarrierPower == []):
        time.sleep(Marker_Delay)
        # aterm.instruments["SpecAn"].basicWrite(':CALC:MARK:X ' + str(CenterFrequency) + 'MHz')
        measuredCenterFreq = float(aterm.instruments["SpecAn"].GetMaxPeakFreq()) / 1000000 ## Convert to MHz
        measuredCenterPWR =  float(aterm.instruments["SpecAn"].GetMaxPeakPower())
    else:
        print("USING " + str(CarrierPower) + ' AS CARRIER POWER AT ' + str(CenterFrequency) + 'MHz')
        measuredCenterFreq = CenterFrequency
        measuredCenterPWR = CarrierPower

    aterm.logMessage(1, "Procedure Ended")
    return [measuredCenterFreq, measuredCenterPWR]

def nextMeasurement(aterm, span, delta, spec, spec_dBm, CenterFrequency, measuredCenterFreq, measuredCenterPWR, Sweep_Delay, Marker_Delay):
    aterm.logMessage(1, "Procedure Started")
    SA_FREQ_MHZ_MIN = 1
    SA_FREQ_MHZ_MAX = 6700
    
    startFreq = min(max(CenterFrequency + delta, SA_FREQ_MHZ_MIN), SA_FREQ_MHZ_MAX)
    endFreq = max(min(startFreq + span, SA_FREQ_MHZ_MAX), SA_FREQ_MHZ_MIN)
    aterm.instruments["SpecAn"].setStartFreq(startFreq, "MHz")
    aterm.instruments["SpecAn"].setStopFreq(endFreq, "MHZ")
    aterm.instruments["SpecAn"].setDisplayLine(str(spec_dBm))
    time.sleep(Sweep_Delay + Marker_Delay)
    spec_freq = float(aterm.instruments["SpecAn"].GetMaxPeakFreq()) / 1000000 ## Convert to MHz
    spec_amp = float(aterm.instruments["SpecAn"].GetMaxPeakPower()) - float(measuredCenterPWR)
    aterm.logMessage(1, "Procedure Ended")
    return spec_amp

def DMESpectrumSetup(aterm, CenterFrequency):
    ''' Setup Analyzer for DME measurements'''
    aterm.logMessage(1, "Procedure Started")

    aterm.instruments["SpecAn"].ResBandwidthSet(510, "khz")
    aterm.instruments["SpecAn"].basicWrite("BAND:SHAP FLAT")
    aterm.instruments["SpecAn"].disableAverage()
    aterm.instruments["SpecAn"].setAttenuation(0)
    aterm.instruments["SpecAn"].sweepNumPoints(21)
    aterm.instruments["SpecAn"].TraceTypeSet("maxhold")
    aterm.instruments["SpecAn"].setDetector("POSITIVE")
    aterm.instruments["SpecAn"].SweepContSet('ON')
    aterm.instruments["SpecAn"].CenterFreqSet(CenterFrequency, "MHz")
    aterm.instruments["SpecAn"].SpanSet("8", "MHZ")
    aterm.logMessage(1, "Procedure Ended")

def TCASCenterFreqAccPwr(aterm, CenterFrequency):
    ''' Return power at specified center frequency on spectrum analyzer'''
    aterm.logMessage(1, "Procedure Started")
    # Setup Spectrum Analyzer 
    aterm.instruments["SpecAn"].MarkerMode("POSITION")

    #aterm.instruments["SpecAn"].ResBandwidthSet(1, "khz")
    #aterm.instruments["SpecAn"].VidBandwidthSet(1, "khz")
    aterm.instruments["SpecAn"].disableAverage()
    aterm.instruments["SpecAn"].TraceTypeSet("maxhold")
    aterm.instruments["SpecAn"].setDetector("POSITIVE")
    aterm.instruments["SpecAn"].SweepContSet('ON')

    aterm.instruments["SpecAn"].CenterFreqSet(CenterFrequency, "MHz")
    aterm.instruments["SpecAn"].SpanSet("0.5", "MHZ")
    #aterm.instruments["SpecAn"].basicWrite("SWE:TYPE FFT")
    time.sleep(10)

    measuredCenterPWR =  float(aterm.instruments["SpecAn"].GetMaxPeakPower())
    #measuredCenterFreq = float(aterm.instruments["SpecAn"].GetMaxPeakFreq()) / 1000000 ## Convert to MHz
    aterm.instruments["SpecAn"].basicWrite(":CALCulate:MARKer1:FCOunt:STATe ON")
    time.sleep(2)
    measuredCenterFreq = float(aterm.instruments["SpecAn"].basicQuery(":CALCulate:MARKer1:FCOunt:X?")) / 1000000
    for i in range(4):
        time.sleep(0.25)
        measuredCenterFreq += float(aterm.instruments["SpecAn"].basicQuery(":CALCulate:MARKer1:FCOunt:X?")) / 1000000
    
    measuredCenterFreq = measuredCenterFreq/5

    aterm.logMessage(1, "Procedure Ended")
    return [measuredCenterFreq, measuredCenterPWR]

#print trace on spectrum analyzer streen to file
def printTrace(aterm,file_name,FILE_NAME_PREFIX):
    aterm.logMessage(1, "Procedure Started")
    data = aterm.instruments["SpecAn"].basicQuery(":TRAC:DATA? TRACE1",False)
    TraceAmplitudes = list(map(float,data.split(',')))
    Fstart = int(float(aterm.instruments["SpecAn"].basicQuery(':FREQ:STAR?')))
    Fstop = int(float(aterm.instruments["SpecAn"].basicQuery(':FREQ:STOP?')))
    points = int(aterm.instruments["SpecAn"].basicQuery(':SWE:POIN?'))
    fstep = (Fstop-Fstart)/points
    TraceFrequencies = np.arange(Fstart, Fstop, fstep)
    parent_dir = 'C:/Test Data/TXD RF/TX Spectrum/' + FILE_NAME_PREFIX
    if not os.path.exists(parent_dir):
        os.mkdir(parent_dir)
    path = parent_dir
    if not os.path.exists(path):
        os.mkdir(path)
    os.chdir(path)
    with open(file_name,'w', newline='') as csvfile:
        writer = csv.writer(csvfile, delimiter = ',')
        rows = zip(TraceFrequencies,TraceAmplitudes)
        writer.writerows(rows)
    csvfile.close()
    aterm.logMessage(1, "Procedure Ended")
