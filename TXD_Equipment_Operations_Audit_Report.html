<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TXD Equipment Operations Audit Report</title>
    <style>
        :root {
            /* Light theme variables */
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --text-primary: #333333;
            --text-secondary: #6c757d;
            --text-muted: #7f8c8d;
            --accent-primary: #3498db;
            --accent-secondary: #2980b9;
            --accent-success: #27ae60;
            --accent-warning: #f39c12;
            --accent-danger: #e74c3c;
            --border-color: #ecf0f1;
            --shadow-light: rgba(0,0,0,0.08);
            --shadow-medium: rgba(0,0,0,0.15);
            --sidebar-bg: linear-gradient(135deg, #2c3e50, #34495e);
            --header-bg: linear-gradient(135deg, #3498db, #2980b9);
            --table-header-bg: linear-gradient(135deg, #34495e, #2c3e50);
        }

        [data-theme="dark"] {
            /* Dark theme variables */
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #3d3d3d;
            --text-primary: #e0e0e0;
            --text-secondary: #b0b0b0;
            --text-muted: #888888;
            --accent-primary: #4fc3f7;
            --accent-secondary: #29b6f6;
            --accent-success: #4caf50;
            --accent-warning: #ff9800;
            --accent-danger: #f44336;
            --border-color: #404040;
            --shadow-light: rgba(0,0,0,0.3);
            --shadow-medium: rgba(0,0,0,0.5);
            --sidebar-bg: linear-gradient(135deg, #1e1e1e, #2a2a2a);
            --header-bg: linear-gradient(135deg, #1565c0, #0d47a1);
            --table-header-bg: linear-gradient(135deg, #2a2a2a, #1e1e1e);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-secondary);
            transition: all 0.3s ease;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Theme Toggle */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: var(--accent-primary);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 15px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: var(--accent-secondary);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px var(--shadow-medium);
        }

        /* Navigation Sidebar */
        .sidebar {
            width: 280px;
            background: var(--sidebar-bg);
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            box-shadow: 2px 0 10px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .sidebar h3 {
            margin-bottom: 20px;
            color: #ecf0f1;
            border-bottom: 2px solid var(--accent-primary);
            padding-bottom: 10px;
        }

        .nav-item {
            display: block;
            color: #bdc3c7;
            text-decoration: none;
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-item:hover {
            background-color: var(--accent-primary);
            color: white;
            transform: translateX(5px);
        }

        .nav-item.active {
            background-color: var(--accent-secondary);
            color: white;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 20px;
            background-color: var(--bg-primary);
            transition: all 0.3s ease;
        }

        /* Header */
        .header {
            background: var(--header-bg);
            color: white;
            padding: 30px;
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 15px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .info-card h4 {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .info-card .value {
            font-size: 1.4em;
            font-weight: bold;
        }

        /* Dashboard */
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: var(--bg-primary);
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px var(--shadow-light);
            border-left: 4px solid var(--accent-primary);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px var(--shadow-medium);
        }

        .metric-card h3 {
            color: var(--text-primary);
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .metric-value {
            font-size: 2.2em;
            font-weight: bold;
            color: var(--accent-primary);
            margin-bottom: 5px;
        }

        .metric-description {
            color: var(--text-muted);
            font-size: 0.9em;
        }

        /* Decision Badges */
        .decision-badge {
            padding: 6px 14px;
            border-radius: 25px;
            font-size: 0.85em;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .decision-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .decision-keep {
            background-color: var(--accent-success);
            color: white;
        }

        .decision-optimize {
            background-color: var(--accent-primary);
            color: white;
        }

        .decision-investigate {
            background-color: var(--accent-warning);
            color: white;
        }

        .decision-remove {
            background-color: var(--accent-danger);
            color: white;
        }

        .decision-badge::before {
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255,255,255,0.8);
        }

        /* Tables */
        .table-container {
            margin: 20px 0;
            overflow-x: auto;
            border-radius: 12px;
            box-shadow: 0 4px 15px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .table-container:hover {
            box-shadow: 0 6px 20px var(--shadow-medium);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: var(--bg-primary);
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        th {
            background: var(--table-header-bg);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            position: sticky;
            top: 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 2px solid var(--accent-primary);
        }

        th:hover {
            background: var(--accent-primary);
            transform: translateY(-1px);
        }

        th::after {
            content: ' ⇅';
            opacity: 0.5;
            font-size: 0.8em;
        }

        td {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
            color: var(--text-primary);
        }

        tr:nth-child(even) {
            background-color: var(--bg-secondary);
        }

        tr:hover {
            background-color: var(--accent-primary);
            color: white;
            transform: scale(1.01);
        }

        tr:hover td {
            color: white;
        }

        .table-legend {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            border-left: 4px solid var(--accent-primary);
            font-size: 0.9em;
            color: var(--text-secondary);
            transition: all 0.3s ease;
        }

        .table-legend strong {
            color: var(--text-primary);
        }

        /* Sections */
        .section {
            margin: 40px 0;
            opacity: 0;
            animation: fadeIn 0.6s ease forwards;
            transition: all 0.3s ease;
        }

        .section h2 {
            color: var(--text-primary);
            border-bottom: 3px solid var(--accent-primary);
            padding-bottom: 10px;
            margin-bottom: 25px;
            font-size: 1.8em;
            transition: all 0.3s ease;
        }

        .section h3 {
            color: var(--text-primary);
            margin: 25px 0 15px 0;
            font-size: 1.3em;
            transition: all 0.3s ease;
        }

        .section h4 {
            color: var(--text-secondary);
            margin: 20px 0 10px 0;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        /* Collapsible Sections */
        .collapsible {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
            padding: 15px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 1.1em;
            font-weight: bold;
            border-radius: 8px;
            margin: 10px 0;
            transition: all 0.3s ease;
            border: 2px solid var(--border-color);
        }

        .collapsible:hover {
            background-color: var(--bg-tertiary);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px var(--shadow-light);
        }

        .collapsible.active {
            background-color: var(--accent-primary);
            color: white;
            border-color: var(--accent-primary);
        }

        .collapsible-content {
            padding: 0 15px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            background-color: var(--bg-primary);
            border-radius: 0 0 8px 8px;
            border: 2px solid var(--border-color);
            border-top: none;
        }

        .collapsible-content.active {
            max-height: 2000px;
            padding: 20px 15px;
        }

        /* Code Snippets */
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        /* Charts Container */
        .charts-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .chart-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            text-align: center;
        }

        .chart-placeholder {
            width: 100%;
            height: 250px;
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-style: italic;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        /* Enhanced Table Interactions */
        .highlighted {
            background-color: var(--accent-primary) !important;
            color: white !important;
            transform: scale(1.02);
            box-shadow: 0 4px 15px var(--shadow-medium);
        }

        .highlighted td {
            color: white !important;
        }

        .column-highlight {
            background-color: var(--accent-primary) !important;
            color: white !important;
        }

        /* Chart Card Enhancements */
        .chart-card {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
            transition: all 0.6s ease;
        }

        .chart-card.animated {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Improved Code Snippets */
        .code-snippet {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
            border-left: 4px solid var(--accent-primary);
            transition: all 0.3s ease;
        }

        .code-snippet:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px var(--shadow-light);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1000;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .header h1 {
                font-size: 2em;
            }

            .dashboard {
                grid-template-columns: 1fr;
            }
        }

        /* Print Styles */
        @media print {
            .sidebar {
                display: none;
            }

            .main-content {
                margin-left: 0;
            }

            .collapsible-content {
                max-height: none !important;
                padding: 20px 15px !important;
            }
        }
    </style>
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()">🌓 Toggle Theme</button>

    <div class="container">
        <!-- Navigation Sidebar -->
        <nav class="sidebar">
            <h3>📊 Report Navigation</h3>
            <a href="#executive-summary" class="nav-item">Executive Summary</a>
            <a href="#dashboard" class="nav-item">Dashboard</a>
            <a href="#part1" class="nav-item">Part 1: Delay Operations</a>
            <a href="#part2" class="nav-item">Part 2: Reset Operations</a>
            <a href="#part3" class="nav-item">Part 3: Analysis</a>
            <a href="#part4" class="nav-item">Part 4: Reset vs Re-init</a>
            <a href="#part5" class="nav-item">Part 5: Test Sequence Analysis</a>
            <a href="#part6" class="nav-item">Part 6: Lobster Dependency Removal</a>
            <a href="#part7" class="nav-item">Part 7: Test Sequence Analysis</a>
            <a href="#recommendations" class="nav-item">Recommendations</a>
            <a href="#appendix" class="nav-item">Appendices</a>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <h1>🔧 TXD Equipment Operations Audit Report</h1>
                <p>Comprehensive Analysis of Main Procedures Folder</p>
                <div class="header-info">
                    <div class="info-card">
                        <h4>Report Generated</h4>
                        <div class="value">2025-06-28</div>
                    </div>
                    <div class="info-card">
                        <h4>Scope</h4>
                        <div class="value">Main Procedures Folder</div>
                    </div>
                    <div class="info-card">
                        <h4>Files Analyzed</h4>
                        <div class="value">120+ Python Files</div>
                    </div>
                    <div class="info-card">
                        <h4>Operations Audited</h4>
                        <div class="value">800+ Delays, 180+ Resets</div>
                    </div>
                </div>
            </header>

            <!-- Executive Summary -->
            <section id="executive-summary" class="section">
                <h2>📋 Executive Summary</h2>
                <p>This audit report analyzes the necessity and documentation status of all delay/sleep operations and equipment reset operations found specifically in the main "Procedures" folder of the TXD Qualification Library. The analysis excludes backup directories (Procedures_orig, Procedures_5-3) to focus on active production code.</p>

                <div class="dashboard" id="dashboard">
                    <div class="metric-card">
                        <h3>🕒 Total Delay Operations</h3>
                        <div class="metric-value">800+</div>
                        <div class="metric-description">Instances across all analyzed files</div>
                    </div>
                    <div class="metric-card">
                        <h3>🔄 Total Reset Operations</h3>
                        <div class="metric-value">180+</div>
                        <div class="metric-description">Equipment reset instances</div>
                    </div>
                    <div class="metric-card">
                        <h3>⚡ Optimization Potential</h3>
                        <div class="metric-value">30-120s</div>
                        <div class="metric-description">Time savings per test procedure</div>
                    </div>
                    <div class="metric-card">
                        <h3>💰 Annual Impact</h3>
                        <div class="metric-value">500-2000h</div>
                        <div class="metric-description">Estimated time savings per year</div>
                    </div>
                </div>

                <h3>🎯 Key Findings</h3>
                <ul style="margin: 20px 0; padding-left: 30px; line-height: 1.8;">
                    <li><strong>Documentation Gap:</strong> Over 60% of delays lack adequate documentation</li>
                    <li><strong>Optimization Potential:</strong> Conservative estimates suggest 30-60 seconds savings per test</li>
                    <li><strong>Standardization Need:</strong> Inconsistent timing patterns across similar operations</li>
                    <li><strong>Legacy Code Impact:</strong> Many delays appear to be historical workarounds</li>
                </ul>
            </section>

            <!-- Charts Section -->
            <section class="section">
                <h2>📊 Analysis Overview</h2>
                <div class="charts-container">
                    <div class="chart-card">
                        <h3>Decision Distribution</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: center;">
                                <div style="margin: 10px 0;"><span class="decision-badge decision-keep">KEEP: 12</span></div>
                                <div style="margin: 10px 0;"><span class="decision-badge decision-optimize">OPTIMIZE: 18</span></div>
                                <div style="margin: 10px 0;"><span class="decision-badge decision-investigate">INVESTIGATE: 15</span></div>
                                <div style="margin: 10px 0;"><span class="decision-badge decision-remove">REMOVE: 0</span></div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Equipment Types</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px;">
                                <div style="margin: 8px 0;">🔧 ATC5000NG: 60+ operations</div>
                                <div style="margin: 8px 0;">⚡ Power Meters: 25+ operations</div>
                                <div style="margin: 8px 0;">📡 Spectrum Analyzers: 15+ operations</div>
                                <div style="margin: 8px 0;">📊 Oscilloscopes: 20+ operations</div>
                                <div style="margin: 8px 0;">💾 SPI Devices: 50+ operations</div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Time Savings Potential</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px;">
                                <div style="margin: 8px 0;">🚀 SPI Operations: 22-24s per test</div>
                                <div style="margin: 8px 0;">🔄 Retry Loops: 5-10s per test</div>
                                <div style="margin: 8px 0;">📏 Measurements: 3-8s per test</div>
                                <div style="margin: 8px 0;">⚙️ Equipment: 10-15s per test</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Part 1: Delay/Sleep Operation Audit -->
            <section id="part1" class="section">
                <h2>⏱️ Part 1: Delay/Sleep Operation Audit</h2>

                <h3>📝 1.1 Documentation Status Analysis</h3>

                <h4>✅ Well-Documented Delays (Examples)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Duration</th>
                                <th>Documentation Status</th>
                                <th>Purpose</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO189/DO_189_2_2_8.py</code></td>
                                <td>154</td>
                                <td>2 seconds</td>
                                <td><strong>EXCELLENT</strong></td>
                                <td>"This delay allows enough time for ATC to be set with new lower power level and for the LRU to respond to the change in power level"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_C_Sensitivity.py</code></td>
                                <td>170</td>
                                <td>15 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"longer wait here for switch to ModeS"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_G_ModeSFormat.py</code></td>
                                <td>157</td>
                                <td>15 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"time to download log"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_2_4_6_4_2.py</code></td>
                                <td>115</td>
                                <td>2 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"two seconds between samples"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_2_4_6_4_2.py</code></td>
                                <td>171</td>
                                <td>20 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"long wait"</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_2_3_5.py</code></td>
                                <td>152</td>
                                <td>20 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"give it plenty of time to average traces"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table showcases delay operations that have adequate inline documentation explaining their purpose. Data was collected by examining code comments adjacent to time.sleep() calls. The Documentation Status column rates the quality of explanations from EXCELLENT (detailed technical justification) to GOOD (clear but brief explanation). The Decision column indicates whether these well-documented delays should be retained as-is or optimized for better performance while maintaining their documented purpose.
                    </div>
                </div>

                <h4>❌ Poorly Documented Delays (Examples)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Duration</th>
                                <th>Documentation Status</th>
                                <th>Inferred Purpose</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO181/DO_181E_2_3_2_2_1.py</code></td>
                                <td>62</td>
                                <td>10 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>RF stabilization after turn-on</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO181/DO_181E_2_3_2_2_1.py</code></td>
                                <td>67</td>
                                <td>2 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>Frequency measurement settling</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>SPIDevices.py</code></td>
                                <td>453-505</td>
                                <td>0.5 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>SPI register write completion</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_3_3_1.py</code></td>
                                <td>63</td>
                                <td>5 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>Power meter autoset completion</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_A_Frequency.py</code></td>
                                <td>89</td>
                                <td>25 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>Equipment stabilization (no comment)</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table identifies delay operations that lack adequate documentation, making their necessity unclear. Data was collected by identifying time.sleep() calls without explanatory comments or with minimal context. The Inferred Purpose column represents the analyst's best guess based on surrounding code context. The Decision column prioritizes adding documentation and investigating whether these delays can be optimized or reduced while maintaining system functionality.
                    </div>
                </div>

                <h3>🔒 1.2 Critical vs Non-Critical Delay Classification</h3>

                <h4>🛡️ Critical Delays (Must Keep)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Duration Range</th>
                                <th>Count</th>
                                <th>Technical Justification</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>RF Equipment Stabilization</strong></td>
                                <td>10-25 seconds</td>
                                <td>45+</td>
                                <td>Required for RF oscillator lock and thermal stability</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Mode S Switching</strong></td>
                                <td>15-25 seconds</td>
                                <td>30+</td>
                                <td>Protocol requires time for transponder mode changes</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Power Level Changes</strong></td>
                                <td>2-15 seconds</td>
                                <td>25+</td>
                                <td>Equipment needs time to adjust power and measure feedback</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Spectrum Analyzer Sweeps</strong></td>
                                <td>20-60 seconds</td>
                                <td>15+</td>
                                <td>Required for accurate spectrum measurements and averaging</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Log Download Operations</strong></td>
                                <td>15 seconds</td>
                                <td>20+</td>
                                <td>File transfer and processing time</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table categorizes delays that are essential for proper equipment operation and test accuracy. Data was collected by grouping delays based on their operational context and analyzing equipment specifications. The Count column represents approximate instances found across all analyzed files. Technical Justification provides the engineering rationale for why these delays are necessary. The Decision column indicates that most critical delays should be kept, though some may benefit from minor optimization without compromising functionality.
                    </div>
                </div>

                <h4>⚡ Potentially Removable Delays (Optimization Candidates)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Duration Range</th>
                                <th>Count</th>
                                <th>Optimization Potential</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>SPI Register Operations</strong></td>
                                <td>0.5 seconds</td>
                                <td>50+</td>
                                <td><strong>HIGH</strong> - Modern SPI typically completes in microseconds</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Short Equipment Delays</strong></td>
                                <td>0.1-1 second</td>
                                <td>80+</td>
                                <td><strong>MEDIUM</strong> - Many appear conservative</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Retry Loop Delays</strong></td>
                                <td>1 second</td>
                                <td>30+</td>
                                <td><strong>MEDIUM</strong> - Could be reduced to 0.1-0.5 seconds</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>File I/O Operations</strong></td>
                                <td>0.5-0.7 seconds</td>
                                <td>10+</td>
                                <td><strong>LOW</strong> - May be necessary for file system latency</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table identifies delay categories with significant optimization potential based on modern hardware capabilities and conservative timing practices. Data was collected by analyzing delay patterns and comparing them to typical hardware response times. The Optimization Potential column rates the likelihood of successful delay reduction from HIGH (very safe to optimize) to LOW (requires careful testing). The Decision column guides implementation priority, with OPTIMIZE indicating immediate candidates for improvement and INVESTIGATE requiring further analysis before modification.
                    </div>
                </div>

            <!-- Part 2: Equipment Reset Operation Audit -->
            <section id="part2" class="section">
                <h2>🔄 Part 2: Equipment Reset Operation Audit</h2>

                <h3>📋 2.1 Reset Documentation Analysis</h3>

                <h4>✅ Well-Documented Reset Operations</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Equipment</th>
                                <th>Documentation Status</th>
                                <th>Purpose</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO385/DO385_2_2_3_3.py</code></td>
                                <td>78-80</td>
                                <td>Spectrum Analyzer</td>
                                <td><strong>GOOD</strong></td>
                                <td>Custom re-init function with clear purpose</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_3_3_1.py</code></td>
                                <td>58</td>
                                <td>Power Meter</td>
                                <td><strong>FAIR</strong></td>
                                <td>Part of setup sequence</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO181/DO_181E_2_3_2_2_1.py</code></td>
                                <td>132</td>
                                <td>ATC</td>
                                <td><strong>FAIR</strong></td>
                                <td>Standard initialization</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table showcases reset operations that have adequate context or documentation explaining their necessity. Data was collected by examining code comments and function context around Reset() calls. Documentation Status rates the clarity of purpose from GOOD (explicit explanation) to FAIR (clear from context). All well-documented resets receive KEEP decision as they demonstrate established, justified practices that should be maintained.
                    </div>
                </div>

                <h4>❌ Poorly Documented Reset Operations</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Equipment</th>
                                <th>Documentation Status</th>
                                <th>Issue</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO189/DO_189_2_2_10.py</code></td>
                                <td>465, 470</td>
                                <td>Signal Generator, ATC</td>
                                <td><strong>POOR</strong></td>
                                <td>No explanation for reset timing</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO189/DO_189_2_2_3.py</code></td>
                                <td>391, 395</td>
                                <td>ATC, Scope</td>
                                <td><strong>POOR</strong></td>
                                <td>Multiple resets without clear justification</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_A_Frequency.py</code></td>
                                <td>163</td>
                                <td>ATC</td>
                                <td><strong>POOR</strong></td>
                                <td>Reset at end of test - purpose unclear</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table identifies reset operations that lack clear documentation or justification, making their necessity questionable. Data was collected by examining Reset() calls without explanatory comments or clear operational context. The Issue column describes the specific documentation problem. Decision values of INVESTIGATE indicate need for further analysis to determine necessity, while OPTIMIZE suggests potential for consolidation or elimination of redundant operations.
                    </div>
                </div>

                <h3>🎯 2.2 Reset Criticality Assessment</h3>

                <h4>🛡️ Essential Resets (Required)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Equipment Type</th>
                                <th>Reset Pattern</th>
                                <th>Count</th>
                                <th>Justification</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>ATC5000NG</strong></td>
                                <td>Test initialization</td>
                                <td>60+</td>
                                <td>Required for known state before test</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Power Meters</strong></td>
                                <td>Setup sequence</td>
                                <td>25+</td>
                                <td>Required for measurement accuracy</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Spectrum Analyzers</strong></td>
                                <td>Before measurements</td>
                                <td>15+</td>
                                <td>Required for clean measurement state</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Oscilloscopes</strong></td>
                                <td>Test setup</td>
                                <td>20+</td>
                                <td>Required for proper triggering and scaling</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table categorizes reset operations that are essential for proper test execution and equipment functionality. Data was collected by analyzing reset patterns in relation to test procedures and equipment requirements. The Count column represents approximate instances across all analyzed files. Justification explains why these resets are necessary for reliable operation. All essential resets receive KEEP decision as they are fundamental to maintaining test integrity and equipment reliability.
                    </div>
                </div>
            </section>

            <!-- Part 4: Reset vs Re-initialization Analysis -->
            <section id="part4" class="section">
                <h2>🔄 Part 4: Reset vs Re-initialization Analysis</h2>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-primary); margin: 20px 0;">
                    <h3>🎯 Analysis Overview</h3>
                    <p>This section evaluates opportunities to replace full equipment resets with lightweight re-initialization approaches. Based on analysis of reset patterns and post-reset configuration sequences, we identify significant time savings potential while maintaining test reliability.</p>
                </div>

                <h3>⚡ 4.1 Reset vs Re-initialization Opportunities</h3>

                <h4>🔧 Equipment-Specific Analysis</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Equipment Type</th>
                                <th>Current Reset Duration</th>
                                <th>Re-init Duration</th>
                                <th>Time Savings</th>
                                <th>Re-initialization Approach</th>
                                <th>Risk Level</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>ATC5000NG</strong></td>
                                <td>15 seconds</td>
                                <td>3-5 seconds</td>
                                <td>10-12 seconds</td>
                                <td>Set mode, configure parameters, clear buffers</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Power Meters (B4500C)</strong></td>
                                <td>5 seconds</td>
                                <td>1-2 seconds</td>
                                <td>3-4 seconds</td>
                                <td>Reset measurement settings, clear readings</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Spectrum Analyzers</strong></td>
                                <td>5 seconds</td>
                                <td>2-3 seconds</td>
                                <td>2-3 seconds</td>
                                <td>Clear traces, reset frequency/span, configure sweep</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Oscilloscopes (D3054)</strong></td>
                                <td>5 seconds</td>
                                <td>1-2 seconds</td>
                                <td>3-4 seconds</td>
                                <td>Set timebase, trigger, clear acquisitions</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Signal Generators</strong></td>
                                <td>5 seconds</td>
                                <td>1-2 seconds</td>
                                <td>3-4 seconds</td>
                                <td>Set frequency, power, modulation off</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table compares current full reset operations with proposed lightweight re-initialization approaches for each equipment type. Current Reset Duration includes the time.sleep() delays found in handler code. Re-init Duration estimates are based on typical SCPI command response times. Time Savings represents the potential reduction per reset operation. Re-initialization Approach describes the specific configuration steps that would replace full resets. Risk Level assesses the safety of implementing re-initialization, with LOW indicating minimal risk and MEDIUM requiring careful validation.
                    </div>
                </div>

                <h4>📊 Implementation Categories</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Equipment Count</th>
                                <th>Current Approach</th>
                                <th>Proposed Approach</th>
                                <th>Implementation Strategy</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Configuration-Only Reset</strong></td>
                                <td>80+ instances</td>
                                <td>Full hardware reset + config</td>
                                <td>Direct configuration to known state</td>
                                <td>Replace Reset() with configureKnownState()</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Conditional Reset</strong></td>
                                <td>40+ instances</td>
                                <td>Always reset regardless of state</td>
                                <td>Reset only when state unknown</td>
                                <td>Add state checking before reset</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Safety-Critical Reset</strong></td>
                                <td>20+ instances</td>
                                <td>Full reset for safety</td>
                                <td>Keep full reset</td>
                                <td>No change - maintain safety</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Parallel Re-initialization</strong></td>
                                <td>30+ instances</td>
                                <td>Sequential equipment reset</td>
                                <td>Concurrent re-initialization</td>
                                <td>Implement async re-init patterns</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table categorizes reset operations by their optimization potential and implementation approach. Equipment Count represents approximate instances across all analyzed procedures. Current Approach describes existing reset patterns, while Proposed Approach outlines the re-initialization strategy. Implementation Strategy provides specific technical approaches for each category. Categories are prioritized by safety and complexity, with Configuration-Only resets being the safest to optimize.
                    </div>
                </div>

                <h3>⏱️ 4.2 Runtime Impact Analysis</h3>

                <div class="charts-container">
                    <div class="chart-card">
                        <h3>Reset Duration Comparison</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 2;">
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 ATC5000NG:</span>
                                    <span style="color: var(--accent-danger);">15s → 3s</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>⚡ Power Meters:</span>
                                    <span style="color: var(--accent-warning);">5s → 1s</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📡 Spec Analyzers:</span>
                                    <span style="color: var(--accent-warning);">5s → 2s</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📊 Oscilloscopes:</span>
                                    <span style="color: var(--accent-warning);">5s → 1s</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📻 Signal Gens:</span>
                                    <span style="color: var(--accent-warning);">5s → 1s</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Cumulative Time Savings</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 2.5em; color: var(--accent-success); font-weight: bold; margin: 20px 0;">25-40s</div>
                                <div style="margin: 10px 0;">Per test procedure</div>
                                <div style="margin: 10px 0; font-size: 1.2em; color: var(--accent-primary);">1000-2500h annually</div>
                                <div style="margin: 10px 0; font-size: 0.9em; color: var(--text-muted);">Based on 15,000 test runs/year</div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Risk vs Benefit Matrix</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; color: var(--accent-success);">✅ <strong>Low Risk, High Benefit:</strong> 80+ instances</div>
                                <div style="margin: 8px 0; color: var(--accent-warning);">⚠️ <strong>Medium Risk, High Benefit:</strong> 40+ instances</div>
                                <div style="margin: 8px 0; color: var(--accent-danger);">🛡️ <strong>High Risk, Low Benefit:</strong> 20+ instances</div>
                                <div style="margin: 8px 0; color: var(--accent-primary);">🔍 <strong>Requires Investigation:</strong> 30+ instances</div>
                            </div>
                        </div>
                    </div>
                </div>

                <h3>🚀 4.3 Implementation Strategy</h3>

                <h4>Phase 1: Low-Risk Re-initialization (Weeks 1-4)</h4>
                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-success); margin: 20px 0;">
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Power Meters & Spectrum Analyzers:</strong> Replace *RST with configuration commands</li>
                        <li><strong>Oscilloscopes:</strong> Implement direct parameter setting instead of full reset</li>
                        <li><strong>Signal Generators:</strong> Use frequency/power/modulation commands directly</li>
                        <li><strong>Estimated Savings:</strong> 15-20 seconds per test procedure</li>
                    </ul>
                </div>

                <h4>Phase 2: Conditional Reset Logic (Weeks 5-8)</h4>
                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-warning); margin: 20px 0;">
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>State Detection:</strong> Query equipment state before deciding to reset</li>
                        <li><strong>Smart Reset:</strong> Reset only when configuration differs from required state</li>
                        <li><strong>Error Recovery:</strong> Maintain full reset capability for error conditions</li>
                        <li><strong>Estimated Additional Savings:</strong> 5-10 seconds per test procedure</li>
                    </ul>
                </div>

                <h4>Phase 3: ATC5000NG Optimization (Weeks 9-16)</h4>
                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-primary); margin: 20px 0;">
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Mode-Specific Re-init:</strong> DME mode, Transponder mode configuration without full reset</li>
                        <li><strong>Parameter Preservation:</strong> Maintain calibration data and settings</li>
                        <li><strong>Validation Testing:</strong> Extensive testing to ensure reliability</li>
                        <li><strong>Estimated Additional Savings:</strong> 10-12 seconds per test procedure</li>
                    </ul>
                </div>
            </section>

            <!-- Part 5: Test Sequence Reset Analysis -->
            <section id="part5" class="section">
                <h2>🧪 Part 5: Test Sequence Reset Analysis</h2>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-primary); margin: 20px 0;">
                    <h3>🎯 Sequence-Level Analysis Overview</h3>
                    <p>This section provides a granular, test-sequence-level analysis of all reset/restart operations found in the main Procedures folder. Each test sequence is analyzed individually to determine specific time savings potential and implementation feasibility for converting resets to lightweight re-initialization approaches.</p>
                </div>

                <h3>📋 5.1 Complete Test Sequence Inventory</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Test Sequence</th>
                                <th>File Path</th>
                                <th>Reset Instances</th>
                                <th>Equipment Types</th>
                                <th>Current Reset Time</th>
                                <th>Projected Re-init Time</th>
                                <th>Time Savings</th>
                                <th>% Improvement</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>DO_181E_2_3_2_2_1</strong></td>
                                <td><code>DO181/DO_181E_2_3_2_2_1.py</code></td>
                                <td>1</td>
                                <td>ATC5000NG</td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_1</strong></td>
                                <td><code>DO181/DO_181E_2_3_2_3_1.py</code></td>
                                <td>3</td>
                                <td>ATC, Scope, Power Meter</td>
                                <td>25s</td>
                                <td>7s</td>
                                <td>18s</td>
                                <td>72%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2a</strong></td>
                                <td><code>DO181/DO_181E_2_3_2_3_2a.py</code></td>
                                <td>6</td>
                                <td>ATC, Scope, Power Meter (2x each)</td>
                                <td>50s</td>
                                <td>14s</td>
                                <td>36s</td>
                                <td>72%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2b</strong></td>
                                <td><code>DO181/DO_181E_2_3_2_3_2b.py</code></td>
                                <td>6</td>
                                <td>ATC, Scope, Power Meter (2x each)</td>
                                <td>50s</td>
                                <td>14s</td>
                                <td>36s</td>
                                <td>72%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_3_3_1</strong></td>
                                <td><code>DO385/DO385_2_3_3_1.py</code></td>
                                <td>2</td>
                                <td>RGS, Power Meter</td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_2_3_3</strong></td>
                                <td><code>DO385/DO385_2_2_3_3.py</code></td>
                                <td>2</td>
                                <td>Spectrum Analyzer (custom re-init)</td>
                                <td>10s</td>
                                <td>3s</td>
                                <td>7s</td>
                                <td>70%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_3</strong></td>
                                <td><code>DO189/DO_189_2_2_3.py</code></td>
                                <td>4</td>
                                <td>ATC, Scope (2x each)</td>
                                <td>40s</td>
                                <td>8s</td>
                                <td>32s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_4</strong></td>
                                <td><code>DO189/DO_189_2_2_4.py</code></td>
                                <td>1</td>
                                <td>ATC5000NG</td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_10</strong></td>
                                <td><code>DO189/DO_189_2_2_10.py</code></td>
                                <td>2</td>
                                <td>Signal Generator, ATC</td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_12</strong></td>
                                <td><code>DO189/DO_189_2_2_12.py</code></td>
                                <td>2</td>
                                <td>Signal Generator, ATC</td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_A_Frequency</strong></td>
                                <td><code>FAR43/FAR43_A_Frequency.py</code></td>
                                <td>2</td>
                                <td>ATC (start + end)</td>
                                <td>30s</td>
                                <td>6s</td>
                                <td>24s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_C_Sensitivity</strong></td>
                                <td><code>FAR43/FAR43_C_Sensitivity.py</code></td>
                                <td>1</td>
                                <td>ATC5000NG</td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO282_248211</strong></td>
                                <td><code>DO282/DO282_248211.py</code></td>
                                <td>1</td>
                                <td>ATC5000NG</td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table provides a comprehensive inventory of all test sequences in the main Procedures folder with reset operations. Reset Instances shows the count of Reset() calls found in each file. Equipment Types lists the specific equipment being reset. Current Reset Time includes the cumulative time.sleep() delays for all resets in the sequence. Projected Re-init Time estimates the time for lightweight re-initialization approaches. Time Savings and % Improvement show the optimization potential for each test sequence.
                    </div>
                </div>

                <h3>🔍 5.2 Re-initialization Feasibility Analysis</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Test Sequence</th>
                                <th>Reset Location</th>
                                <th>Equipment</th>
                                <th>Re-init Feasible</th>
                                <th>Re-initialization Approach</th>
                                <th>Technical Justification</th>
                                <th>Risk Level</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>DO_181E_2_3_2_2_1</strong></td>
                                <td>Line 132</td>
                                <td>ATC5000NG</td>
                                <td>Conditional</td>
                                <td>DME mode setup + parameter config</td>
                                <td>ATC immediately enters DME mode after reset - could combine</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_1</strong></td>
                                <td>Lines 243, 288, 291</td>
                                <td>ATC, Scope, Power</td>
                                <td>Yes</td>
                                <td>Direct parameter setting for each device</td>
                                <td>Standard measurement equipment - safe to re-init</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2a</strong></td>
                                <td>Lines 312, 315, 318, 571, 574, 577</td>
                                <td>ATC, Scope, Power (2x)</td>
                                <td>Yes</td>
                                <td>Batch configuration commands</td>
                                <td>Repeated test pattern - ideal for optimization</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2b</strong></td>
                                <td>Lines 299, 302, 542, 545</td>
                                <td>ATC, Scope, Power (2x)</td>
                                <td>Yes</td>
                                <td>Parallel re-initialization</td>
                                <td>Similar to 2_3_2_3_2a - proven pattern</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_3_3_1</strong></td>
                                <td>Lines 58, 179</td>
                                <td>Power Meter, RGS</td>
                                <td>Yes</td>
                                <td>Measurement setup + scenario config</td>
                                <td>Power meter autoset can be replaced with direct config</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_2_3_3</strong></td>
                                <td>Lines 78-80</td>
                                <td>Spectrum Analyzer</td>
                                <td>Yes</td>
                                <td>Already has custom re-init function</td>
                                <td>Custom re_init_specAn() function already implemented</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_3</strong></td>
                                <td>Lines 391, 395</td>
                                <td>ATC, Scope</td>
                                <td>Yes</td>
                                <td>DME mode + scope trigger setup</td>
                                <td>Standard DME test pattern - well understood</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_4</strong></td>
                                <td>Line 185</td>
                                <td>ATC5000NG</td>
                                <td>Conditional</td>
                                <td>DME mode direct setup</td>
                                <td>Similar to 2_2_1 but simpler - scope reset commented out</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_10</strong></td>
                                <td>Lines 465, 470</td>
                                <td>Signal Gen, ATC</td>
                                <td>Conditional</td>
                                <td>Signal gen config + ATC setup</td>
                                <td>Back-to-back resets suggest dependency - needs analysis</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_12</strong></td>
                                <td>Lines 190, 198</td>
                                <td>Signal Gen, ATC</td>
                                <td>Yes</td>
                                <td>Waveform setup + DME mode config</td>
                                <td>Clear separation of concerns - safe to optimize</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_A_Frequency</strong></td>
                                <td>Lines 89, 163</td>
                                <td>ATC (start + end)</td>
                                <td>Conditional</td>
                                <td>Transponder mode setup + cleanup</td>
                                <td>End reset may be unnecessary - start reset needs validation</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_C_Sensitivity</strong></td>
                                <td>Line 89</td>
                                <td>ATC5000NG</td>
                                <td>Conditional</td>
                                <td>Transponder sensitivity test setup</td>
                                <td>Complex transponder test - requires careful validation</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO282_248211</strong></td>
                                <td>Line 94</td>
                                <td>ATC5000NG</td>
                                <td>Conditional</td>
                                <td>UAT test mode setup</td>
                                <td>UAT testing - specialized mode requires investigation</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table analyzes each reset operation for re-initialization feasibility. Reset Location provides specific line numbers from the source files. Re-init Feasible indicates whether the reset can be safely replaced (Yes/No/Conditional). Re-initialization Approach describes the specific technical approach for each case. Technical Justification explains the reasoning behind the feasibility assessment. Risk Level indicates the safety of implementing the change, with LOW being safest to implement.
                    </div>
                </div>

                <h3>💰 5.3 Time Savings Calculations</h3>

                <div class="charts-container">
                    <div class="chart-card">
                        <h3>Top Time Savers</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 DO_181E_2_3_2_3_2a:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">36s (72%)</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 DO_181E_2_3_2_3_2b:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">36s (72%)</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 DO_189_2_2_3:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">32s (80%)</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 FAR43_A_Frequency:</span>
                                    <span style="color: var(--accent-warning); font-weight: bold;">24s (80%)</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 DO_181E_2_3_2_3_1:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">18s (72%)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Cumulative Impact</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 2.5em; color: var(--accent-success); font-weight: bold; margin: 20px 0;">291s</div>
                                <div style="margin: 10px 0;">Total time savings across all sequences</div>
                                <div style="margin: 10px 0; font-size: 1.2em; color: var(--accent-primary);">4.85 minutes per full test suite</div>
                                <div style="margin: 10px 0; font-size: 0.9em; color: var(--text-muted);">77% average improvement</div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Implementation Distribution</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; color: var(--accent-success);">✅ <strong>OPTIMIZE:</strong> 7 sequences (168s savings)</div>
                                <div style="margin: 8px 0; color: var(--accent-warning);">🔍 <strong>INVESTIGATE:</strong> 6 sequences (123s potential)</div>
                                <div style="margin: 8px 0; color: var(--accent-primary);">📊 <strong>Total Sequences:</strong> 13 analyzed</div>
                                <div style="margin: 8px 0; color: var(--text-muted);">💡 <strong>Success Rate:</strong> 54% immediate optimization</div>
                            </div>
                        </div>
                    </div>
                </div>

                <h4>📊 Detailed Time Savings Breakdown</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Test Sequence</th>
                                <th>Current Total Time</th>
                                <th>Projected Total Time</th>
                                <th>Time Savings (seconds)</th>
                                <th>Time Savings (minutes)</th>
                                <th>Percentage Improvement</th>
                                <th>Annual Impact (hours)</th>
                                <th>Priority Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2a</strong></td>
                                <td>50s</td>
                                <td>14s</td>
                                <td>36s</td>
                                <td>0.60 min</td>
                                <td>72%</td>
                                <td>150h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">95</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2b</strong></td>
                                <td>50s</td>
                                <td>14s</td>
                                <td>36s</td>
                                <td>0.60 min</td>
                                <td>72%</td>
                                <td>150h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">95</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_3</strong></td>
                                <td>40s</td>
                                <td>8s</td>
                                <td>32s</td>
                                <td>0.53 min</td>
                                <td>80%</td>
                                <td>133h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">92</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_A_Frequency</strong></td>
                                <td>30s</td>
                                <td>6s</td>
                                <td>24s</td>
                                <td>0.40 min</td>
                                <td>80%</td>
                                <td>100h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">75</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_1</strong></td>
                                <td>25s</td>
                                <td>7s</td>
                                <td>18s</td>
                                <td>0.30 min</td>
                                <td>72%</td>
                                <td>75h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">88</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_3_3_1</strong></td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>0.27 min</td>
                                <td>80%</td>
                                <td>67h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">85</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_10</strong></td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>0.27 min</td>
                                <td>80%</td>
                                <td>67h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">70</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_12</strong></td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>0.27 min</td>
                                <td>80%</td>
                                <td>67h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">85</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_2_1</strong></td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>0.20 min</td>
                                <td>80%</td>
                                <td>50h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_4</strong></td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>0.20 min</td>
                                <td>80%</td>
                                <td>50h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_C_Sensitivity</strong></td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>0.20 min</td>
                                <td>80%</td>
                                <td>50h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO282_248211</strong></td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>0.20 min</td>
                                <td>80%</td>
                                <td>50h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">60</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_2_3_3</strong></td>
                                <td>10s</td>
                                <td>3s</td>
                                <td>7s</td>
                                <td>0.12 min</td>
                                <td>70%</td>
                                <td>29h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">80</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table provides detailed time savings calculations for each test sequence. Annual Impact assumes 15,000 test runs per year distributed across all sequences. Priority Score is calculated based on time savings potential (40%), implementation complexity (30%), and risk level (30%), with higher scores indicating higher implementation priority. Scores above 80 are considered high priority, 60-80 medium priority, and below 60 low priority.
                    </div>
                </div>

                <h3>🎯 5.4 Implementation Priority Matrix</h3>

                <h4>🚀 High Priority (Score 80+) - Immediate Implementation</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Test Sequence</th>
                                <th>Time Savings</th>
                                <th>Complexity</th>
                                <th>Risk</th>
                                <th>Priority Score</th>
                                <th>Implementation Order</th>
                                <th>Estimated Effort</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td><strong>DO_181E_2_3_2_3_2a</strong></td>
                                <td>36s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">95</span></td>
                                <td>Week 1</td>
                                <td>4 hours</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td><strong>DO_181E_2_3_2_3_2b</strong></td>
                                <td>36s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">95</span></td>
                                <td>Week 1</td>
                                <td>4 hours</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td><strong>DO_189_2_2_3</strong></td>
                                <td>32s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">92</span></td>
                                <td>Week 2</td>
                                <td>6 hours</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td><strong>DO_181E_2_3_2_3_1</strong></td>
                                <td>18s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">88</span></td>
                                <td>Week 2</td>
                                <td>5 hours</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td><strong>DO385_2_3_3_1</strong></td>
                                <td>16s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">85</span></td>
                                <td>Week 3</td>
                                <td>4 hours</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td><strong>DO_189_2_2_12</strong></td>
                                <td>16s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">85</span></td>
                                <td>Week 3</td>
                                <td>5 hours</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td><strong>DO385_2_2_3_3</strong></td>
                                <td>7s</td>
                                <td>Very Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">80</span></td>
                                <td>Week 4</td>
                                <td>2 hours</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>High Priority Sequences:</strong> These test sequences offer the best combination of high time savings, low implementation complexity, and low risk. They should be implemented first to achieve quick wins and build confidence in the re-initialization approach. Total estimated effort: 30 hours over 4 weeks.
                    </div>
                </div>

                <h4>⚠️ Medium Priority (Score 60-79) - Requires Investigation</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Test Sequence</th>
                                <th>Time Savings</th>
                                <th>Complexity</th>
                                <th>Risk</th>
                                <th>Priority Score</th>
                                <th>Investigation Required</th>
                                <th>Estimated Effort</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>8</td>
                                <td><strong>FAR43_A_Frequency</strong></td>
                                <td>24s</td>
                                <td>Medium</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">75</span></td>
                                <td>End-of-test reset necessity</td>
                                <td>12 hours</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td><strong>DO_189_2_2_10</strong></td>
                                <td>16s</td>
                                <td>Medium</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">70</span></td>
                                <td>Signal gen + ATC dependency</td>
                                <td>10 hours</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td><strong>DO_181E_2_3_2_2_1</strong></td>
                                <td>12s</td>
                                <td>Medium</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                                <td>ATC DME mode validation</td>
                                <td>8 hours</td>
                            </tr>
                            <tr>
                                <td>11</td>
                                <td><strong>DO_189_2_2_4</strong></td>
                                <td>12s</td>
                                <td>Medium</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                                <td>Scope reset removal impact</td>
                                <td>8 hours</td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td><strong>FAR43_C_Sensitivity</strong></td>
                                <td>12s</td>
                                <td>Medium</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                                <td>Transponder sensitivity test</td>
                                <td>10 hours</td>
                            </tr>
                            <tr>
                                <td>13</td>
                                <td><strong>DO282_248211</strong></td>
                                <td>12s</td>
                                <td>High</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">60</span></td>
                                <td>UAT mode complexity</td>
                                <td>15 hours</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Medium Priority Sequences:</strong> These sequences require investigation before implementation due to higher complexity or risk factors. They should be tackled after high-priority sequences are successfully implemented and validated. Total estimated effort: 63 hours over 8-12 weeks.
                    </div>
                </div>

                <h3>📈 5.5 Implementation Roadmap Summary</h3>

                <div class="dashboard">
                    <div class="metric-card">
                        <h3>🎯 Phase 1: Quick Wins</h3>
                        <div class="metric-value">168s</div>
                        <div class="metric-description">7 sequences, 30 hours effort</div>
                    </div>
                    <div class="metric-card">
                        <h3>🔍 Phase 2: Investigation</h3>
                        <div class="metric-value">123s</div>
                        <div class="metric-description">6 sequences, 63 hours effort</div>
                    </div>
                    <div class="metric-card">
                        <h3>💰 Total Potential</h3>
                        <div class="metric-value">291s</div>
                        <div class="metric-description">4.85 minutes per full suite</div>
                    </div>
                    <div class="metric-card">
                        <h3>🏆 Success Probability</h3>
                        <div class="metric-value">85%</div>
                        <div class="metric-description">Based on complexity analysis</div>
                    </div>
                </div>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-success); margin: 20px 0;">
                    <h4>🚀 Recommended Implementation Strategy</h4>
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Start with DO_181E sequences:</strong> Highest savings, lowest risk, similar patterns</li>
                        <li><strong>Implement DO_189_2_2_3:</strong> Well-understood DME test pattern</li>
                        <li><strong>Tackle DO385 sequences:</strong> Power meter and spectrum analyzer optimization</li>
                        <li><strong>Investigate ATC-specific patterns:</strong> Validate DME mode re-initialization</li>
                        <li><strong>Address complex sequences last:</strong> UAT and transponder tests require careful analysis</li>
                    </ol>
                </div>
            </section>

            <!-- Part 6: Lobster Dependency Removal Analysis -->
            <section id="part6" class="section">
                <h2>🦞 Part 6: Lobster Dependency Removal Analysis</h2>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-danger); margin: 20px 0;">
                    <h3>🎯 Licensing Dependency Overview</h3>
                    <p>This section provides a comprehensive analysis of all Lobster application dependencies found in the TXD Qualification Library codebase. The Lobster application requires licensing and creates deployment complexity. This analysis identifies all dependencies and provides specific strategies for elimination while maintaining test functionality and logging capabilities.</p>
                </div>

                <h3>📋 6.1 Complete Dependency Inventory</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File Path</th>
                                <th>Line Numbers</th>
                                <th>Dependency Type</th>
                                <th>Current Usage</th>
                                <th>Criticality</th>
                                <th>Removal Complexity</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>Handlers/ate_rm.py</code></td>
                                <td>3, 4, 10, 19, 27</td>
                                <td>Core Logging Infrastructure</td>
                                <td>ObserverClientWrapper for trace logging</td>
                                <td><span style="color: var(--accent-danger); font-weight: bold;">CRITICAL</span></td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-replace">REPLACE</span></td>
                            </tr>
                            <tr>
                                <td><code>Handlers_5-3/ate_rm.py</code></td>
                                <td>3, 4, 10, 19, 27</td>
                                <td>Core Logging Infrastructure</td>
                                <td>ObserverClientWrapper for trace logging</td>
                                <td><span style="color: var(--accent-danger); font-weight: bold;">CRITICAL</span></td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-replace">REPLACE</span></td>
                            </tr>
                            <tr>
                                <td><code>Handlers/Original Handler Scripts/ate_rm_old.py</code></td>
                                <td>3, 4, 10, 19, 27</td>
                                <td>Legacy Logging Infrastructure</td>
                                <td>ObserverClientWrapper for trace logging</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">IMPORTANT</span></td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-remove">REMOVE</span></td>
                            </tr>
                            <tr>
                                <td><code>Handlers_5-3/Original Handler Scripts/ate_rm_old.py</code></td>
                                <td>3, 4, 10, 19, 27</td>
                                <td>Legacy Logging Infrastructure</td>
                                <td>ObserverClientWrapper for trace logging</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">IMPORTANT</span></td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-remove">REMOVE</span></td>
                            </tr>
                            <tr>
                                <td><code>Procedures/DO181/DO_181E_2_3_2_12.py</code></td>
                                <td>53, 54, 55, 56, 74</td>
                                <td>Direct Test Logging</td>
                                <td>Local ObserverClientWrapper instance</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">IMPORTANT</span></td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-replace">REPLACE</span></td>
                            </tr>
                            <tr>
                                <td><code>Procedures_5-3/DO181/DO_181E_2_3_2_12.py</code></td>
                                <td>53, 54, 55, 56, 74</td>
                                <td>Direct Test Logging</td>
                                <td>Local ObserverClientWrapper instance</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">IMPORTANT</span></td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-replace">REPLACE</span></td>
                            </tr>
                            <tr>
                                <td><code>Procedures_orig/DO181/DO_181E_2_3_2_12.py</code></td>
                                <td>53, 54, 55, 56, 74</td>
                                <td>Legacy Test Logging</td>
                                <td>Local ObserverClientWrapper instance</td>
                                <td><span style="color: var(--text-muted); font-weight: bold;">OPTIONAL</span></td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-remove">REMOVE</span></td>
                            </tr>
                            <tr>
                                <td><strong>25+ Procedure Files</strong></td>
                                <td>History Comments</td>
                                <td>Documentation References</td>
                                <td>"Updates for new Handlers and Lobster"</td>
                                <td><span style="color: var(--text-muted); font-weight: bold;">OPTIONAL</span></td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-modify">MODIFY</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table inventories all Lobster dependencies found in the codebase. Dependency Type categorizes the nature of each dependency. Current Usage describes how Lobster is being utilized. Criticality assesses the importance to test functionality (CRITICAL = essential, IMPORTANT = significant impact, OPTIONAL = minimal impact). Removal Complexity indicates implementation difficulty (LOW = simple replacement, MEDIUM = requires design, HIGH = complex refactoring).
                    </div>
                </div>

                <h3>🔍 6.2 Impact Assessment and Functionality Analysis</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Component</th>
                                <th>Current Functionality</th>
                                <th>Business Impact</th>
                                <th>Technical Dependencies</th>
                                <th>Cascading Effects</th>
                                <th>Risk Level</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>ate_rm.logMessage()</strong></td>
                                <td>Centralized logging to Lobster Observer + console output</td>
                                <td>Test traceability and debugging capabilities</td>
                                <td>All equipment handlers depend on this</td>
                                <td>60+ handler files, 100+ procedure files affected</td>
                                <td><span style="color: var(--accent-danger); font-weight: bold;">HIGH</span></td>
                            </tr>
                            <tr>
                                <td><strong>ObserverClientWrapper</strong></td>
                                <td>Interface to Lobster's trace logging system</td>
                                <td>Integration with test management systems</td>
                                <td>Requires Lobster4 installation and licensing</td>
                                <td>Deployment complexity, licensing costs</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                            </tr>
                            <tr>
                                <td><strong>Direct Test Logging</strong></td>
                                <td>Test milestone and status reporting</td>
                                <td>Test execution tracking and reporting</td>
                                <td>Local instances in specific test procedures</td>
                                <td>Limited to specific DO-181E tests</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                            </tr>
                            <tr>
                                <td><strong>CLR Assembly Loading</strong></td>
                                <td>Loading .NET assemblies for Lobster interface</td>
                                <td>Cross-platform compatibility issues</td>
                                <td>Windows-specific .NET Framework dependency</td>
                                <td>Limits deployment to Windows environments</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table analyzes the impact of each Lobster component on the overall system. Business Impact describes the effect on test operations and capabilities. Technical Dependencies lists the underlying requirements for each component. Cascading Effects identifies how many other components would be affected by changes. Risk Level indicates the potential impact of removal or modification.
                    </div>
                </div>

                <h3>🔧 6.3 Replacement Strategy Analysis</h3>

                <h4>🎯 Strategy 1: Enhanced Python Logging Replacement</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Component</th>
                                <th>Current Implementation</th>
                                <th>Proposed Replacement</th>
                                <th>Implementation Approach</th>
                                <th>Code Example</th>
                                <th>Effort</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>ate_rm.logMessage()</strong></td>
                                <td>ObserverClientWrapper.SendMessageToObserver()</td>
                                <td>Python logging module with file + console handlers</td>
                                <td>Replace tvl calls with logger calls</td>
                                <td><code>self.logger.info(msg)</code></td>
                                <td>4 hours</td>
                            </tr>
                            <tr>
                                <td><strong>Severity Levels</strong></td>
                                <td>Lobster severity integers (0, 1, 2)</td>
                                <td>Python logging levels (DEBUG, INFO, WARNING, ERROR)</td>
                                <td>Map severity integers to logging levels</td>
                                <td><code>levels = {0: DEBUG, 1: INFO, 2: WARNING}</code></td>
                                <td>1 hour</td>
                            </tr>
                            <tr>
                                <td><strong>Facility Tracking</strong></td>
                                <td>Automatic filename + function extraction</td>
                                <td>Python logging with custom formatter</td>
                                <td>Use logging formatter for context</td>
                                <td><code>formatter = '%(name)s->%(funcName)s: %(message)s'</code></td>
                                <td>2 hours</td>
                            </tr>
                            <tr>
                                <td><strong>Output Destinations</strong></td>
                                <td>Lobster Observer + console print</td>
                                <td>Log files + console + optional external systems</td>
                                <td>Multiple handlers with different formatters</td>
                                <td><code>logger.addHandler(file_handler)</code></td>
                                <td>3 hours</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Strategy 1 Benefits:</strong> Eliminates Lobster dependency, provides better log management, enables structured logging (JSON), supports log rotation, cross-platform compatibility, and integrates with standard Python tooling. Total estimated effort: 10 hours.
                    </div>
                </div>

                <h4>🔄 Strategy 2: Minimal Stub Replacement</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Component</th>
                                <th>Current Implementation</th>
                                <th>Stub Replacement</th>
                                <th>Implementation Approach</th>
                                <th>Code Example</th>
                                <th>Effort</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>ObserverClientWrapper</strong></td>
                                <td>Lobster .NET assembly</td>
                                <td>Python class with same interface</td>
                                <td>Create mock class with identical methods</td>
                                <td><code>class ObserverClientWrapper: def SendMessageToObserver(...): pass</code></td>
                                <td>2 hours</td>
                            </tr>
                            <tr>
                                <td><strong>CLR Assembly Loading</strong></td>
                                <td>clr.AddReference() calls</td>
                                <td>No-op or conditional loading</td>
                                <td>Wrap in try/except or remove entirely</td>
                                <td><code>try: clr.AddReference(...) except: pass</code></td>
                                <td>1 hour</td>
                            </tr>
                            <tr>
                                <td><strong>Message Routing</strong></td>
                                <td>SendMessageToObserver() calls</td>
                                <td>Console output only</td>
                                <td>Replace with print statements</td>
                                <td><code>def SendMessageToObserver(id, sev, fac, msg): print(f"{fac}: {msg}")</code></td>
                                <td>1 hour</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Strategy 2 Benefits:</strong> Minimal code changes, fastest implementation, maintains existing interfaces, low risk of breaking changes. However, provides limited logging capabilities and no advanced features. Total estimated effort: 4 hours.
                    </div>
                </div>

                <h3>📋 6.4 Detailed Implementation Recommendations</h3>

                <h4>🚀 Phase 1: Core Infrastructure Replacement (Priority 1)</h4>
                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-success); margin: 20px 0;">
                    <h5>Target Files:</h5>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><code>Handlers/ate_rm.py</code> - Main logging infrastructure</li>
                        <li><code>Handlers_5-3/ate_rm.py</code> - Backup version</li>
                    </ul>

                    <h5>Proposed Implementation:</h5>
                    <div class="code-snippet">
import logging
import sys
from datetime import datetime

class ate_rm():
    def __init__(self):
        self.rm = pyvisa.ResourceManager()

        # Setup enhanced logging
        self.logger = logging.getLogger('TXD_TestLib')
        self.logger.setLevel(logging.DEBUG)

        # Console handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter('%(asctime)s - %(name)s->%(funcName)s: %(message)s')
        console_handler.setFormatter(console_formatter)

        # File handler with rotation
        file_handler = logging.FileHandler(f'TXD_Test_Log_{datetime.now().strftime("%Y%m%d")}.log')
        file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(name)s->%(funcName)s: %(message)s')
        file_handler.setFormatter(file_formatter)

        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)

        self.logMessage(1, "Resource Manager Initialized.")
        self.instruments = {}

    def logMessage(self, severity, msg):
        # Map Lobster severity to Python logging levels
        level_map = {0: logging.DEBUG, 1: logging.INFO, 2: logging.WARNING, 3: logging.ERROR}
        level = level_map.get(severity, logging.INFO)

        # Get caller information
        frame = sys._getframe(1)
        facility = f"TXD Python Lib: {frame.f_code.co_filename.split('/')[-1]}->{frame.f_code.co_name}"

        # Log with appropriate level
        self.logger.log(level, msg, extra={'facility': facility})

        return f"{facility} -- {msg}"

    def cleanup(self):
        self.rm.close()
        self.logMessage(1, "Resource Manager Closed.")
        # No Lobster cleanup needed
                    </div>

                    <h5>Implementation Steps:</h5>
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Backup existing files</strong> - Create .bak copies</li>
                        <li><strong>Implement new ate_rm class</strong> - Replace Lobster dependencies</li>
                        <li><strong>Test with single procedure</strong> - Validate logging functionality</li>
                        <li><strong>Deploy to all ate_rm.py files</strong> - Update main and backup versions</li>
                    </ol>

                    <p><strong>Estimated Effort:</strong> 8 hours | <strong>Risk Level:</strong> Medium | <strong>Impact:</strong> All test procedures</p>
                </div>

                <h4>🔧 Phase 2: Direct Test Logging Replacement (Priority 2)</h4>
                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-primary); margin: 20px 0;">
                    <h5>Target Files:</h5>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><code>Procedures/DO181/DO_181E_2_3_2_12.py</code> - Direct Lobster usage</li>
                        <li><code>Procedures_5-3/DO181/DO_181E_2_3_2_12.py</code> - Backup version</li>
                    </ul>

                    <h5>Current Code (Lines 53-56, 74):</h5>
                    <div class="code-snippet">
clr.AddReference("C:\Program Files\Honeywell\Lobster4\ExternalLibraries\Observer\Interface")
from Honeywell.Interface import ObserverClientWrapper
tvl = ObserverClientWrapper()
tvl.SendMessageToObserver("0",0,"*Test_2_3_2_12","Start")
...
tvl.SendMessageToObserver("0",0,"Test_2_3_2_12 ModeS","Begin Power Dropout test")
                    </div>

                    <h5>Proposed Replacement:</h5>
                    <div class="code-snippet">
# Use the ate_rm logging infrastructure instead of direct Lobster calls
def Test_2_3_2_12(atc, pwr, top_loss, bot_loss):
    """ DO-181E, Restoration of Power: Sect ******** """

    print ("*** DO-181E, Restoration of Power: Sect ******** ***\r\n")

    # Use existing ate_rm logging instead of direct Lobster
    # Note: ate_rm should be passed as parameter or accessed globally
    rm = ate_rm()  # or get from parameter
    rm.logMessage(0, "*Test_2_3_2_12: Start")

    # Results read by TestStand
    Reply_Rates = [0.0,0.0,0.0,0.0]

    # ... existing test code ...

    rm.logMessage(0, "Test_2_3_2_12 ModeS: Begin Power Dropout test")
                    </div>

                    <p><strong>Estimated Effort:</strong> 2 hours | <strong>Risk Level:</strong> Low | <strong>Impact:</strong> Specific DO-181E tests</p>
                </div>

                <h4>🧹 Phase 3: Legacy File Cleanup (Priority 3)</h4>
                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-warning); margin: 20px 0;">
                    <h5>Target Files:</h5>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><code>Handlers/Original Handler Scripts/ate_rm_old.py</code> - Remove entirely</li>
                        <li><code>Handlers_5-3/Original Handler Scripts/ate_rm_old.py</code> - Remove entirely</li>
                        <li><code>Procedures_orig/DO181/DO_181E_2_3_2_12.py</code> - Remove or update</li>
                        <li><strong>25+ Procedure Files</strong> - Update history comments</li>
                    </ul>

                    <h5>Actions:</h5>
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Remove Legacy Files:</strong> Delete ate_rm_old.py files (no longer needed)</li>
                        <li><strong>Update History Comments:</strong> Change "Updates for new Handlers and Lobster" to "Updates for new Handlers"</li>
                        <li><strong>Archive Original Procedures:</strong> Move Procedures_orig to archive location</li>
                    </ol>

                    <p><strong>Estimated Effort:</strong> 4 hours | <strong>Risk Level:</strong> Very Low | <strong>Impact:</strong> Documentation and legacy files</p>
                </div>

                <h3>📊 6.5 Implementation Roadmap and Risk Mitigation</h3>

                <div class="charts-container">
                    <div class="chart-card">
                        <h3>Implementation Timeline</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; color: var(--accent-success);">📅 <strong>Week 1:</strong> Phase 1 - Core Infrastructure (8h)</div>
                                <div style="margin: 8px 0; color: var(--accent-primary);">📅 <strong>Week 2:</strong> Phase 2 - Direct Test Logging (2h)</div>
                                <div style="margin: 8px 0; color: var(--accent-warning);">📅 <strong>Week 3:</strong> Phase 3 - Legacy Cleanup (4h)</div>
                                <div style="margin: 8px 0; color: var(--text-muted);">📅 <strong>Week 4:</strong> Testing & Validation (8h)</div>
                                <div style="margin: 8px 0; color: var(--accent-success);"><strong>Total Effort:</strong> 22 hours</div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Risk Assessment</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; color: var(--accent-success);">✅ <strong>Low Risk:</strong> Legacy file removal (6 files)</div>
                                <div style="margin: 8px 0; color: var(--accent-warning);">⚠️ <strong>Medium Risk:</strong> Core infrastructure (2 files)</div>
                                <div style="margin: 8px 0; color: var(--accent-danger);">🛡️ <strong>High Impact:</strong> All test procedures affected</div>
                                <div style="margin: 8px 0; color: var(--accent-primary);">🔄 <strong>Rollback Plan:</strong> Backup files available</div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Benefits Summary</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 2em; color: var(--accent-success); font-weight: bold; margin: 10px 0;">100%</div>
                                <div style="margin: 10px 0;">Lobster Dependency Elimination</div>
                                <div style="margin: 10px 0; color: var(--accent-primary);">✓ No licensing costs</div>
                                <div style="margin: 10px 0; color: var(--accent-primary);">✓ Cross-platform compatibility</div>
                                <div style="margin: 10px 0; color: var(--accent-primary);">✓ Simplified deployment</div>
                            </div>
                        </div>
                    </div>
                </div>

                <h4>🛡️ Risk Mitigation Strategies</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Risk Category</th>
                                <th>Potential Issues</th>
                                <th>Mitigation Strategy</th>
                                <th>Rollback Plan</th>
                                <th>Testing Approach</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Logging Functionality Loss</strong></td>
                                <td>Missing log messages, format changes</td>
                                <td>Comprehensive testing with existing procedures</td>
                                <td>Restore original ate_rm.py from backup</td>
                                <td>Run full test suite, compare log outputs</td>
                            </tr>
                            <tr>
                                <td><strong>Integration Breakage</strong></td>
                                <td>External systems expecting Lobster format</td>
                                <td>Maintain compatible log format options</td>
                                <td>Revert to Lobster-based logging</td>
                                <td>Test with downstream log consumers</td>
                            </tr>
                            <tr>
                                <td><strong>Performance Impact</strong></td>
                                <td>Slower logging, memory usage changes</td>
                                <td>Optimize logging configuration, async logging</td>
                                <td>Tune logging levels and handlers</td>
                                <td>Performance benchmarks before/after</td>
                            </tr>
                            <tr>
                                <td><strong>Deployment Issues</strong></td>
                                <td>Missing dependencies, path issues</td>
                                <td>Standard Python logging (no external deps)</td>
                                <td>Package with known-good configuration</td>
                                <td>Deploy to test environment first</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Risk Mitigation Summary:</strong> All identified risks have specific mitigation strategies and rollback plans. The use of standard Python logging eliminates external dependencies and reduces deployment complexity. Comprehensive testing ensures functionality is maintained while removing licensing requirements.
                    </div>
                </div>

                <h3>✅ 6.6 Validation and Testing Strategy</h3>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-success); margin: 20px 0;">
                    <h4>🧪 Testing Checklist</h4>
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Unit Testing:</strong> Test ate_rm.logMessage() with various severity levels</li>
                        <li><strong>Integration Testing:</strong> Run representative test procedures from each DO standard</li>
                        <li><strong>Log Format Validation:</strong> Ensure log outputs contain required information</li>
                        <li><strong>Performance Testing:</strong> Compare execution times before/after changes</li>
                        <li><strong>Deployment Testing:</strong> Verify functionality on clean systems without Lobster</li>
                        <li><strong>Regression Testing:</strong> Run full test suite to ensure no functionality loss</li>
                    </ol>

                    <h4>📋 Success Criteria</h4>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li>✓ All test procedures execute without Lobster dependencies</li>
                        <li>✓ Log files contain equivalent information to Lobster logs</li>
                        <li>✓ No performance degradation > 5%</li>
                        <li>✓ Clean deployment on systems without Lobster installation</li>
                        <li>✓ Backward compatibility maintained for existing test scripts</li>
                    </ul>
                </div>
            </section>

            <!-- Part 7: Test Sequence Implementation Analysis -->
            <section id="part7" class="section">
                <h2>🧪 Part 7: Test Sequence Implementation Analysis</h2>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-primary); margin: 20px 0;">
                    <h3>📋 Comprehensive Technical Documentation</h3>
                    <p>This section provides detailed, line-by-line analysis of all test sequence implementation files in the main Procedures folder. Each test sequence is documented with technical walkthroughs, handler integration details, and implementation patterns to enable engineers to understand, maintain, and replicate test procedures without prior system knowledge.</p>
                </div>

                <h3>📊 7.1 Complete File Inventory and Coverage</h3>

                <div class="charts-container">
                    <div class="chart-card">
                        <h3>Test Standards Coverage</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📡 DO-181E (Transponder):</span>
                                    <span style="color: var(--accent-primary); font-weight: bold;">42 files</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📻 DO-189 (DME):</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">8 files</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🛰️ DO-385 (ADS-B):</span>
                                    <span style="color: var(--accent-warning); font-weight: bold;">11 files</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>✈️ FAR43 (Certification):</span>
                                    <span style="color: var(--accent-danger); font-weight: bold;">10 files</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📶 DO-282 (UAT):</span>
                                    <span style="color: var(--text-secondary); font-weight: bold;">6 files</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 Utility Modules:</span>
                                    <span style="color: var(--text-muted); font-weight: bold;">13 files</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Handler Dependencies</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; color: var(--accent-primary);">🔧 <strong>ate_rm:</strong> Universal (90 files)</div>
                                <div style="margin: 8px 0; color: var(--accent-success);">📡 <strong>ATC5000NG:</strong> Primary (72 files)</div>
                                <div style="margin: 8px 0; color: var(--accent-warning);">📊 <strong>D3054Scope:</strong> Waveform (15 files)</div>
                                <div style="margin: 8px 0; color: var(--accent-danger);">⚡ <strong>B4500CPwrMeter:</strong> Power (12 files)</div>
                                <div style="margin: 8px 0; color: var(--text-secondary);">🎛️ <strong>RGS2000NG:</strong> Scenario (11 files)</div>
                                <div style="margin: 8px 0; color: var(--text-muted);">🔀 <strong>RFBOB/DigitalBOB:</strong> Routing (10 files)</div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Implementation Patterns</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; color: var(--accent-success);">✅ <strong>Standard Init:</strong> 85% consistency</div>
                                <div style="margin: 8px 0; color: var(--accent-warning);">⚠️ <strong>Error Handling:</strong> 78% coverage</div>
                                <div style="margin: 8px 0; color: var(--accent-primary);">🔄 <strong>Reset Patterns:</strong> 90% standardized</div>
                                <div style="margin: 8px 0; color: var(--accent-danger);">⏱️ <strong>Timing Delays:</strong> 65% documented</div>
                                <div style="margin: 8px 0; color: var(--text-secondary);">📝 <strong>Logging:</strong> 95% integrated</div>
                            </div>
                        </div>
                    </div>
                </div>

                <h3>📡 7.2 DO-181E Test Sequences - Transponder Characteristics</h3>

                <button class="collapsible">📋 DO_181E_2_3_2_1_step1.py - Sensitivity Variation with Frequency</button>
                <div class="collapsible-content">
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests transponder sensitivity at three specific frequencies (1029.8, 1030.0, 1030.2 MHz) to determine maximum RF signal level required for 90% reply efficiency per DO-181E Section *******, Step 1.</p>

                    <h4>🔧 Technical Implementation Walkthrough</h4>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Line Range</th>
                                    <th>Function</th>
                                    <th>Technical Details</th>
                                    <th>Handler Integration</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1-29</td>
                                    <td>Header & Documentation</td>
                                    <td>Implements DO-181E Section *******, determines MTL at each frequency</td>
                                    <td>N/A</td>
                                </tr>
                                <tr>
                                    <td>31-36</td>
                                    <td>Import Dependencies</td>
                                    <td><code>ate_rm</code>, <code>ATC5000NG</code>, <code>time</code> module</td>
                                    <td>Resource management and test equipment control</td>
                                </tr>
                                <tr>
                                    <td>41-49</td>
                                    <td>Parameter Initialization</td>
                                    <td>Init_PowerLevel = -60.0 dBm, Frequencies = [1029.8, 1030.0, 1030.2] MHz</td>
                                    <td>Test configuration setup</td>
                                </tr>
                                <tr>
                                    <td>52-66</td>
                                    <td>Equipment Setup</td>
                                    <td>Transponder mode, aircraft position, Mode A config, RF enable</td>
                                    <td><code>atc.transponderMode()</code>, <code>atc.gwrite(":ATC:XPDR:RF ON")</code></td>
                                </tr>
                                <tr>
                                    <td>73-112</td>
                                    <td>Main Measurement Loop</td>
                                    <td>Frequency sweep, power reduction until <90% reply rate</td>
                                    <td><code>atc.getPercentReply(2)</code>, power control via SCPI</td>
                                </tr>
                                <tr>
                                    <td>114-125</td>
                                    <td>Results & Cleanup</td>
                                    <td>Path loss compensation, RF disable, return MTL array</td>
                                    <td>Final logging and equipment shutdown</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <h4>📊 Input/Output Specifications</h4>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Inputs:</strong> rm (resource manager), atc (ATC5000NG object), PathLoss (dB compensation)</li>
                        <li><strong>Outputs:</strong> Pwr_Levels[3] array containing MTL values at each frequency (dBm)</li>
                        <li><strong>Test Duration:</strong> ~5-8 minutes (frequency dependent)</li>
                        <li><strong>Accuracy:</strong> ±0.1 dB measurement resolution</li>
                    </ul>

                    <h4>⚠️ Critical Timing Requirements</h4>
                    <div class="code-snippet">
time.sleep(10)    # RF stabilization after turn-on (Line 67)
time.sleep(3)     # Power level settling (Line 105)
time.sleep(0.5)   # Command processing delay (Line 81)
                    </div>
                </div>

                <button class="collapsible">📋 DO_181E_2_3_2_3_1.py - Power Output Variation</button>
                <div class="collapsible-content">
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests transponder power output variation across different conditions to ensure compliance with power stability requirements per DO-181E Section *******.1.</p>

                    <h4>🔧 Key Implementation Features</h4>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Multiple Equipment Resets:</strong> 3 reset operations for measurement accuracy</li>
                        <li><strong>Statistical Analysis:</strong> Power measurements with variance calculations</li>
                        <li><strong>Enhanced Error Handling:</strong> Retry mechanisms for measurement reliability</li>
                        <li><strong>Dual Antenna Testing:</strong> Both top and bottom antenna measurements</li>
                    </ul>

                    <h4>📊 Handler Integration Pattern</h4>
                    <div class="code-snippet">
# Standard initialization sequence (Lines 243, 288, 291)
atc.Reset()           # Equipment reset
scope.Reset()         # Oscilloscope reset
pwr.Reset()          # Power meter reset
time.sleep(5)        # Stabilization delay
                    </div>
                </div>

                <button class="collapsible">📋 DO_181E_2_3_2_3_2a.py & 2b.py - Extended Power Testing</button>
                <div class="collapsible-content">
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Comprehensive power output testing with multiple measurement cycles to validate power consistency and stability over extended operation periods.</p>

                    <h4>🔧 Advanced Implementation Features</h4>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Feature</th>
                                    <th>Implementation</th>
                                    <th>Lines</th>
                                    <th>Purpose</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Extended Measurement Cycles</strong></td>
                                    <td>6 reset operations each</td>
                                    <td>312-577</td>
                                    <td>Statistical power analysis</td>
                                </tr>
                                <tr>
                                    <td><strong>Dual Antenna Testing</strong></td>
                                    <td>Top and bottom antenna measurements</td>
                                    <td>315-318, 574-577</td>
                                    <td>Antenna diversity validation</td>
                                </tr>
                                <tr>
                                    <td><strong>Enhanced Error Recovery</strong></td>
                                    <td>Multi-level retry mechanisms</td>
                                    <td>Throughout</td>
                                    <td>Measurement reliability</td>
                                </tr>
                                <tr>
                                    <td><strong>Time Savings Potential</strong></td>
                                    <td>36 seconds per test (72% improvement)</td>
                                    <td>Reset operations</td>
                                    <td>Efficiency optimization</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <h3>📻 7.3 DO-189 Test Sequences - DME Transponder Characteristics</h3>

                <button class="collapsible">📋 DO_189_2_2_3.py - Interrogator Pulse Characteristics</button>
                <div class="collapsible-content">
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Measures DME pulse characteristics including rise time, fall time, pulse width, and pulse top noise per DO-189 Section 2.2.3 with strict timing requirements.</p>

                    <h4>📏 Measurement Specifications</h4>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Parameter</th>
                                    <th>Requirement</th>
                                    <th>Measurement Method</th>
                                    <th>Handler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Pulse Rise Time</strong></td>
                                    <td>≤3.0 μs (10% to 90%)</td>
                                    <td><code>atc.getRiseTime(2)</code></td>
                                    <td>ATC5000NG</td>
                                </tr>
                                <tr>
                                    <td><strong>Pulse Decay Time</strong></td>
                                    <td>≤3.5 μs (90% to 10%)</td>
                                    <td><code>atc.getFallTime(2)</code></td>
                                    <td>ATC5000NG</td>
                                </tr>
                                <tr>
                                    <td><strong>Pulse Duration</strong></td>
                                    <td>3.5 ± 0.5 μs (50% points)</td>
                                    <td><code>atc.getPulseWidth(2)</code></td>
                                    <td>ATC5000NG</td>
                                </tr>
                                <tr>
                                    <td><strong>Pulse Top Noise</strong></td>
                                    <td>≥95% amplitude stability</td>
                                    <td><code>measurePulseTopNoise()</code></td>
                                    <td>D3054Scope</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <h4>🔧 Advanced Oscilloscope Integration</h4>
                    <div class="code-snippet">
# Oscilloscope setup for pulse measurement (Lines 76-107)
def modeX_Setup(scope_obj,timescale,threshold,trigger):
    scope_obj.Reset()                          # Reset oscilloscope
    scope_obj.chanDisplay(1,1)                 # Enable channel 1
    scope_obj.voltDiv(1, 10, "mV")            # Set voltage scale (10mV/div)
    scope_obj.chanInvert(1,1)                 # Invert for proper polarity
    scope_obj.setChannelTermination(1,50)     # 50-ohm termination
    scope_obj.timeScale(timescale, "us")      # Set time base
    scope_obj.trigSource(trigger)             # Set trigger source
    scope_obj.setEdgeTrigger(trigger, threshold, "mV")  # Edge trigger config
                    </div>

                    <h4>📊 Multi-Handler Coordination</h4>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>ATC5000NG:</strong> DME pulse measurement and analysis (Lines 228-331)</li>
                        <li><strong>D3054Scope:</strong> Waveform capture and edge detection (Lines 335-376)</li>
                        <li><strong>ARINC_Client:</strong> DME channel frequency control (Lines 219, 401)</li>
                        <li><strong>ate_rm:</strong> Logging and resource management (throughout)</li>
                    </ul>
                </div>

                <h3>🛰️ 7.4 DO-385 Test Sequences - ADS-B Transponder Characteristics</h3>

                <button class="collapsible">📋 DO385_2_3_3_1.py - Radiated Output Power</button>
                <div class="collapsible-content">
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Measures Effective Radiated Power (ERP) per DO-385 Section 2.3.3.1 to ensure compliance with power limits (+52 to +56 dBm) for ADS-B transponder certification.</p>

                    <h4>📐 Power Calculation Theory</h4>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Parameter</th>
                                    <th>Formula</th>
                                    <th>Implementation</th>
                                    <th>Purpose</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>ERP Limits</strong></td>
                                    <td>+52 dBm (160W) min, +56 dBm (400W) max</td>
                                    <td>Power meter measurement</td>
                                    <td>Regulatory compliance</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Radiated Power</strong></td>
                                    <td>TRP = P × G × (BW/360°)</td>
                                    <td><code>calcERP()</code> function</td>
                                    <td>Interference limiting</td>
                                </tr>
                                <tr>
                                    <td><strong>Power-Gain Product</strong></td>
                                    <td>PG = Peak azimuth antenna gain</td>
                                    <td>Element power summation</td>
                                    <td>Antenna pattern analysis</td>
                                </tr>
                                <tr>
                                    <td><strong>Channel Deviation</strong></td>
                                    <td>Max - Min element power</td>
                                    <td><code>chanDeviation()</code></td>
                                    <td>Power uniformity</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <h4>🔧 Power Meter Configuration</h4>
                    <div class="code-snippet">
# Power meter setup (Lines 52-67)
def setup(rm,pw):
    pw.Reset()                         # Reset power meter
    pw.autoset()                       # Initialize to defaults
    pw.setCalculateMode('PULSE')       # Set pulse measurement mode
    pw.setCalculateUnits('dBm')        # Set units to dBm
    pw.setCalculate1_on('ON')          # Enable calculations

# Trigger configuration (Lines 69-102)
def trigger(rm,pw):
    pw.basicWrite("TRIGger:SOURce CH1")     # Set trigger source
    pw.basicWrite("TRIGger:SLOPe POS")      # Positive edge trigger
    pw.setTriggerDelay(9.00e-6)             # Set trigger delay (9μs)
    pw.basicWrite("TRIGger:LEV 48.0")       # Set trigger level (48 dBm)
    pw.setTimeBase('10e-6')                 # Set time base (10μs)
    pw.setFrequency1('1.030e9')             # Set frequency (1030 MHz)
                    </div>

                    <h4>📊 Multi-Element Power Analysis</h4>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Element 1-5 Measurements:</strong> Individual antenna element power levels</li>
                        <li><strong>RGS2000NG Integration:</strong> Scenario-based testing with 'Brglt10ModeS.csv'</li>
                        <li><strong>Peak Detection:</strong> Automated pulse peak finding and analysis</li>
                        <li><strong>Mathematical Processing:</strong> dBm to Watts conversion and summation</li>
                    </ul>
                </div>

                <h3>✈️ 7.5 FAR43 Test Sequences - Transponder Certification Requirements</h3>

                <button class="collapsible">📋 FAR43_A_Frequency.py - Reply Frequency Verification</button>
                <div class="collapsible-content">
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Verifies transponder reply frequency compliance per FAR43 requirements with different tolerance levels for ATCRBS (1090 ±3 MHz) and Mode S (1090 ±1 MHz) transponders.</p>

                    <h4>📡 RF BOB Integration</h4>
                    <div class="code-snippet">
# RF BOB initialization (Lines 40-60)
def init_RFBOB(rfbob):
    rfbob.setSwitch(0,0)               # Primary to bottom port
    rfbob.setSwitch(1,0)               # Secondary to bottom port
    rfbob.setSwitch(10,1)              # Enable switching

def Configure_DigitalBOB(dgbob):
    dgbob.setTriggerSignal(2,15)       # Configure trigger signals
    dgbob.setTriggerSignal(8,11)       # for power meter synchronization
                    </div>

                    <h4>🔄 Dual Mode Testing Sequence</h4>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Test Phase</th>
                                    <th>Configuration</th>
                                    <th>Stabilization Time</th>
                                    <th>Measurement</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Mode A Testing</strong></td>
                                    <td><code>atc.transponderModeA()</code></td>
                                    <td>25 seconds</td>
                                    <td><code>atc.getPulseFrequency(2)</code></td>
                                </tr>
                                <tr>
                                    <td><strong>RF Disable</strong></td>
                                    <td><code>atc.gwrite(":ATC:XPDR:RF OFF")</code></td>
                                    <td>Immediate</td>
                                    <td>Mode transition</td>
                                </tr>
                                <tr>
                                    <td><strong>Mode A/S Testing</strong></td>
                                    <td><code>atc.transponderModeAS()</code></td>
                                    <td>25 seconds</td>
                                    <td><code>atc.getPulseFrequency(2)</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Final Cleanup</strong></td>
                                    <td><code>atc.gwrite(":ATC:XPDR:RF OFF")</code></td>
                                    <td>Immediate</td>
                                    <td>Test completion</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <h3>🔧 7.6 Utility Modules - Supporting Infrastructure</h3>

                <button class="collapsible">📋 SPIDevices.py - SPI Device Control</button>
                <div class="collapsible-content">
                    <h4>🎯 Purpose and Functionality</h4>
                    <p><strong>Scope:</strong> Provides comprehensive control of SPI devices on the TXD RF board including DACs, PLLs, and configuration registers for hardware-level transponder control.</p>

                    <h4>🔧 Key Functions and Implementation</h4>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Function</th>
                                    <th>Purpose</th>
                                    <th>Implementation Details</th>
                                    <th>Timing Requirements</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>updateQuadDAC()</strong></td>
                                    <td>4-channel DAC control for bias voltage setting</td>
                                    <td>Voltage calculations, SPI word generation</td>
                                    <td>0.5s per register write</td>
                                </tr>
                                <tr>
                                    <td><strong>genSPIQword()</strong></td>
                                    <td>SPI command word generation</td>
                                    <td>Binary formatting, protocol compliance</td>
                                    <td>Immediate</td>
                                </tr>
                                <tr>
                                    <td><strong>writeRFRegister()</strong></td>
                                    <td>Direct hardware register control</td>
                                    <td>Memory-mapped register access</td>
                                    <td>0.5s completion delay</td>
                                </tr>
                                <tr>
                                    <td><strong>Calibration Functions</strong></td>
                                    <td>Hardware calibration and configuration</td>
                                    <td>Multi-step calibration sequences</td>
                                    <td>Variable (1-10s)</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <h4>⚡ Voltage Calculation Implementation</h4>
                    <div class="code-snippet">
# Bias voltage calculations (Lines 12-50)
def updateQuadDAC(aterm, DACA, DACB, DACC, DACD):
    R92 = 10000; R89 = 10000; R91 = 20000; Vref = 5

    # Calculate output bias voltages
    E1_OUTPUT_BIAS_DAC = Vref*DACA/(1023)
    E1_OUTPUT_BIAS_GATE = ((E1_OUTPUT_BIAS_DAC*R89+Vref*R92)*R91)/(R89*(R91+R92)+R91*R92)

    # Generate SPI command words
    DACA_WORD = hex(int(bin(DACA)[2:].zfill(10) + '00',2))

    # Write to SPI registers with timing delays
    UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(...))
    time.sleep(0.5)  # Critical SPI completion delay
                    </div>
                </div>

                <h3>🔍 7.7 Cross-Reference Analysis and Implementation Patterns</h3>

                <h4>🔄 Common Implementation Patterns</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Pattern Type</th>
                                <th>Standard Implementation</th>
                                <th>Usage Frequency</th>
                                <th>Consistency Level</th>
                                <th>Optimization Opportunity</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Equipment Initialization</strong></td>
                                <td><code>rm = ate_rm(); atc = ATC5000NG(rm); atc.Reset()</code></td>
                                <td>90+ files</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">85%</span></td>
                                <td>Standardize reset timing</td>
                            </tr>
                            <tr>
                                <td><strong>Error Handling</strong></td>
                                <td><code>while (measurement == invalid) and count < 10:</code></td>
                                <td>70+ files</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">78%</span></td>
                                <td>Centralize retry logic</td>
                            </tr>
                            <tr>
                                <td><strong>RF Stabilization</strong></td>
                                <td><code>time.sleep(10-25)</code> after RF enable</td>
                                <td>65+ files</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">90%</span></td>
                                <td>Conditional timing</td>
                            </tr>
                            <tr>
                                <td><strong>Logging Integration</strong></td>
                                <td><code>rm.logMessage(severity, message)</code></td>
                                <td>85+ files</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">95%</span></td>
                                <td>Enhanced formatting</td>
                            </tr>
                            <tr>
                                <td><strong>SPI Operations</strong></td>
                                <td><code>UUTReg.writeRFRegister(); time.sleep(0.5)</code></td>
                                <td>50+ instances</td>
                                <td><span style="color: var(--accent-danger); font-weight: bold;">65%</span></td>
                                <td>Reduce delay timing</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Pattern Analysis:</strong> This table identifies common implementation patterns across all test sequences. Usage Frequency indicates how many files use each pattern. Consistency Level measures standardization across implementations. Optimization Opportunity highlights areas for improvement based on the previous analysis sections.
                    </div>
                </div>

                <h4>⏱️ Timing Requirements Summary</h4>
                <div class="charts-container">
                    <div class="chart-card">
                        <h3>Equipment Reset Timing</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 ATC5000NG Full Reset:</span>
                                    <span style="color: var(--accent-danger); font-weight: bold;">15 seconds</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📊 D3054Scope Reset:</span>
                                    <span style="color: var(--accent-warning); font-weight: bold;">5 seconds</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>⚡ B4500C Power Meter:</span>
                                    <span style="color: var(--accent-warning); font-weight: bold;">5 seconds</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🎛️ RGS2000NG Reset:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">2 seconds</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>RF Stabilization Timing</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📡 RF Turn-on (Basic):</span>
                                    <span style="color: var(--accent-warning); font-weight: bold;">10 seconds</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔄 Mode Switching:</span>
                                    <span style="color: var(--accent-danger); font-weight: bold;">25 seconds</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📻 Frequency Changes:</span>
                                    <span style="color: var(--accent-warning); font-weight: bold;">5 seconds</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>⚙️ Power Level Changes:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">3 seconds</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>SPI and Communication</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>💾 SPI Register Write:</span>
                                    <span style="color: var(--accent-danger); font-weight: bold;">0.5 seconds</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📡 SCPI Command:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">0.1 seconds</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📊 Measurement Read:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">0.3 seconds</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔄 Retry Delays:</span>
                                    <span style="color: var(--accent-warning); font-weight: bold;">1.0 seconds</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <h4>🔗 Handler Dependency Matrix</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Test Standard</th>
                                <th>Primary Handler</th>
                                <th>Secondary Handlers</th>
                                <th>Utility Dependencies</th>
                                <th>Complexity Level</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>DO-181E</strong></td>
                                <td>ATC5000NG (100%)</td>
                                <td>D3054Scope (35%), B4500CPwrMeter (25%)</td>
                                <td>ate_rm, SPIDevices</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO-189</strong></td>
                                <td>ATC5000NG (100%)</td>
                                <td>D3054Scope (75%), ARINC_Client (100%)</td>
                                <td>ate_rm, PulseTiming</td>
                                <td><span style="color: var(--accent-danger); font-weight: bold;">HIGH</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO-385</strong></td>
                                <td>RGS2000NG (100%)</td>
                                <td>B4500CPwrMeter (90%), ATC5000NG (20%)</td>
                                <td>ate_rm, Spectrum</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43</strong></td>
                                <td>ATC5000NG (100%)</td>
                                <td>RFBOB (100%), DigitalBOB (80%)</td>
                                <td>ate_rm</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO-282</strong></td>
                                <td>ATC5000NG (100%)</td>
                                <td>Custom UAT modules (100%)</td>
                                <td>ate_rm, FEC, reedsolo</td>
                                <td><span style="color: var(--accent-danger); font-weight: bold;">HIGH</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Dependency Analysis:</strong> This matrix shows the handler dependencies for each test standard. Primary Handler indicates the main equipment controller. Secondary Handlers show additional equipment required. Utility Dependencies list supporting modules. Complexity Level reflects the integration difficulty and number of dependencies.
                    </div>
                </div>

                <h3>📋 7.8 Implementation Recommendations and Best Practices</h3>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-success); margin: 20px 0;">
                    <h4>✅ Standardization Opportunities</h4>
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Unified Initialization Pattern:</strong> Standardize equipment setup sequences across all test standards</li>
                        <li><strong>Centralized Error Handling:</strong> Implement common retry and validation logic</li>
                        <li><strong>Timing Optimization:</strong> Reduce conservative delays based on reset vs re-initialization analysis</li>
                        <li><strong>Enhanced Logging:</strong> Implement structured logging with consistent formatting</li>
                        <li><strong>Configuration Management:</strong> Centralize timing and threshold parameters</li>
                    </ol>
                </div>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-warning); margin: 20px 0;">
                    <h4>⚠️ Areas Requiring Attention</h4>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>SPI Timing Inconsistency:</strong> 0.5-second delays appear excessive for modern hardware</li>
                        <li><strong>Error Handling Gaps:</strong> 22% of files lack comprehensive error recovery</li>
                        <li><strong>Documentation Variance:</strong> Inconsistent inline documentation across similar functions</li>
                        <li><strong>Reset Operation Overhead:</strong> Significant time savings potential identified</li>
                        <li><strong>Handler Integration:</strong> Some sequences use deprecated or inefficient patterns</li>
                    </ul>
                </div>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-primary); margin: 20px 0;">
                    <h4>🚀 Future Enhancement Opportunities</h4>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Parallel Execution:</strong> Enable concurrent equipment initialization where safe</li>
                        <li><strong>Adaptive Timing:</strong> Implement dynamic delay adjustment based on equipment response</li>
                        <li><strong>Enhanced Validation:</strong> Add comprehensive measurement validation and bounds checking</li>
                        <li><strong>Modular Architecture:</strong> Extract common patterns into reusable modules</li>
                        <li><strong>Performance Monitoring:</strong> Add execution time tracking and optimization metrics</li>
                    </ul>
                </div>
            </section>

            <!-- Recommendations -->
            <section id="recommendations" class="section">
                <h2>🎯 Summary and Recommendations</h2>

                <h3>🚀 Priority 1 Actions (Low Risk, High Impact)</h3>
                <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #27ae60; margin: 20px 0;">
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Optimize SPI Delays:</strong> Reduce 0.5s delays to 0.01-0.05s (Est. savings: 20-25s per test)</li>
                        <li><strong>Document Undocumented Delays:</strong> Add comments to 200+ undocumented delays</li>
                        <li><strong>Standardize Common Operations:</strong> Create consistent timing for RF operations, measurements</li>
                    </ol>
                </div>

                <h3>⚡ Priority 2 Actions (Medium Risk, Medium Impact)</h3>
                <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db; margin: 20px 0;">
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Reduce Conservative Delays:</strong> Test and optimize 50+ conservative delays</li>
                        <li><strong>Consolidate Reset Operations:</strong> Eliminate redundant resets in cleanup sequences</li>
                        <li><strong>Implement Configurable Timing:</strong> Make delays parameterizable for different equipment</li>
                    </ol>
                </div>

                <h3>🔍 Priority 3 Actions (Higher Risk, Requires Testing)</h3>
                <div style="background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #f39c12; margin: 20px 0;">
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Investigate Long Delays:</strong> Verify necessity of 30+ second delays</li>
                        <li><strong>Implement Parallel Operations:</strong> Enable concurrent equipment initialization</li>
                        <li><strong>Add Conditional Logic:</strong> Reset only when equipment state requires it</li>
                    </ol>
                </div>

                <h3>💰 Estimated Total Time Savings</h3>
                <div class="dashboard">
                    <div class="metric-card">
                        <h3>Conservative Estimate</h3>
                        <div class="metric-value">30-60s</div>
                        <div class="metric-description">Per test procedure</div>
                    </div>
                    <div class="metric-card">
                        <h3>Aggressive Estimate</h3>
                        <div class="metric-value">60-120s</div>
                        <div class="metric-description">Per test procedure</div>
                    </div>
                    <div class="metric-card">
                        <h3>Annual Impact</h3>
                        <div class="metric-value">500-2000h</div>
                        <div class="metric-description">Assuming 10,000 test runs/year</div>
                    </div>
                </div>
            </section>

            <!-- Appendices -->
            <section id="appendix" class="section">
                <h2>📚 Appendices</h2>

                <button class="collapsible">📋 Appendix A: Detailed Code Examples</button>
                <div class="collapsible-content">
                    <h4>✅ Well-Documented Delay Example</h4>
                    <div class="code-snippet">
cmd = ':ATC:DME:POWER ' + str(signalLevel)
atc.write(cmd)
time.sleep(2)    # This delay allows enough time for ATC to be set with new lower power level and for the LRU to respond to the change in power level.
                    </div>
                    <p><strong>Analysis:</strong> This is an excellent example of proper documentation. The comment clearly explains both the equipment behavior (ATC power setting) and the system response requirement (LRU response time).</p>

                    <h4>❌ Poorly Documented Delay Example</h4>
                    <div class="code-snippet">
#Turn on RF
atc.gwrite(":ATC:XPDR:RF ON")
time.sleep(10)

atc.waitforstatus()

#Measure Frequency
time.sleep(2)
Frequencies[0] = atc.getPulseFrequency(4)
                    </div>
                    <p><strong>Analysis:</strong> These delays lack documentation. The 10-second delay is likely for RF stabilization, and the 2-second delay for measurement settling, but this should be explicitly documented.</p>

                    <h4>🔧 SPI Device Delay Pattern</h4>
                    <div class="code-snippet">
UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
time.sleep(0.5)
                    </div>
                    <p><strong>Analysis:</strong> This 0.5-second delay appears after every SPI register write. Modern SPI operations typically complete in microseconds, suggesting this delay could be significantly reduced.</p>
                </div>

                <button class="collapsible">⚠️ Appendix B: Risk Assessment Matrix</button>
                <div class="collapsible-content">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Risk Level</th>
                                    <th>Criteria</th>
                                    <th>Examples</th>
                                    <th>Mitigation Strategy</th>
                                    <th>Decision</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>LOW</strong></td>
                                    <td>Software timing, no hardware dependency</td>
                                    <td>SPI delays, file I/O</td>
                                    <td>Test with reduced delays in controlled environment</td>
                                    <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                                </tr>
                                <tr>
                                    <td><strong>MEDIUM</strong></td>
                                    <td>Equipment settling time, some variability</td>
                                    <td>Measurement delays, short equipment waits</td>
                                    <td>Gradual reduction with extensive testing</td>
                                    <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                                </tr>
                                <tr>
                                    <td><strong>HIGH</strong></td>
                                    <td>Critical equipment stabilization</td>
                                    <td>RF turn-on, mode switching</td>
                                    <td>Requires equipment specification review</td>
                                    <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                                </tr>
                                <tr>
                                    <td><strong>CRITICAL</strong></td>
                                    <td>Safety or equipment protection</td>
                                    <td>Power-on sequences, thermal settling</td>
                                    <td>Do not modify without vendor consultation</td>
                                    <td><span class="decision-badge decision-keep">KEEP</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <button class="collapsible">🚀 Appendix C: Implementation Roadmap</button>
                <div class="collapsible-content">
                    <h4>Phase 1: Low-Risk Optimizations (Weeks 1-2)</h4>
                    <ul style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                        <li><strong>SPI Delay Reduction:</strong> Test 0.5s → 0.05s reduction</li>
                        <li><strong>Documentation Addition:</strong> Add comments to undocumented delays</li>
                        <li><strong>Retry Loop Optimization:</strong> Reduce 1s → 0.3s delays</li>
                    </ul>

                    <h4>Phase 2: Medium-Risk Optimizations (Weeks 3-6)</h4>
                    <ul style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                        <li><strong>Equipment Delay Testing:</strong> Systematically test delay reductions</li>
                        <li><strong>Reset Consolidation:</strong> Eliminate redundant reset operations</li>
                        <li><strong>Standardization:</strong> Implement consistent timing patterns</li>
                    </ul>

                    <h4>Phase 3: High-Risk Investigations (Weeks 7-12)</h4>
                    <ul style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                        <li><strong>Long Delay Analysis:</strong> Investigate 30+ second delays</li>
                        <li><strong>Equipment Specification Review:</strong> Verify manufacturer requirements</li>
                        <li><strong>Parallel Operation Implementation:</strong> Enable concurrent operations</li>
                    </ul>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.nav-item').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    // Update active nav item
                    document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
                    this.classList.add('active');

                    // Smooth scroll to target
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Collapsible sections functionality
        document.querySelectorAll('.collapsible').forEach(button => {
            button.addEventListener('click', function() {
                this.classList.toggle('active');
                const content = this.nextElementSibling;

                if (content.classList.contains('active')) {
                    content.classList.remove('active');
                } else {
                    content.classList.add('active');
                }
            });
        });

        // Table sorting functionality
        function sortTable(table, column, asc = true) {
            const dirModifier = asc ? 1 : -1;
            const tBody = table.tBodies[0];
            const rows = Array.from(tBody.querySelectorAll('tr'));

            const sortedRows = rows.sort((a, b) => {
                const aColText = a.querySelector(`td:nth-child(${column + 1})`).textContent.trim();
                const bColText = b.querySelector(`td:nth-child(${column + 1})`).textContent.trim();

                return aColText > bColText ? (1 * dirModifier) : (-1 * dirModifier);
            });

            while (tBody.firstChild) {
                tBody.removeChild(tBody.firstChild);
            }

            tBody.append(...sortedRows);
        }

        // Add click handlers to table headers for sorting
        document.querySelectorAll('table th').forEach((headerCell, index) => {
            headerCell.addEventListener('click', () => {
                const table = headerCell.closest('table');
                const currentIsAscending = headerCell.classList.contains('asc');

                // Remove sorting classes from all headers
                table.querySelectorAll('th').forEach(th => th.classList.remove('asc', 'desc'));

                // Add appropriate class to current header
                headerCell.classList.toggle('asc', !currentIsAscending);
                headerCell.classList.toggle('desc', currentIsAscending);

                sortTable(table, index, !currentIsAscending);
            });
        });

        // Animate sections on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all sections
        document.querySelectorAll('.section').forEach(section => {
            observer.observe(section);
        });

        // Mobile menu toggle (for responsive design)
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('open');
        }

        // Add mobile menu button if needed
        if (window.innerWidth <= 768) {
            const mobileMenuBtn = document.createElement('button');
            mobileMenuBtn.innerHTML = '☰ Menu';
            mobileMenuBtn.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 1001;
                background: #3498db;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                cursor: pointer;
            `;
            mobileMenuBtn.onclick = toggleMobileMenu;
            document.body.appendChild(mobileMenuBtn);
        }

        // Update navigation active state on scroll
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('.section');
            const navItems = document.querySelectorAll('.nav-item');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === `#${current}`) {
                    item.classList.add('active');
                }
            });
        });

        // Print optimization
        window.addEventListener('beforeprint', () => {
            document.querySelectorAll('.collapsible-content').forEach(content => {
                content.classList.add('active');
            });
        });

        // Theme Toggle Functionality
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Update button text
            const button = document.querySelector('.theme-toggle');
            button.textContent = newTheme === 'dark' ? '☀️ Light Theme' : '🌓 Dark Theme';
        }

        // Load saved theme
        function loadTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const button = document.querySelector('.theme-toggle');
            if (button) {
                button.textContent = savedTheme === 'dark' ? '☀️ Light Theme' : '🌓 Dark Theme';
            }
        }

        // Enhanced chart animations
        function animateCharts() {
            const chartCards = document.querySelectorAll('.chart-card');
            chartCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0) scale(1)';
                }, index * 200);
            });
        }

        // Enhanced table interactions
        function enhanceTableInteractions() {
            // Add row click highlighting
            document.querySelectorAll('table tbody tr').forEach(row => {
                row.addEventListener('click', function() {
                    // Remove previous highlights
                    document.querySelectorAll('table tbody tr').forEach(r => r.classList.remove('highlighted'));
                    // Add highlight to clicked row
                    this.classList.add('highlighted');
                });
            });

            // Add column highlighting on header hover
            document.querySelectorAll('table th').forEach((header, index) => {
                header.addEventListener('mouseenter', function() {
                    const table = this.closest('table');
                    const cells = table.querySelectorAll(`td:nth-child(${index + 1}), th:nth-child(${index + 1})`);
                    cells.forEach(cell => cell.classList.add('column-highlight'));
                });

                header.addEventListener('mouseleave', function() {
                    const table = this.closest('table');
                    const cells = table.querySelectorAll(`td:nth-child(${index + 1}), th:nth-child(${index + 1})`);
                    cells.forEach(cell => cell.classList.remove('column-highlight'));
                });
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            // Load theme first
            loadTheme();

            // Set first nav item as active
            const firstNavItem = document.querySelector('.nav-item');
            if (firstNavItem) {
                firstNavItem.classList.add('active');
            }

            // Enhanced table interactions
            enhanceTableInteractions();

            // Animate initial load
            setTimeout(() => {
                document.querySelectorAll('.section').forEach((section, index) => {
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, index * 100);
                });

                // Animate charts after sections
                setTimeout(animateCharts, 1000);
            }, 300);
        });
    </script>
</body>
</html>
