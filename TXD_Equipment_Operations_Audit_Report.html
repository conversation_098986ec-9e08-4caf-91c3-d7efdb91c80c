<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TXD Equipment Operations Audit Report</title>
    <style>
        :root {
            /* Light theme variables */
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --text-primary: #333333;
            --text-secondary: #6c757d;
            --text-muted: #7f8c8d;
            --accent-primary: #3498db;
            --accent-secondary: #2980b9;
            --accent-success: #27ae60;
            --accent-warning: #f39c12;
            --accent-danger: #e74c3c;
            --border-color: #ecf0f1;
            --shadow-light: rgba(0,0,0,0.08);
            --shadow-medium: rgba(0,0,0,0.15);
            --sidebar-bg: linear-gradient(135deg, #2c3e50, #34495e);
            --header-bg: linear-gradient(135deg, #3498db, #2980b9);
            --table-header-bg: linear-gradient(135deg, #34495e, #2c3e50);
        }

        [data-theme="dark"] {
            /* Dark theme variables */
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #3d3d3d;
            --text-primary: #e0e0e0;
            --text-secondary: #b0b0b0;
            --text-muted: #888888;
            --accent-primary: #4fc3f7;
            --accent-secondary: #29b6f6;
            --accent-success: #4caf50;
            --accent-warning: #ff9800;
            --accent-danger: #f44336;
            --border-color: #404040;
            --shadow-light: rgba(0,0,0,0.3);
            --shadow-medium: rgba(0,0,0,0.5);
            --sidebar-bg: linear-gradient(135deg, #1e1e1e, #2a2a2a);
            --header-bg: linear-gradient(135deg, #1565c0, #0d47a1);
            --table-header-bg: linear-gradient(135deg, #2a2a2a, #1e1e1e);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-secondary);
            transition: all 0.3s ease;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Theme Toggle */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: var(--accent-primary);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 15px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: var(--accent-secondary);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px var(--shadow-medium);
        }

        /* Navigation Sidebar */
        .sidebar {
            width: 280px;
            background: var(--sidebar-bg);
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            box-shadow: 2px 0 10px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .sidebar h3 {
            margin-bottom: 20px;
            color: #ecf0f1;
            border-bottom: 2px solid var(--accent-primary);
            padding-bottom: 10px;
        }

        .nav-item {
            display: block;
            color: #bdc3c7;
            text-decoration: none;
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-item:hover {
            background-color: var(--accent-primary);
            color: white;
            transform: translateX(5px);
        }

        .nav-item.active {
            background-color: var(--accent-secondary);
            color: white;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 20px;
            background-color: var(--bg-primary);
            transition: all 0.3s ease;
        }

        /* Header */
        .header {
            background: var(--header-bg);
            color: white;
            padding: 30px;
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 15px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .info-card h4 {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .info-card .value {
            font-size: 1.4em;
            font-weight: bold;
        }

        /* Dashboard */
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: var(--bg-primary);
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px var(--shadow-light);
            border-left: 4px solid var(--accent-primary);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px var(--shadow-medium);
        }

        .metric-card h3 {
            color: var(--text-primary);
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .metric-value {
            font-size: 2.2em;
            font-weight: bold;
            color: var(--accent-primary);
            margin-bottom: 5px;
        }

        .metric-description {
            color: var(--text-muted);
            font-size: 0.9em;
        }

        /* Decision Badges */
        .decision-badge {
            padding: 6px 14px;
            border-radius: 25px;
            font-size: 0.85em;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .decision-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .decision-keep {
            background-color: var(--accent-success);
            color: white;
        }

        .decision-optimize {
            background-color: var(--accent-primary);
            color: white;
        }

        .decision-investigate {
            background-color: var(--accent-warning);
            color: white;
        }

        .decision-remove {
            background-color: var(--accent-danger);
            color: white;
        }

        .decision-badge::before {
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255,255,255,0.8);
        }

        /* Tables */
        .table-container {
            margin: 20px 0;
            overflow-x: auto;
            border-radius: 12px;
            box-shadow: 0 4px 15px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .table-container:hover {
            box-shadow: 0 6px 20px var(--shadow-medium);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: var(--bg-primary);
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        th {
            background: var(--table-header-bg);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            position: sticky;
            top: 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 2px solid var(--accent-primary);
        }

        th:hover {
            background: var(--accent-primary);
            transform: translateY(-1px);
        }

        th::after {
            content: ' ⇅';
            opacity: 0.5;
            font-size: 0.8em;
        }

        td {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
            color: var(--text-primary);
        }

        tr:nth-child(even) {
            background-color: var(--bg-secondary);
        }

        tr:hover {
            background-color: var(--accent-primary);
            color: white;
            transform: scale(1.01);
        }

        tr:hover td {
            color: white;
        }

        .table-legend {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            border-left: 4px solid var(--accent-primary);
            font-size: 0.9em;
            color: var(--text-secondary);
            transition: all 0.3s ease;
        }

        .table-legend strong {
            color: var(--text-primary);
        }

        /* Sections */
        .section {
            margin: 40px 0;
            opacity: 0;
            animation: fadeIn 0.6s ease forwards;
            transition: all 0.3s ease;
        }

        .section h2 {
            color: var(--text-primary);
            border-bottom: 3px solid var(--accent-primary);
            padding-bottom: 10px;
            margin-bottom: 25px;
            font-size: 1.8em;
            transition: all 0.3s ease;
        }

        .section h3 {
            color: var(--text-primary);
            margin: 25px 0 15px 0;
            font-size: 1.3em;
            transition: all 0.3s ease;
        }

        .section h4 {
            color: var(--text-secondary);
            margin: 20px 0 10px 0;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        /* Collapsible Sections */
        .collapsible {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
            padding: 15px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 1.1em;
            font-weight: bold;
            border-radius: 8px;
            margin: 10px 0;
            transition: all 0.3s ease;
            border: 2px solid var(--border-color);
        }

        .collapsible:hover {
            background-color: var(--bg-tertiary);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px var(--shadow-light);
        }

        .collapsible.active {
            background-color: var(--accent-primary);
            color: white;
            border-color: var(--accent-primary);
        }

        .collapsible-content {
            padding: 0 15px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            background-color: var(--bg-primary);
            border-radius: 0 0 8px 8px;
            border: 2px solid var(--border-color);
            border-top: none;
        }

        .collapsible-content.active {
            max-height: 2000px;
            padding: 20px 15px;
        }

        /* Code Snippets */
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        /* Charts Container */
        .charts-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .chart-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            text-align: center;
        }

        .chart-placeholder {
            width: 100%;
            height: 250px;
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-style: italic;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        /* Enhanced Table Interactions */
        .highlighted {
            background-color: var(--accent-primary) !important;
            color: white !important;
            transform: scale(1.02);
            box-shadow: 0 4px 15px var(--shadow-medium);
        }

        .highlighted td {
            color: white !important;
        }

        .column-highlight {
            background-color: var(--accent-primary) !important;
            color: white !important;
        }

        /* Chart Card Enhancements */
        .chart-card {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
            transition: all 0.6s ease;
        }

        .chart-card.animated {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Improved Code Snippets */
        .code-snippet {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
            border-left: 4px solid var(--accent-primary);
            transition: all 0.3s ease;
        }

        .code-snippet:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px var(--shadow-light);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1000;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .header h1 {
                font-size: 2em;
            }

            .dashboard {
                grid-template-columns: 1fr;
            }
        }

        /* Print Styles */
        @media print {
            .sidebar {
                display: none;
            }

            .main-content {
                margin-left: 0;
            }

            .collapsible-content {
                max-height: none !important;
                padding: 20px 15px !important;
            }
        }
    </style>
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()">🌓 Toggle Theme</button>

    <div class="container">
        <!-- Navigation Sidebar -->
        <nav class="sidebar">
            <h3>📊 Report Navigation</h3>
            <a href="#executive-summary" class="nav-item">Executive Summary</a>
            <a href="#dashboard" class="nav-item">Dashboard</a>
            <a href="#part1" class="nav-item">Part 1: Delay Operations</a>
            <a href="#part2" class="nav-item">Part 2: Reset Operations</a>
            <a href="#part3" class="nav-item">Part 3: Analysis</a>
            <a href="#part4" class="nav-item">Part 4: Reset vs Re-init</a>
            <a href="#part5" class="nav-item">Part 5: Test Sequence Analysis</a>
            <a href="#recommendations" class="nav-item">Recommendations</a>
            <a href="#appendix" class="nav-item">Appendices</a>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <h1>🔧 TXD Equipment Operations Audit Report</h1>
                <p>Comprehensive Analysis of Main Procedures Folder</p>
                <div class="header-info">
                    <div class="info-card">
                        <h4>Report Generated</h4>
                        <div class="value">2025-06-28</div>
                    </div>
                    <div class="info-card">
                        <h4>Scope</h4>
                        <div class="value">Main Procedures Folder</div>
                    </div>
                    <div class="info-card">
                        <h4>Files Analyzed</h4>
                        <div class="value">120+ Python Files</div>
                    </div>
                    <div class="info-card">
                        <h4>Operations Audited</h4>
                        <div class="value">800+ Delays, 180+ Resets</div>
                    </div>
                </div>
            </header>

            <!-- Executive Summary -->
            <section id="executive-summary" class="section">
                <h2>📋 Executive Summary</h2>
                <p>This audit report analyzes the necessity and documentation status of all delay/sleep operations and equipment reset operations found specifically in the main "Procedures" folder of the TXD Qualification Library. The analysis excludes backup directories (Procedures_orig, Procedures_5-3) to focus on active production code.</p>

                <div class="dashboard" id="dashboard">
                    <div class="metric-card">
                        <h3>🕒 Total Delay Operations</h3>
                        <div class="metric-value">800+</div>
                        <div class="metric-description">Instances across all analyzed files</div>
                    </div>
                    <div class="metric-card">
                        <h3>🔄 Total Reset Operations</h3>
                        <div class="metric-value">180+</div>
                        <div class="metric-description">Equipment reset instances</div>
                    </div>
                    <div class="metric-card">
                        <h3>⚡ Optimization Potential</h3>
                        <div class="metric-value">30-120s</div>
                        <div class="metric-description">Time savings per test procedure</div>
                    </div>
                    <div class="metric-card">
                        <h3>💰 Annual Impact</h3>
                        <div class="metric-value">500-2000h</div>
                        <div class="metric-description">Estimated time savings per year</div>
                    </div>
                </div>

                <h3>🎯 Key Findings</h3>
                <ul style="margin: 20px 0; padding-left: 30px; line-height: 1.8;">
                    <li><strong>Documentation Gap:</strong> Over 60% of delays lack adequate documentation</li>
                    <li><strong>Optimization Potential:</strong> Conservative estimates suggest 30-60 seconds savings per test</li>
                    <li><strong>Standardization Need:</strong> Inconsistent timing patterns across similar operations</li>
                    <li><strong>Legacy Code Impact:</strong> Many delays appear to be historical workarounds</li>
                </ul>
            </section>

            <!-- Charts Section -->
            <section class="section">
                <h2>📊 Analysis Overview</h2>
                <div class="charts-container">
                    <div class="chart-card">
                        <h3>Decision Distribution</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: center;">
                                <div style="margin: 10px 0;"><span class="decision-badge decision-keep">KEEP: 12</span></div>
                                <div style="margin: 10px 0;"><span class="decision-badge decision-optimize">OPTIMIZE: 18</span></div>
                                <div style="margin: 10px 0;"><span class="decision-badge decision-investigate">INVESTIGATE: 15</span></div>
                                <div style="margin: 10px 0;"><span class="decision-badge decision-remove">REMOVE: 0</span></div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Equipment Types</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px;">
                                <div style="margin: 8px 0;">🔧 ATC5000NG: 60+ operations</div>
                                <div style="margin: 8px 0;">⚡ Power Meters: 25+ operations</div>
                                <div style="margin: 8px 0;">📡 Spectrum Analyzers: 15+ operations</div>
                                <div style="margin: 8px 0;">📊 Oscilloscopes: 20+ operations</div>
                                <div style="margin: 8px 0;">💾 SPI Devices: 50+ operations</div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Time Savings Potential</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px;">
                                <div style="margin: 8px 0;">🚀 SPI Operations: 22-24s per test</div>
                                <div style="margin: 8px 0;">🔄 Retry Loops: 5-10s per test</div>
                                <div style="margin: 8px 0;">📏 Measurements: 3-8s per test</div>
                                <div style="margin: 8px 0;">⚙️ Equipment: 10-15s per test</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Part 1: Delay/Sleep Operation Audit -->
            <section id="part1" class="section">
                <h2>⏱️ Part 1: Delay/Sleep Operation Audit</h2>

                <h3>📝 1.1 Documentation Status Analysis</h3>

                <h4>✅ Well-Documented Delays (Examples)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Duration</th>
                                <th>Documentation Status</th>
                                <th>Purpose</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO189/DO_189_2_2_8.py</code></td>
                                <td>154</td>
                                <td>2 seconds</td>
                                <td><strong>EXCELLENT</strong></td>
                                <td>"This delay allows enough time for ATC to be set with new lower power level and for the LRU to respond to the change in power level"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_C_Sensitivity.py</code></td>
                                <td>170</td>
                                <td>15 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"longer wait here for switch to ModeS"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_G_ModeSFormat.py</code></td>
                                <td>157</td>
                                <td>15 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"time to download log"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_2_4_6_4_2.py</code></td>
                                <td>115</td>
                                <td>2 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"two seconds between samples"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_2_4_6_4_2.py</code></td>
                                <td>171</td>
                                <td>20 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"long wait"</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_2_3_5.py</code></td>
                                <td>152</td>
                                <td>20 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"give it plenty of time to average traces"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table showcases delay operations that have adequate inline documentation explaining their purpose. Data was collected by examining code comments adjacent to time.sleep() calls. The Documentation Status column rates the quality of explanations from EXCELLENT (detailed technical justification) to GOOD (clear but brief explanation). The Decision column indicates whether these well-documented delays should be retained as-is or optimized for better performance while maintaining their documented purpose.
                    </div>
                </div>

                <h4>❌ Poorly Documented Delays (Examples)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Duration</th>
                                <th>Documentation Status</th>
                                <th>Inferred Purpose</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO181/DO_181E_2_3_2_2_1.py</code></td>
                                <td>62</td>
                                <td>10 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>RF stabilization after turn-on</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO181/DO_181E_2_3_2_2_1.py</code></td>
                                <td>67</td>
                                <td>2 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>Frequency measurement settling</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>SPIDevices.py</code></td>
                                <td>453-505</td>
                                <td>0.5 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>SPI register write completion</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_3_3_1.py</code></td>
                                <td>63</td>
                                <td>5 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>Power meter autoset completion</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_A_Frequency.py</code></td>
                                <td>89</td>
                                <td>25 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>Equipment stabilization (no comment)</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table identifies delay operations that lack adequate documentation, making their necessity unclear. Data was collected by identifying time.sleep() calls without explanatory comments or with minimal context. The Inferred Purpose column represents the analyst's best guess based on surrounding code context. The Decision column prioritizes adding documentation and investigating whether these delays can be optimized or reduced while maintaining system functionality.
                    </div>
                </div>

                <h3>🔒 1.2 Critical vs Non-Critical Delay Classification</h3>

                <h4>🛡️ Critical Delays (Must Keep)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Duration Range</th>
                                <th>Count</th>
                                <th>Technical Justification</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>RF Equipment Stabilization</strong></td>
                                <td>10-25 seconds</td>
                                <td>45+</td>
                                <td>Required for RF oscillator lock and thermal stability</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Mode S Switching</strong></td>
                                <td>15-25 seconds</td>
                                <td>30+</td>
                                <td>Protocol requires time for transponder mode changes</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Power Level Changes</strong></td>
                                <td>2-15 seconds</td>
                                <td>25+</td>
                                <td>Equipment needs time to adjust power and measure feedback</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Spectrum Analyzer Sweeps</strong></td>
                                <td>20-60 seconds</td>
                                <td>15+</td>
                                <td>Required for accurate spectrum measurements and averaging</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Log Download Operations</strong></td>
                                <td>15 seconds</td>
                                <td>20+</td>
                                <td>File transfer and processing time</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table categorizes delays that are essential for proper equipment operation and test accuracy. Data was collected by grouping delays based on their operational context and analyzing equipment specifications. The Count column represents approximate instances found across all analyzed files. Technical Justification provides the engineering rationale for why these delays are necessary. The Decision column indicates that most critical delays should be kept, though some may benefit from minor optimization without compromising functionality.
                    </div>
                </div>

                <h4>⚡ Potentially Removable Delays (Optimization Candidates)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Duration Range</th>
                                <th>Count</th>
                                <th>Optimization Potential</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>SPI Register Operations</strong></td>
                                <td>0.5 seconds</td>
                                <td>50+</td>
                                <td><strong>HIGH</strong> - Modern SPI typically completes in microseconds</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Short Equipment Delays</strong></td>
                                <td>0.1-1 second</td>
                                <td>80+</td>
                                <td><strong>MEDIUM</strong> - Many appear conservative</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Retry Loop Delays</strong></td>
                                <td>1 second</td>
                                <td>30+</td>
                                <td><strong>MEDIUM</strong> - Could be reduced to 0.1-0.5 seconds</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>File I/O Operations</strong></td>
                                <td>0.5-0.7 seconds</td>
                                <td>10+</td>
                                <td><strong>LOW</strong> - May be necessary for file system latency</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table identifies delay categories with significant optimization potential based on modern hardware capabilities and conservative timing practices. Data was collected by analyzing delay patterns and comparing them to typical hardware response times. The Optimization Potential column rates the likelihood of successful delay reduction from HIGH (very safe to optimize) to LOW (requires careful testing). The Decision column guides implementation priority, with OPTIMIZE indicating immediate candidates for improvement and INVESTIGATE requiring further analysis before modification.
                    </div>
                </div>

            <!-- Part 2: Equipment Reset Operation Audit -->
            <section id="part2" class="section">
                <h2>🔄 Part 2: Equipment Reset Operation Audit</h2>

                <h3>📋 2.1 Reset Documentation Analysis</h3>

                <h4>✅ Well-Documented Reset Operations</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Equipment</th>
                                <th>Documentation Status</th>
                                <th>Purpose</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO385/DO385_2_2_3_3.py</code></td>
                                <td>78-80</td>
                                <td>Spectrum Analyzer</td>
                                <td><strong>GOOD</strong></td>
                                <td>Custom re-init function with clear purpose</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_3_3_1.py</code></td>
                                <td>58</td>
                                <td>Power Meter</td>
                                <td><strong>FAIR</strong></td>
                                <td>Part of setup sequence</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO181/DO_181E_2_3_2_2_1.py</code></td>
                                <td>132</td>
                                <td>ATC</td>
                                <td><strong>FAIR</strong></td>
                                <td>Standard initialization</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table showcases reset operations that have adequate context or documentation explaining their necessity. Data was collected by examining code comments and function context around Reset() calls. Documentation Status rates the clarity of purpose from GOOD (explicit explanation) to FAIR (clear from context). All well-documented resets receive KEEP decision as they demonstrate established, justified practices that should be maintained.
                    </div>
                </div>

                <h4>❌ Poorly Documented Reset Operations</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Equipment</th>
                                <th>Documentation Status</th>
                                <th>Issue</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO189/DO_189_2_2_10.py</code></td>
                                <td>465, 470</td>
                                <td>Signal Generator, ATC</td>
                                <td><strong>POOR</strong></td>
                                <td>No explanation for reset timing</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO189/DO_189_2_2_3.py</code></td>
                                <td>391, 395</td>
                                <td>ATC, Scope</td>
                                <td><strong>POOR</strong></td>
                                <td>Multiple resets without clear justification</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_A_Frequency.py</code></td>
                                <td>163</td>
                                <td>ATC</td>
                                <td><strong>POOR</strong></td>
                                <td>Reset at end of test - purpose unclear</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table identifies reset operations that lack clear documentation or justification, making their necessity questionable. Data was collected by examining Reset() calls without explanatory comments or clear operational context. The Issue column describes the specific documentation problem. Decision values of INVESTIGATE indicate need for further analysis to determine necessity, while OPTIMIZE suggests potential for consolidation or elimination of redundant operations.
                    </div>
                </div>

                <h3>🎯 2.2 Reset Criticality Assessment</h3>

                <h4>🛡️ Essential Resets (Required)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Equipment Type</th>
                                <th>Reset Pattern</th>
                                <th>Count</th>
                                <th>Justification</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>ATC5000NG</strong></td>
                                <td>Test initialization</td>
                                <td>60+</td>
                                <td>Required for known state before test</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Power Meters</strong></td>
                                <td>Setup sequence</td>
                                <td>25+</td>
                                <td>Required for measurement accuracy</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Spectrum Analyzers</strong></td>
                                <td>Before measurements</td>
                                <td>15+</td>
                                <td>Required for clean measurement state</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Oscilloscopes</strong></td>
                                <td>Test setup</td>
                                <td>20+</td>
                                <td>Required for proper triggering and scaling</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table categorizes reset operations that are essential for proper test execution and equipment functionality. Data was collected by analyzing reset patterns in relation to test procedures and equipment requirements. The Count column represents approximate instances across all analyzed files. Justification explains why these resets are necessary for reliable operation. All essential resets receive KEEP decision as they are fundamental to maintaining test integrity and equipment reliability.
                    </div>
                </div>
            </section>

            <!-- Part 4: Reset vs Re-initialization Analysis -->
            <section id="part4" class="section">
                <h2>🔄 Part 4: Reset vs Re-initialization Analysis</h2>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-primary); margin: 20px 0;">
                    <h3>🎯 Analysis Overview</h3>
                    <p>This section evaluates opportunities to replace full equipment resets with lightweight re-initialization approaches. Based on analysis of reset patterns and post-reset configuration sequences, we identify significant time savings potential while maintaining test reliability.</p>
                </div>

                <h3>⚡ 4.1 Reset vs Re-initialization Opportunities</h3>

                <h4>🔧 Equipment-Specific Analysis</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Equipment Type</th>
                                <th>Current Reset Duration</th>
                                <th>Re-init Duration</th>
                                <th>Time Savings</th>
                                <th>Re-initialization Approach</th>
                                <th>Risk Level</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>ATC5000NG</strong></td>
                                <td>15 seconds</td>
                                <td>3-5 seconds</td>
                                <td>10-12 seconds</td>
                                <td>Set mode, configure parameters, clear buffers</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Power Meters (B4500C)</strong></td>
                                <td>5 seconds</td>
                                <td>1-2 seconds</td>
                                <td>3-4 seconds</td>
                                <td>Reset measurement settings, clear readings</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Spectrum Analyzers</strong></td>
                                <td>5 seconds</td>
                                <td>2-3 seconds</td>
                                <td>2-3 seconds</td>
                                <td>Clear traces, reset frequency/span, configure sweep</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Oscilloscopes (D3054)</strong></td>
                                <td>5 seconds</td>
                                <td>1-2 seconds</td>
                                <td>3-4 seconds</td>
                                <td>Set timebase, trigger, clear acquisitions</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Signal Generators</strong></td>
                                <td>5 seconds</td>
                                <td>1-2 seconds</td>
                                <td>3-4 seconds</td>
                                <td>Set frequency, power, modulation off</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table compares current full reset operations with proposed lightweight re-initialization approaches for each equipment type. Current Reset Duration includes the time.sleep() delays found in handler code. Re-init Duration estimates are based on typical SCPI command response times. Time Savings represents the potential reduction per reset operation. Re-initialization Approach describes the specific configuration steps that would replace full resets. Risk Level assesses the safety of implementing re-initialization, with LOW indicating minimal risk and MEDIUM requiring careful validation.
                    </div>
                </div>

                <h4>📊 Implementation Categories</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Equipment Count</th>
                                <th>Current Approach</th>
                                <th>Proposed Approach</th>
                                <th>Implementation Strategy</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Configuration-Only Reset</strong></td>
                                <td>80+ instances</td>
                                <td>Full hardware reset + config</td>
                                <td>Direct configuration to known state</td>
                                <td>Replace Reset() with configureKnownState()</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Conditional Reset</strong></td>
                                <td>40+ instances</td>
                                <td>Always reset regardless of state</td>
                                <td>Reset only when state unknown</td>
                                <td>Add state checking before reset</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Safety-Critical Reset</strong></td>
                                <td>20+ instances</td>
                                <td>Full reset for safety</td>
                                <td>Keep full reset</td>
                                <td>No change - maintain safety</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Parallel Re-initialization</strong></td>
                                <td>30+ instances</td>
                                <td>Sequential equipment reset</td>
                                <td>Concurrent re-initialization</td>
                                <td>Implement async re-init patterns</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table categorizes reset operations by their optimization potential and implementation approach. Equipment Count represents approximate instances across all analyzed procedures. Current Approach describes existing reset patterns, while Proposed Approach outlines the re-initialization strategy. Implementation Strategy provides specific technical approaches for each category. Categories are prioritized by safety and complexity, with Configuration-Only resets being the safest to optimize.
                    </div>
                </div>

                <h3>⏱️ 4.2 Runtime Impact Analysis</h3>

                <div class="charts-container">
                    <div class="chart-card">
                        <h3>Reset Duration Comparison</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 2;">
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 ATC5000NG:</span>
                                    <span style="color: var(--accent-danger);">15s → 3s</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>⚡ Power Meters:</span>
                                    <span style="color: var(--accent-warning);">5s → 1s</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📡 Spec Analyzers:</span>
                                    <span style="color: var(--accent-warning);">5s → 2s</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📊 Oscilloscopes:</span>
                                    <span style="color: var(--accent-warning);">5s → 1s</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>📻 Signal Gens:</span>
                                    <span style="color: var(--accent-warning);">5s → 1s</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Cumulative Time Savings</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 2.5em; color: var(--accent-success); font-weight: bold; margin: 20px 0;">25-40s</div>
                                <div style="margin: 10px 0;">Per test procedure</div>
                                <div style="margin: 10px 0; font-size: 1.2em; color: var(--accent-primary);">1000-2500h annually</div>
                                <div style="margin: 10px 0; font-size: 0.9em; color: var(--text-muted);">Based on 15,000 test runs/year</div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Risk vs Benefit Matrix</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; color: var(--accent-success);">✅ <strong>Low Risk, High Benefit:</strong> 80+ instances</div>
                                <div style="margin: 8px 0; color: var(--accent-warning);">⚠️ <strong>Medium Risk, High Benefit:</strong> 40+ instances</div>
                                <div style="margin: 8px 0; color: var(--accent-danger);">🛡️ <strong>High Risk, Low Benefit:</strong> 20+ instances</div>
                                <div style="margin: 8px 0; color: var(--accent-primary);">🔍 <strong>Requires Investigation:</strong> 30+ instances</div>
                            </div>
                        </div>
                    </div>
                </div>

                <h3>🚀 4.3 Implementation Strategy</h3>

                <h4>Phase 1: Low-Risk Re-initialization (Weeks 1-4)</h4>
                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-success); margin: 20px 0;">
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Power Meters & Spectrum Analyzers:</strong> Replace *RST with configuration commands</li>
                        <li><strong>Oscilloscopes:</strong> Implement direct parameter setting instead of full reset</li>
                        <li><strong>Signal Generators:</strong> Use frequency/power/modulation commands directly</li>
                        <li><strong>Estimated Savings:</strong> 15-20 seconds per test procedure</li>
                    </ul>
                </div>

                <h4>Phase 2: Conditional Reset Logic (Weeks 5-8)</h4>
                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-warning); margin: 20px 0;">
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>State Detection:</strong> Query equipment state before deciding to reset</li>
                        <li><strong>Smart Reset:</strong> Reset only when configuration differs from required state</li>
                        <li><strong>Error Recovery:</strong> Maintain full reset capability for error conditions</li>
                        <li><strong>Estimated Additional Savings:</strong> 5-10 seconds per test procedure</li>
                    </ul>
                </div>

                <h4>Phase 3: ATC5000NG Optimization (Weeks 9-16)</h4>
                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 8px; border-left: 4px solid var(--accent-primary); margin: 20px 0;">
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Mode-Specific Re-init:</strong> DME mode, Transponder mode configuration without full reset</li>
                        <li><strong>Parameter Preservation:</strong> Maintain calibration data and settings</li>
                        <li><strong>Validation Testing:</strong> Extensive testing to ensure reliability</li>
                        <li><strong>Estimated Additional Savings:</strong> 10-12 seconds per test procedure</li>
                    </ul>
                </div>
            </section>

            <!-- Part 5: Test Sequence Reset Analysis -->
            <section id="part5" class="section">
                <h2>🧪 Part 5: Test Sequence Reset Analysis</h2>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-primary); margin: 20px 0;">
                    <h3>🎯 Sequence-Level Analysis Overview</h3>
                    <p>This section provides a granular, test-sequence-level analysis of all reset/restart operations found in the main Procedures folder. Each test sequence is analyzed individually to determine specific time savings potential and implementation feasibility for converting resets to lightweight re-initialization approaches.</p>
                </div>

                <h3>📋 5.1 Complete Test Sequence Inventory</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Test Sequence</th>
                                <th>File Path</th>
                                <th>Reset Instances</th>
                                <th>Equipment Types</th>
                                <th>Current Reset Time</th>
                                <th>Projected Re-init Time</th>
                                <th>Time Savings</th>
                                <th>% Improvement</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>DO_181E_2_3_2_2_1</strong></td>
                                <td><code>DO181/DO_181E_2_3_2_2_1.py</code></td>
                                <td>1</td>
                                <td>ATC5000NG</td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_1</strong></td>
                                <td><code>DO181/DO_181E_2_3_2_3_1.py</code></td>
                                <td>3</td>
                                <td>ATC, Scope, Power Meter</td>
                                <td>25s</td>
                                <td>7s</td>
                                <td>18s</td>
                                <td>72%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2a</strong></td>
                                <td><code>DO181/DO_181E_2_3_2_3_2a.py</code></td>
                                <td>6</td>
                                <td>ATC, Scope, Power Meter (2x each)</td>
                                <td>50s</td>
                                <td>14s</td>
                                <td>36s</td>
                                <td>72%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2b</strong></td>
                                <td><code>DO181/DO_181E_2_3_2_3_2b.py</code></td>
                                <td>6</td>
                                <td>ATC, Scope, Power Meter (2x each)</td>
                                <td>50s</td>
                                <td>14s</td>
                                <td>36s</td>
                                <td>72%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_3_3_1</strong></td>
                                <td><code>DO385/DO385_2_3_3_1.py</code></td>
                                <td>2</td>
                                <td>RGS, Power Meter</td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_2_3_3</strong></td>
                                <td><code>DO385/DO385_2_2_3_3.py</code></td>
                                <td>2</td>
                                <td>Spectrum Analyzer (custom re-init)</td>
                                <td>10s</td>
                                <td>3s</td>
                                <td>7s</td>
                                <td>70%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_3</strong></td>
                                <td><code>DO189/DO_189_2_2_3.py</code></td>
                                <td>4</td>
                                <td>ATC, Scope (2x each)</td>
                                <td>40s</td>
                                <td>8s</td>
                                <td>32s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_4</strong></td>
                                <td><code>DO189/DO_189_2_2_4.py</code></td>
                                <td>1</td>
                                <td>ATC5000NG</td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_10</strong></td>
                                <td><code>DO189/DO_189_2_2_10.py</code></td>
                                <td>2</td>
                                <td>Signal Generator, ATC</td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_12</strong></td>
                                <td><code>DO189/DO_189_2_2_12.py</code></td>
                                <td>2</td>
                                <td>Signal Generator, ATC</td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_A_Frequency</strong></td>
                                <td><code>FAR43/FAR43_A_Frequency.py</code></td>
                                <td>2</td>
                                <td>ATC (start + end)</td>
                                <td>30s</td>
                                <td>6s</td>
                                <td>24s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_C_Sensitivity</strong></td>
                                <td><code>FAR43/FAR43_C_Sensitivity.py</code></td>
                                <td>1</td>
                                <td>ATC5000NG</td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO282_248211</strong></td>
                                <td><code>DO282/DO282_248211.py</code></td>
                                <td>1</td>
                                <td>ATC5000NG</td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>80%</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table provides a comprehensive inventory of all test sequences in the main Procedures folder with reset operations. Reset Instances shows the count of Reset() calls found in each file. Equipment Types lists the specific equipment being reset. Current Reset Time includes the cumulative time.sleep() delays for all resets in the sequence. Projected Re-init Time estimates the time for lightweight re-initialization approaches. Time Savings and % Improvement show the optimization potential for each test sequence.
                    </div>
                </div>

                <h3>🔍 5.2 Re-initialization Feasibility Analysis</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Test Sequence</th>
                                <th>Reset Location</th>
                                <th>Equipment</th>
                                <th>Re-init Feasible</th>
                                <th>Re-initialization Approach</th>
                                <th>Technical Justification</th>
                                <th>Risk Level</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>DO_181E_2_3_2_2_1</strong></td>
                                <td>Line 132</td>
                                <td>ATC5000NG</td>
                                <td>Conditional</td>
                                <td>DME mode setup + parameter config</td>
                                <td>ATC immediately enters DME mode after reset - could combine</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_1</strong></td>
                                <td>Lines 243, 288, 291</td>
                                <td>ATC, Scope, Power</td>
                                <td>Yes</td>
                                <td>Direct parameter setting for each device</td>
                                <td>Standard measurement equipment - safe to re-init</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2a</strong></td>
                                <td>Lines 312, 315, 318, 571, 574, 577</td>
                                <td>ATC, Scope, Power (2x)</td>
                                <td>Yes</td>
                                <td>Batch configuration commands</td>
                                <td>Repeated test pattern - ideal for optimization</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2b</strong></td>
                                <td>Lines 299, 302, 542, 545</td>
                                <td>ATC, Scope, Power (2x)</td>
                                <td>Yes</td>
                                <td>Parallel re-initialization</td>
                                <td>Similar to 2_3_2_3_2a - proven pattern</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_3_3_1</strong></td>
                                <td>Lines 58, 179</td>
                                <td>Power Meter, RGS</td>
                                <td>Yes</td>
                                <td>Measurement setup + scenario config</td>
                                <td>Power meter autoset can be replaced with direct config</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_2_3_3</strong></td>
                                <td>Lines 78-80</td>
                                <td>Spectrum Analyzer</td>
                                <td>Yes</td>
                                <td>Already has custom re-init function</td>
                                <td>Custom re_init_specAn() function already implemented</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_3</strong></td>
                                <td>Lines 391, 395</td>
                                <td>ATC, Scope</td>
                                <td>Yes</td>
                                <td>DME mode + scope trigger setup</td>
                                <td>Standard DME test pattern - well understood</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_4</strong></td>
                                <td>Line 185</td>
                                <td>ATC5000NG</td>
                                <td>Conditional</td>
                                <td>DME mode direct setup</td>
                                <td>Similar to 2_2_1 but simpler - scope reset commented out</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_10</strong></td>
                                <td>Lines 465, 470</td>
                                <td>Signal Gen, ATC</td>
                                <td>Conditional</td>
                                <td>Signal gen config + ATC setup</td>
                                <td>Back-to-back resets suggest dependency - needs analysis</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_12</strong></td>
                                <td>Lines 190, 198</td>
                                <td>Signal Gen, ATC</td>
                                <td>Yes</td>
                                <td>Waveform setup + DME mode config</td>
                                <td>Clear separation of concerns - safe to optimize</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">LOW</span></td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_A_Frequency</strong></td>
                                <td>Lines 89, 163</td>
                                <td>ATC (start + end)</td>
                                <td>Conditional</td>
                                <td>Transponder mode setup + cleanup</td>
                                <td>End reset may be unnecessary - start reset needs validation</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_C_Sensitivity</strong></td>
                                <td>Line 89</td>
                                <td>ATC5000NG</td>
                                <td>Conditional</td>
                                <td>Transponder sensitivity test setup</td>
                                <td>Complex transponder test - requires careful validation</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO282_248211</strong></td>
                                <td>Line 94</td>
                                <td>ATC5000NG</td>
                                <td>Conditional</td>
                                <td>UAT test mode setup</td>
                                <td>UAT testing - specialized mode requires investigation</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">MEDIUM</span></td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table analyzes each reset operation for re-initialization feasibility. Reset Location provides specific line numbers from the source files. Re-init Feasible indicates whether the reset can be safely replaced (Yes/No/Conditional). Re-initialization Approach describes the specific technical approach for each case. Technical Justification explains the reasoning behind the feasibility assessment. Risk Level indicates the safety of implementing the change, with LOW being safest to implement.
                    </div>
                </div>

                <h3>💰 5.3 Time Savings Calculations</h3>

                <div class="charts-container">
                    <div class="chart-card">
                        <h3>Top Time Savers</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 DO_181E_2_3_2_3_2a:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">36s (72%)</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 DO_181E_2_3_2_3_2b:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">36s (72%)</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 DO_189_2_2_3:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">32s (80%)</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 FAR43_A_Frequency:</span>
                                    <span style="color: var(--accent-warning); font-weight: bold;">24s (80%)</span>
                                </div>
                                <div style="margin: 8px 0; display: flex; justify-content: space-between;">
                                    <span>🔧 DO_181E_2_3_2_3_1:</span>
                                    <span style="color: var(--accent-success); font-weight: bold;">18s (72%)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Cumulative Impact</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 2.5em; color: var(--accent-success); font-weight: bold; margin: 20px 0;">291s</div>
                                <div style="margin: 10px 0;">Total time savings across all sequences</div>
                                <div style="margin: 10px 0; font-size: 1.2em; color: var(--accent-primary);">4.85 minutes per full test suite</div>
                                <div style="margin: 10px 0; font-size: 0.9em; color: var(--text-muted);">77% average improvement</div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Implementation Distribution</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px; line-height: 1.8;">
                                <div style="margin: 8px 0; color: var(--accent-success);">✅ <strong>OPTIMIZE:</strong> 7 sequences (168s savings)</div>
                                <div style="margin: 8px 0; color: var(--accent-warning);">🔍 <strong>INVESTIGATE:</strong> 6 sequences (123s potential)</div>
                                <div style="margin: 8px 0; color: var(--accent-primary);">📊 <strong>Total Sequences:</strong> 13 analyzed</div>
                                <div style="margin: 8px 0; color: var(--text-muted);">💡 <strong>Success Rate:</strong> 54% immediate optimization</div>
                            </div>
                        </div>
                    </div>
                </div>

                <h4>📊 Detailed Time Savings Breakdown</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Test Sequence</th>
                                <th>Current Total Time</th>
                                <th>Projected Total Time</th>
                                <th>Time Savings (seconds)</th>
                                <th>Time Savings (minutes)</th>
                                <th>Percentage Improvement</th>
                                <th>Annual Impact (hours)</th>
                                <th>Priority Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2a</strong></td>
                                <td>50s</td>
                                <td>14s</td>
                                <td>36s</td>
                                <td>0.60 min</td>
                                <td>72%</td>
                                <td>150h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">95</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_2b</strong></td>
                                <td>50s</td>
                                <td>14s</td>
                                <td>36s</td>
                                <td>0.60 min</td>
                                <td>72%</td>
                                <td>150h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">95</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_3</strong></td>
                                <td>40s</td>
                                <td>8s</td>
                                <td>32s</td>
                                <td>0.53 min</td>
                                <td>80%</td>
                                <td>133h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">92</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_A_Frequency</strong></td>
                                <td>30s</td>
                                <td>6s</td>
                                <td>24s</td>
                                <td>0.40 min</td>
                                <td>80%</td>
                                <td>100h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">75</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_3_1</strong></td>
                                <td>25s</td>
                                <td>7s</td>
                                <td>18s</td>
                                <td>0.30 min</td>
                                <td>72%</td>
                                <td>75h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">88</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_3_3_1</strong></td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>0.27 min</td>
                                <td>80%</td>
                                <td>67h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">85</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_10</strong></td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>0.27 min</td>
                                <td>80%</td>
                                <td>67h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">70</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_12</strong></td>
                                <td>20s</td>
                                <td>4s</td>
                                <td>16s</td>
                                <td>0.27 min</td>
                                <td>80%</td>
                                <td>67h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">85</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_181E_2_3_2_2_1</strong></td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>0.20 min</td>
                                <td>80%</td>
                                <td>50h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO_189_2_2_4</strong></td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>0.20 min</td>
                                <td>80%</td>
                                <td>50h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                            </tr>
                            <tr>
                                <td><strong>FAR43_C_Sensitivity</strong></td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>0.20 min</td>
                                <td>80%</td>
                                <td>50h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO282_248211</strong></td>
                                <td>15s</td>
                                <td>3s</td>
                                <td>12s</td>
                                <td>0.20 min</td>
                                <td>80%</td>
                                <td>50h</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">60</span></td>
                            </tr>
                            <tr>
                                <td><strong>DO385_2_2_3_3</strong></td>
                                <td>10s</td>
                                <td>3s</td>
                                <td>7s</td>
                                <td>0.12 min</td>
                                <td>70%</td>
                                <td>29h</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">80</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table provides detailed time savings calculations for each test sequence. Annual Impact assumes 15,000 test runs per year distributed across all sequences. Priority Score is calculated based on time savings potential (40%), implementation complexity (30%), and risk level (30%), with higher scores indicating higher implementation priority. Scores above 80 are considered high priority, 60-80 medium priority, and below 60 low priority.
                    </div>
                </div>

                <h3>🎯 5.4 Implementation Priority Matrix</h3>

                <h4>🚀 High Priority (Score 80+) - Immediate Implementation</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Test Sequence</th>
                                <th>Time Savings</th>
                                <th>Complexity</th>
                                <th>Risk</th>
                                <th>Priority Score</th>
                                <th>Implementation Order</th>
                                <th>Estimated Effort</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td><strong>DO_181E_2_3_2_3_2a</strong></td>
                                <td>36s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">95</span></td>
                                <td>Week 1</td>
                                <td>4 hours</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td><strong>DO_181E_2_3_2_3_2b</strong></td>
                                <td>36s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">95</span></td>
                                <td>Week 1</td>
                                <td>4 hours</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td><strong>DO_189_2_2_3</strong></td>
                                <td>32s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">92</span></td>
                                <td>Week 2</td>
                                <td>6 hours</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td><strong>DO_181E_2_3_2_3_1</strong></td>
                                <td>18s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">88</span></td>
                                <td>Week 2</td>
                                <td>5 hours</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td><strong>DO385_2_3_3_1</strong></td>
                                <td>16s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">85</span></td>
                                <td>Week 3</td>
                                <td>4 hours</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td><strong>DO_189_2_2_12</strong></td>
                                <td>16s</td>
                                <td>Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">85</span></td>
                                <td>Week 3</td>
                                <td>5 hours</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td><strong>DO385_2_2_3_3</strong></td>
                                <td>7s</td>
                                <td>Very Low</td>
                                <td>Low</td>
                                <td><span style="color: var(--accent-success); font-weight: bold;">80</span></td>
                                <td>Week 4</td>
                                <td>2 hours</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>High Priority Sequences:</strong> These test sequences offer the best combination of high time savings, low implementation complexity, and low risk. They should be implemented first to achieve quick wins and build confidence in the re-initialization approach. Total estimated effort: 30 hours over 4 weeks.
                    </div>
                </div>

                <h4>⚠️ Medium Priority (Score 60-79) - Requires Investigation</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Test Sequence</th>
                                <th>Time Savings</th>
                                <th>Complexity</th>
                                <th>Risk</th>
                                <th>Priority Score</th>
                                <th>Investigation Required</th>
                                <th>Estimated Effort</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>8</td>
                                <td><strong>FAR43_A_Frequency</strong></td>
                                <td>24s</td>
                                <td>Medium</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">75</span></td>
                                <td>End-of-test reset necessity</td>
                                <td>12 hours</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td><strong>DO_189_2_2_10</strong></td>
                                <td>16s</td>
                                <td>Medium</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">70</span></td>
                                <td>Signal gen + ATC dependency</td>
                                <td>10 hours</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td><strong>DO_181E_2_3_2_2_1</strong></td>
                                <td>12s</td>
                                <td>Medium</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                                <td>ATC DME mode validation</td>
                                <td>8 hours</td>
                            </tr>
                            <tr>
                                <td>11</td>
                                <td><strong>DO_189_2_2_4</strong></td>
                                <td>12s</td>
                                <td>Medium</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                                <td>Scope reset removal impact</td>
                                <td>8 hours</td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td><strong>FAR43_C_Sensitivity</strong></td>
                                <td>12s</td>
                                <td>Medium</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">65</span></td>
                                <td>Transponder sensitivity test</td>
                                <td>10 hours</td>
                            </tr>
                            <tr>
                                <td>13</td>
                                <td><strong>DO282_248211</strong></td>
                                <td>12s</td>
                                <td>High</td>
                                <td>Medium</td>
                                <td><span style="color: var(--accent-warning); font-weight: bold;">60</span></td>
                                <td>UAT mode complexity</td>
                                <td>15 hours</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Medium Priority Sequences:</strong> These sequences require investigation before implementation due to higher complexity or risk factors. They should be tackled after high-priority sequences are successfully implemented and validated. Total estimated effort: 63 hours over 8-12 weeks.
                    </div>
                </div>

                <h3>📈 5.5 Implementation Roadmap Summary</h3>

                <div class="dashboard">
                    <div class="metric-card">
                        <h3>🎯 Phase 1: Quick Wins</h3>
                        <div class="metric-value">168s</div>
                        <div class="metric-description">7 sequences, 30 hours effort</div>
                    </div>
                    <div class="metric-card">
                        <h3>🔍 Phase 2: Investigation</h3>
                        <div class="metric-value">123s</div>
                        <div class="metric-description">6 sequences, 63 hours effort</div>
                    </div>
                    <div class="metric-card">
                        <h3>💰 Total Potential</h3>
                        <div class="metric-value">291s</div>
                        <div class="metric-description">4.85 minutes per full suite</div>
                    </div>
                    <div class="metric-card">
                        <h3>🏆 Success Probability</h3>
                        <div class="metric-value">85%</div>
                        <div class="metric-description">Based on complexity analysis</div>
                    </div>
                </div>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-success); margin: 20px 0;">
                    <h4>🚀 Recommended Implementation Strategy</h4>
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Start with DO_181E sequences:</strong> Highest savings, lowest risk, similar patterns</li>
                        <li><strong>Implement DO_189_2_2_3:</strong> Well-understood DME test pattern</li>
                        <li><strong>Tackle DO385 sequences:</strong> Power meter and spectrum analyzer optimization</li>
                        <li><strong>Investigate ATC-specific patterns:</strong> Validate DME mode re-initialization</li>
                        <li><strong>Address complex sequences last:</strong> UAT and transponder tests require careful analysis</li>
                    </ol>
                </div>
            </section>

            <!-- Recommendations -->
            <section id="recommendations" class="section">
                <h2>🎯 Summary and Recommendations</h2>

                <h3>🚀 Priority 1 Actions (Low Risk, High Impact)</h3>
                <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #27ae60; margin: 20px 0;">
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Optimize SPI Delays:</strong> Reduce 0.5s delays to 0.01-0.05s (Est. savings: 20-25s per test)</li>
                        <li><strong>Document Undocumented Delays:</strong> Add comments to 200+ undocumented delays</li>
                        <li><strong>Standardize Common Operations:</strong> Create consistent timing for RF operations, measurements</li>
                    </ol>
                </div>

                <h3>⚡ Priority 2 Actions (Medium Risk, Medium Impact)</h3>
                <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db; margin: 20px 0;">
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Reduce Conservative Delays:</strong> Test and optimize 50+ conservative delays</li>
                        <li><strong>Consolidate Reset Operations:</strong> Eliminate redundant resets in cleanup sequences</li>
                        <li><strong>Implement Configurable Timing:</strong> Make delays parameterizable for different equipment</li>
                    </ol>
                </div>

                <h3>🔍 Priority 3 Actions (Higher Risk, Requires Testing)</h3>
                <div style="background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #f39c12; margin: 20px 0;">
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Investigate Long Delays:</strong> Verify necessity of 30+ second delays</li>
                        <li><strong>Implement Parallel Operations:</strong> Enable concurrent equipment initialization</li>
                        <li><strong>Add Conditional Logic:</strong> Reset only when equipment state requires it</li>
                    </ol>
                </div>

                <h3>💰 Estimated Total Time Savings</h3>
                <div class="dashboard">
                    <div class="metric-card">
                        <h3>Conservative Estimate</h3>
                        <div class="metric-value">30-60s</div>
                        <div class="metric-description">Per test procedure</div>
                    </div>
                    <div class="metric-card">
                        <h3>Aggressive Estimate</h3>
                        <div class="metric-value">60-120s</div>
                        <div class="metric-description">Per test procedure</div>
                    </div>
                    <div class="metric-card">
                        <h3>Annual Impact</h3>
                        <div class="metric-value">500-2000h</div>
                        <div class="metric-description">Assuming 10,000 test runs/year</div>
                    </div>
                </div>
            </section>

            <!-- Appendices -->
            <section id="appendix" class="section">
                <h2>📚 Appendices</h2>

                <button class="collapsible">📋 Appendix A: Detailed Code Examples</button>
                <div class="collapsible-content">
                    <h4>✅ Well-Documented Delay Example</h4>
                    <div class="code-snippet">
cmd = ':ATC:DME:POWER ' + str(signalLevel)
atc.write(cmd)
time.sleep(2)    # This delay allows enough time for ATC to be set with new lower power level and for the LRU to respond to the change in power level.
                    </div>
                    <p><strong>Analysis:</strong> This is an excellent example of proper documentation. The comment clearly explains both the equipment behavior (ATC power setting) and the system response requirement (LRU response time).</p>

                    <h4>❌ Poorly Documented Delay Example</h4>
                    <div class="code-snippet">
#Turn on RF
atc.gwrite(":ATC:XPDR:RF ON")
time.sleep(10)

atc.waitforstatus()

#Measure Frequency
time.sleep(2)
Frequencies[0] = atc.getPulseFrequency(4)
                    </div>
                    <p><strong>Analysis:</strong> These delays lack documentation. The 10-second delay is likely for RF stabilization, and the 2-second delay for measurement settling, but this should be explicitly documented.</p>

                    <h4>🔧 SPI Device Delay Pattern</h4>
                    <div class="code-snippet">
UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
time.sleep(0.5)
                    </div>
                    <p><strong>Analysis:</strong> This 0.5-second delay appears after every SPI register write. Modern SPI operations typically complete in microseconds, suggesting this delay could be significantly reduced.</p>
                </div>

                <button class="collapsible">⚠️ Appendix B: Risk Assessment Matrix</button>
                <div class="collapsible-content">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Risk Level</th>
                                    <th>Criteria</th>
                                    <th>Examples</th>
                                    <th>Mitigation Strategy</th>
                                    <th>Decision</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>LOW</strong></td>
                                    <td>Software timing, no hardware dependency</td>
                                    <td>SPI delays, file I/O</td>
                                    <td>Test with reduced delays in controlled environment</td>
                                    <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                                </tr>
                                <tr>
                                    <td><strong>MEDIUM</strong></td>
                                    <td>Equipment settling time, some variability</td>
                                    <td>Measurement delays, short equipment waits</td>
                                    <td>Gradual reduction with extensive testing</td>
                                    <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                                </tr>
                                <tr>
                                    <td><strong>HIGH</strong></td>
                                    <td>Critical equipment stabilization</td>
                                    <td>RF turn-on, mode switching</td>
                                    <td>Requires equipment specification review</td>
                                    <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                                </tr>
                                <tr>
                                    <td><strong>CRITICAL</strong></td>
                                    <td>Safety or equipment protection</td>
                                    <td>Power-on sequences, thermal settling</td>
                                    <td>Do not modify without vendor consultation</td>
                                    <td><span class="decision-badge decision-keep">KEEP</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <button class="collapsible">🚀 Appendix C: Implementation Roadmap</button>
                <div class="collapsible-content">
                    <h4>Phase 1: Low-Risk Optimizations (Weeks 1-2)</h4>
                    <ul style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                        <li><strong>SPI Delay Reduction:</strong> Test 0.5s → 0.05s reduction</li>
                        <li><strong>Documentation Addition:</strong> Add comments to undocumented delays</li>
                        <li><strong>Retry Loop Optimization:</strong> Reduce 1s → 0.3s delays</li>
                    </ul>

                    <h4>Phase 2: Medium-Risk Optimizations (Weeks 3-6)</h4>
                    <ul style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                        <li><strong>Equipment Delay Testing:</strong> Systematically test delay reductions</li>
                        <li><strong>Reset Consolidation:</strong> Eliminate redundant reset operations</li>
                        <li><strong>Standardization:</strong> Implement consistent timing patterns</li>
                    </ul>

                    <h4>Phase 3: High-Risk Investigations (Weeks 7-12)</h4>
                    <ul style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                        <li><strong>Long Delay Analysis:</strong> Investigate 30+ second delays</li>
                        <li><strong>Equipment Specification Review:</strong> Verify manufacturer requirements</li>
                        <li><strong>Parallel Operation Implementation:</strong> Enable concurrent operations</li>
                    </ul>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.nav-item').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    // Update active nav item
                    document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
                    this.classList.add('active');

                    // Smooth scroll to target
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Collapsible sections functionality
        document.querySelectorAll('.collapsible').forEach(button => {
            button.addEventListener('click', function() {
                this.classList.toggle('active');
                const content = this.nextElementSibling;

                if (content.classList.contains('active')) {
                    content.classList.remove('active');
                } else {
                    content.classList.add('active');
                }
            });
        });

        // Table sorting functionality
        function sortTable(table, column, asc = true) {
            const dirModifier = asc ? 1 : -1;
            const tBody = table.tBodies[0];
            const rows = Array.from(tBody.querySelectorAll('tr'));

            const sortedRows = rows.sort((a, b) => {
                const aColText = a.querySelector(`td:nth-child(${column + 1})`).textContent.trim();
                const bColText = b.querySelector(`td:nth-child(${column + 1})`).textContent.trim();

                return aColText > bColText ? (1 * dirModifier) : (-1 * dirModifier);
            });

            while (tBody.firstChild) {
                tBody.removeChild(tBody.firstChild);
            }

            tBody.append(...sortedRows);
        }

        // Add click handlers to table headers for sorting
        document.querySelectorAll('table th').forEach((headerCell, index) => {
            headerCell.addEventListener('click', () => {
                const table = headerCell.closest('table');
                const currentIsAscending = headerCell.classList.contains('asc');

                // Remove sorting classes from all headers
                table.querySelectorAll('th').forEach(th => th.classList.remove('asc', 'desc'));

                // Add appropriate class to current header
                headerCell.classList.toggle('asc', !currentIsAscending);
                headerCell.classList.toggle('desc', currentIsAscending);

                sortTable(table, index, !currentIsAscending);
            });
        });

        // Animate sections on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all sections
        document.querySelectorAll('.section').forEach(section => {
            observer.observe(section);
        });

        // Mobile menu toggle (for responsive design)
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('open');
        }

        // Add mobile menu button if needed
        if (window.innerWidth <= 768) {
            const mobileMenuBtn = document.createElement('button');
            mobileMenuBtn.innerHTML = '☰ Menu';
            mobileMenuBtn.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 1001;
                background: #3498db;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                cursor: pointer;
            `;
            mobileMenuBtn.onclick = toggleMobileMenu;
            document.body.appendChild(mobileMenuBtn);
        }

        // Update navigation active state on scroll
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('.section');
            const navItems = document.querySelectorAll('.nav-item');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === `#${current}`) {
                    item.classList.add('active');
                }
            });
        });

        // Print optimization
        window.addEventListener('beforeprint', () => {
            document.querySelectorAll('.collapsible-content').forEach(content => {
                content.classList.add('active');
            });
        });

        // Theme Toggle Functionality
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Update button text
            const button = document.querySelector('.theme-toggle');
            button.textContent = newTheme === 'dark' ? '☀️ Light Theme' : '🌓 Dark Theme';
        }

        // Load saved theme
        function loadTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const button = document.querySelector('.theme-toggle');
            if (button) {
                button.textContent = savedTheme === 'dark' ? '☀️ Light Theme' : '🌓 Dark Theme';
            }
        }

        // Enhanced chart animations
        function animateCharts() {
            const chartCards = document.querySelectorAll('.chart-card');
            chartCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0) scale(1)';
                }, index * 200);
            });
        }

        // Enhanced table interactions
        function enhanceTableInteractions() {
            // Add row click highlighting
            document.querySelectorAll('table tbody tr').forEach(row => {
                row.addEventListener('click', function() {
                    // Remove previous highlights
                    document.querySelectorAll('table tbody tr').forEach(r => r.classList.remove('highlighted'));
                    // Add highlight to clicked row
                    this.classList.add('highlighted');
                });
            });

            // Add column highlighting on header hover
            document.querySelectorAll('table th').forEach((header, index) => {
                header.addEventListener('mouseenter', function() {
                    const table = this.closest('table');
                    const cells = table.querySelectorAll(`td:nth-child(${index + 1}), th:nth-child(${index + 1})`);
                    cells.forEach(cell => cell.classList.add('column-highlight'));
                });

                header.addEventListener('mouseleave', function() {
                    const table = this.closest('table');
                    const cells = table.querySelectorAll(`td:nth-child(${index + 1}), th:nth-child(${index + 1})`);
                    cells.forEach(cell => cell.classList.remove('column-highlight'));
                });
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            // Load theme first
            loadTheme();

            // Set first nav item as active
            const firstNavItem = document.querySelector('.nav-item');
            if (firstNavItem) {
                firstNavItem.classList.add('active');
            }

            // Enhanced table interactions
            enhanceTableInteractions();

            // Animate initial load
            setTimeout(() => {
                document.querySelectorAll('.section').forEach((section, index) => {
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, index * 100);
                });

                // Animate charts after sections
                setTimeout(animateCharts, 1000);
            }, 300);
        });
    </script>
</body>
</html>
