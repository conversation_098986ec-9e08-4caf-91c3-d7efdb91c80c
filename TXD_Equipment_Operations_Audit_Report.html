<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TXD Equipment Operations Audit Report</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Navigation Sidebar */
        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .sidebar h3 {
            margin-bottom: 20px;
            color: #ecf0f1;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .nav-item {
            display: block;
            color: #bdc3c7;
            text-decoration: none;
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-item:hover {
            background-color: #3498db;
            color: white;
            transform: translateX(5px);
        }

        .nav-item.active {
            background-color: #2980b9;
            color: white;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 20px;
            background-color: #ffffff;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 30px;
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .info-card h4 {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .info-card .value {
            font-size: 1.4em;
            font-weight: bold;
        }

        /* Dashboard */
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .metric-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .metric-value {
            font-size: 2.2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .metric-description {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        /* Decision Badges */
        .decision-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .decision-keep {
            background-color: #27ae60;
            color: white;
        }

        .decision-optimize {
            background-color: #3498db;
            color: white;
        }

        .decision-investigate {
            background-color: #f39c12;
            color: white;
        }

        .decision-remove {
            background-color: #e74c3c;
            color: white;
        }

        /* Tables */
        .table-container {
            margin: 20px 0;
            overflow-x: auto;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            font-size: 0.9em;
        }

        th {
            background: linear-gradient(135deg, #34495e, #2c3e50);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            position: sticky;
            top: 0;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        th:hover {
            background: linear-gradient(135deg, #2c3e50, #34495e);
        }

        td {
            padding: 12px;
            border-bottom: 1px solid #ecf0f1;
            transition: background-color 0.3s ease;
        }

        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tr:hover {
            background-color: #e3f2fd;
            transform: scale(1.01);
        }

        .table-legend {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            border-left: 4px solid #3498db;
            font-size: 0.9em;
            color: #555;
        }

        .table-legend strong {
            color: #2c3e50;
        }

        /* Sections */
        .section {
            margin: 40px 0;
            opacity: 0;
            animation: fadeIn 0.6s ease forwards;
        }

        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 25px;
            font-size: 1.8em;
        }

        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.3em;
        }

        .section h4 {
            color: #7f8c8d;
            margin: 20px 0 10px 0;
            font-size: 1.1em;
        }

        /* Collapsible Sections */
        .collapsible {
            background-color: #f1f2f6;
            color: #2c3e50;
            cursor: pointer;
            padding: 15px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 1.1em;
            font-weight: bold;
            border-radius: 8px;
            margin: 10px 0;
            transition: background-color 0.3s ease;
        }

        .collapsible:hover {
            background-color: #ddd;
        }

        .collapsible.active {
            background-color: #3498db;
            color: white;
        }

        .collapsible-content {
            padding: 0 15px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background-color: white;
            border-radius: 0 0 8px 8px;
        }

        .collapsible-content.active {
            max-height: 2000px;
            padding: 20px 15px;
        }

        /* Code Snippets */
        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        /* Charts Container */
        .charts-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .chart-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            text-align: center;
        }

        .chart-placeholder {
            width: 100%;
            height: 250px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-style: italic;
            margin-top: 10px;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1000;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .header h1 {
                font-size: 2em;
            }

            .dashboard {
                grid-template-columns: 1fr;
            }
        }

        /* Print Styles */
        @media print {
            .sidebar {
                display: none;
            }

            .main-content {
                margin-left: 0;
            }

            .collapsible-content {
                max-height: none !important;
                padding: 20px 15px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navigation Sidebar -->
        <nav class="sidebar">
            <h3>📊 Report Navigation</h3>
            <a href="#executive-summary" class="nav-item">Executive Summary</a>
            <a href="#dashboard" class="nav-item">Dashboard</a>
            <a href="#part1" class="nav-item">Part 1: Delay Operations</a>
            <a href="#part2" class="nav-item">Part 2: Reset Operations</a>
            <a href="#part3" class="nav-item">Part 3: Analysis</a>
            <a href="#recommendations" class="nav-item">Recommendations</a>
            <a href="#appendix" class="nav-item">Appendices</a>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <h1>🔧 TXD Equipment Operations Audit Report</h1>
                <p>Comprehensive Analysis of Main Procedures Folder</p>
                <div class="header-info">
                    <div class="info-card">
                        <h4>Report Generated</h4>
                        <div class="value">2025-06-28</div>
                    </div>
                    <div class="info-card">
                        <h4>Scope</h4>
                        <div class="value">Main Procedures Folder</div>
                    </div>
                    <div class="info-card">
                        <h4>Files Analyzed</h4>
                        <div class="value">120+ Python Files</div>
                    </div>
                    <div class="info-card">
                        <h4>Operations Audited</h4>
                        <div class="value">800+ Delays, 180+ Resets</div>
                    </div>
                </div>
            </header>

            <!-- Executive Summary -->
            <section id="executive-summary" class="section">
                <h2>📋 Executive Summary</h2>
                <p>This audit report analyzes the necessity and documentation status of all delay/sleep operations and equipment reset operations found specifically in the main "Procedures" folder of the TXD Qualification Library. The analysis excludes backup directories (Procedures_orig, Procedures_5-3) to focus on active production code.</p>

                <div class="dashboard" id="dashboard">
                    <div class="metric-card">
                        <h3>🕒 Total Delay Operations</h3>
                        <div class="metric-value">800+</div>
                        <div class="metric-description">Instances across all analyzed files</div>
                    </div>
                    <div class="metric-card">
                        <h3>🔄 Total Reset Operations</h3>
                        <div class="metric-value">180+</div>
                        <div class="metric-description">Equipment reset instances</div>
                    </div>
                    <div class="metric-card">
                        <h3>⚡ Optimization Potential</h3>
                        <div class="metric-value">30-120s</div>
                        <div class="metric-description">Time savings per test procedure</div>
                    </div>
                    <div class="metric-card">
                        <h3>💰 Annual Impact</h3>
                        <div class="metric-value">500-2000h</div>
                        <div class="metric-description">Estimated time savings per year</div>
                    </div>
                </div>

                <h3>🎯 Key Findings</h3>
                <ul style="margin: 20px 0; padding-left: 30px; line-height: 1.8;">
                    <li><strong>Documentation Gap:</strong> Over 60% of delays lack adequate documentation</li>
                    <li><strong>Optimization Potential:</strong> Conservative estimates suggest 30-60 seconds savings per test</li>
                    <li><strong>Standardization Need:</strong> Inconsistent timing patterns across similar operations</li>
                    <li><strong>Legacy Code Impact:</strong> Many delays appear to be historical workarounds</li>
                </ul>
            </section>

            <!-- Charts Section -->
            <section class="section">
                <h2>📊 Analysis Overview</h2>
                <div class="charts-container">
                    <div class="chart-card">
                        <h3>Decision Distribution</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: center;">
                                <div style="margin: 10px 0;"><span class="decision-badge decision-keep">KEEP: 12</span></div>
                                <div style="margin: 10px 0;"><span class="decision-badge decision-optimize">OPTIMIZE: 18</span></div>
                                <div style="margin: 10px 0;"><span class="decision-badge decision-investigate">INVESTIGATE: 15</span></div>
                                <div style="margin: 10px 0;"><span class="decision-badge decision-remove">REMOVE: 0</span></div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Equipment Types</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px;">
                                <div style="margin: 8px 0;">🔧 ATC5000NG: 60+ operations</div>
                                <div style="margin: 8px 0;">⚡ Power Meters: 25+ operations</div>
                                <div style="margin: 8px 0;">📡 Spectrum Analyzers: 15+ operations</div>
                                <div style="margin: 8px 0;">📊 Oscilloscopes: 20+ operations</div>
                                <div style="margin: 8px 0;">💾 SPI Devices: 50+ operations</div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h3>Time Savings Potential</h3>
                        <div class="chart-placeholder">
                            <div style="text-align: left; padding: 20px;">
                                <div style="margin: 8px 0;">🚀 SPI Operations: 22-24s per test</div>
                                <div style="margin: 8px 0;">🔄 Retry Loops: 5-10s per test</div>
                                <div style="margin: 8px 0;">📏 Measurements: 3-8s per test</div>
                                <div style="margin: 8px 0;">⚙️ Equipment: 10-15s per test</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Part 1: Delay/Sleep Operation Audit -->
            <section id="part1" class="section">
                <h2>⏱️ Part 1: Delay/Sleep Operation Audit</h2>

                <h3>📝 1.1 Documentation Status Analysis</h3>

                <h4>✅ Well-Documented Delays (Examples)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Duration</th>
                                <th>Documentation Status</th>
                                <th>Purpose</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO189/DO_189_2_2_8.py</code></td>
                                <td>154</td>
                                <td>2 seconds</td>
                                <td><strong>EXCELLENT</strong></td>
                                <td>"This delay allows enough time for ATC to be set with new lower power level and for the LRU to respond to the change in power level"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_C_Sensitivity.py</code></td>
                                <td>170</td>
                                <td>15 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"longer wait here for switch to ModeS"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_G_ModeSFormat.py</code></td>
                                <td>157</td>
                                <td>15 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"time to download log"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_2_4_6_4_2.py</code></td>
                                <td>115</td>
                                <td>2 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"two seconds between samples"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_2_4_6_4_2.py</code></td>
                                <td>171</td>
                                <td>20 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"long wait"</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_2_3_5.py</code></td>
                                <td>152</td>
                                <td>20 seconds</td>
                                <td><strong>GOOD</strong></td>
                                <td>"give it plenty of time to average traces"</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table showcases delay operations that have adequate inline documentation explaining their purpose. Data was collected by examining code comments adjacent to time.sleep() calls. The Documentation Status column rates the quality of explanations from EXCELLENT (detailed technical justification) to GOOD (clear but brief explanation). The Decision column indicates whether these well-documented delays should be retained as-is or optimized for better performance while maintaining their documented purpose.
                    </div>
                </div>

                <h4>❌ Poorly Documented Delays (Examples)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Duration</th>
                                <th>Documentation Status</th>
                                <th>Inferred Purpose</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO181/DO_181E_2_3_2_2_1.py</code></td>
                                <td>62</td>
                                <td>10 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>RF stabilization after turn-on</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO181/DO_181E_2_3_2_2_1.py</code></td>
                                <td>67</td>
                                <td>2 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>Frequency measurement settling</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>SPIDevices.py</code></td>
                                <td>453-505</td>
                                <td>0.5 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>SPI register write completion</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_3_3_1.py</code></td>
                                <td>63</td>
                                <td>5 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>Power meter autoset completion</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_A_Frequency.py</code></td>
                                <td>89</td>
                                <td>25 seconds</td>
                                <td><strong>POOR</strong></td>
                                <td>Equipment stabilization (no comment)</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table identifies delay operations that lack adequate documentation, making their necessity unclear. Data was collected by identifying time.sleep() calls without explanatory comments or with minimal context. The Inferred Purpose column represents the analyst's best guess based on surrounding code context. The Decision column prioritizes adding documentation and investigating whether these delays can be optimized or reduced while maintaining system functionality.
                    </div>
                </div>

                <h3>🔒 1.2 Critical vs Non-Critical Delay Classification</h3>

                <h4>🛡️ Critical Delays (Must Keep)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Duration Range</th>
                                <th>Count</th>
                                <th>Technical Justification</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>RF Equipment Stabilization</strong></td>
                                <td>10-25 seconds</td>
                                <td>45+</td>
                                <td>Required for RF oscillator lock and thermal stability</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Mode S Switching</strong></td>
                                <td>15-25 seconds</td>
                                <td>30+</td>
                                <td>Protocol requires time for transponder mode changes</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Power Level Changes</strong></td>
                                <td>2-15 seconds</td>
                                <td>25+</td>
                                <td>Equipment needs time to adjust power and measure feedback</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Spectrum Analyzer Sweeps</strong></td>
                                <td>20-60 seconds</td>
                                <td>15+</td>
                                <td>Required for accurate spectrum measurements and averaging</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Log Download Operations</strong></td>
                                <td>15 seconds</td>
                                <td>20+</td>
                                <td>File transfer and processing time</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table categorizes delays that are essential for proper equipment operation and test accuracy. Data was collected by grouping delays based on their operational context and analyzing equipment specifications. The Count column represents approximate instances found across all analyzed files. Technical Justification provides the engineering rationale for why these delays are necessary. The Decision column indicates that most critical delays should be kept, though some may benefit from minor optimization without compromising functionality.
                    </div>
                </div>

                <h4>⚡ Potentially Removable Delays (Optimization Candidates)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Duration Range</th>
                                <th>Count</th>
                                <th>Optimization Potential</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>SPI Register Operations</strong></td>
                                <td>0.5 seconds</td>
                                <td>50+</td>
                                <td><strong>HIGH</strong> - Modern SPI typically completes in microseconds</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Short Equipment Delays</strong></td>
                                <td>0.1-1 second</td>
                                <td>80+</td>
                                <td><strong>MEDIUM</strong> - Many appear conservative</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>Retry Loop Delays</strong></td>
                                <td>1 second</td>
                                <td>30+</td>
                                <td><strong>MEDIUM</strong> - Could be reduced to 0.1-0.5 seconds</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><strong>File I/O Operations</strong></td>
                                <td>0.5-0.7 seconds</td>
                                <td>10+</td>
                                <td><strong>LOW</strong> - May be necessary for file system latency</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table identifies delay categories with significant optimization potential based on modern hardware capabilities and conservative timing practices. Data was collected by analyzing delay patterns and comparing them to typical hardware response times. The Optimization Potential column rates the likelihood of successful delay reduction from HIGH (very safe to optimize) to LOW (requires careful testing). The Decision column guides implementation priority, with OPTIMIZE indicating immediate candidates for improvement and INVESTIGATE requiring further analysis before modification.
                    </div>
                </div>

            <!-- Part 2: Equipment Reset Operation Audit -->
            <section id="part2" class="section">
                <h2>🔄 Part 2: Equipment Reset Operation Audit</h2>

                <h3>📋 2.1 Reset Documentation Analysis</h3>

                <h4>✅ Well-Documented Reset Operations</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Equipment</th>
                                <th>Documentation Status</th>
                                <th>Purpose</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO385/DO385_2_2_3_3.py</code></td>
                                <td>78-80</td>
                                <td>Spectrum Analyzer</td>
                                <td><strong>GOOD</strong></td>
                                <td>Custom re-init function with clear purpose</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO385/DO385_2_3_3_1.py</code></td>
                                <td>58</td>
                                <td>Power Meter</td>
                                <td><strong>FAIR</strong></td>
                                <td>Part of setup sequence</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><code>DO181/DO_181E_2_3_2_2_1.py</code></td>
                                <td>132</td>
                                <td>ATC</td>
                                <td><strong>FAIR</strong></td>
                                <td>Standard initialization</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table showcases reset operations that have adequate context or documentation explaining their necessity. Data was collected by examining code comments and function context around Reset() calls. Documentation Status rates the clarity of purpose from GOOD (explicit explanation) to FAIR (clear from context). All well-documented resets receive KEEP decision as they demonstrate established, justified practices that should be maintained.
                    </div>
                </div>

                <h4>❌ Poorly Documented Reset Operations</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Line</th>
                                <th>Equipment</th>
                                <th>Documentation Status</th>
                                <th>Issue</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>DO189/DO_189_2_2_10.py</code></td>
                                <td>465, 470</td>
                                <td>Signal Generator, ATC</td>
                                <td><strong>POOR</strong></td>
                                <td>No explanation for reset timing</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                            <tr>
                                <td><code>DO189/DO_189_2_2_3.py</code></td>
                                <td>391, 395</td>
                                <td>ATC, Scope</td>
                                <td><strong>POOR</strong></td>
                                <td>Multiple resets without clear justification</td>
                                <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                            </tr>
                            <tr>
                                <td><code>FAR43/FAR43_A_Frequency.py</code></td>
                                <td>163</td>
                                <td>ATC</td>
                                <td><strong>POOR</strong></td>
                                <td>Reset at end of test - purpose unclear</td>
                                <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table identifies reset operations that lack clear documentation or justification, making their necessity questionable. Data was collected by examining Reset() calls without explanatory comments or clear operational context. The Issue column describes the specific documentation problem. Decision values of INVESTIGATE indicate need for further analysis to determine necessity, while OPTIMIZE suggests potential for consolidation or elimination of redundant operations.
                    </div>
                </div>

                <h3>🎯 2.2 Reset Criticality Assessment</h3>

                <h4>🛡️ Essential Resets (Required)</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Equipment Type</th>
                                <th>Reset Pattern</th>
                                <th>Count</th>
                                <th>Justification</th>
                                <th>Decision</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>ATC5000NG</strong></td>
                                <td>Test initialization</td>
                                <td>60+</td>
                                <td>Required for known state before test</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Power Meters</strong></td>
                                <td>Setup sequence</td>
                                <td>25+</td>
                                <td>Required for measurement accuracy</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Spectrum Analyzers</strong></td>
                                <td>Before measurements</td>
                                <td>15+</td>
                                <td>Required for clean measurement state</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                            <tr>
                                <td><strong>Oscilloscopes</strong></td>
                                <td>Test setup</td>
                                <td>20+</td>
                                <td>Required for proper triggering and scaling</td>
                                <td><span class="decision-badge decision-keep">KEEP</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="table-legend">
                        <strong>Table Legend:</strong> This table categorizes reset operations that are essential for proper test execution and equipment functionality. Data was collected by analyzing reset patterns in relation to test procedures and equipment requirements. The Count column represents approximate instances across all analyzed files. Justification explains why these resets are necessary for reliable operation. All essential resets receive KEEP decision as they are fundamental to maintaining test integrity and equipment reliability.
                    </div>
                </div>

            <!-- Recommendations -->
            <section id="recommendations" class="section">
                <h2>🎯 Summary and Recommendations</h2>

                <h3>🚀 Priority 1 Actions (Low Risk, High Impact)</h3>
                <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #27ae60; margin: 20px 0;">
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Optimize SPI Delays:</strong> Reduce 0.5s delays to 0.01-0.05s (Est. savings: 20-25s per test)</li>
                        <li><strong>Document Undocumented Delays:</strong> Add comments to 200+ undocumented delays</li>
                        <li><strong>Standardize Common Operations:</strong> Create consistent timing for RF operations, measurements</li>
                    </ol>
                </div>

                <h3>⚡ Priority 2 Actions (Medium Risk, Medium Impact)</h3>
                <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db; margin: 20px 0;">
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Reduce Conservative Delays:</strong> Test and optimize 50+ conservative delays</li>
                        <li><strong>Consolidate Reset Operations:</strong> Eliminate redundant resets in cleanup sequences</li>
                        <li><strong>Implement Configurable Timing:</strong> Make delays parameterizable for different equipment</li>
                    </ol>
                </div>

                <h3>🔍 Priority 3 Actions (Higher Risk, Requires Testing)</h3>
                <div style="background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #f39c12; margin: 20px 0;">
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Investigate Long Delays:</strong> Verify necessity of 30+ second delays</li>
                        <li><strong>Implement Parallel Operations:</strong> Enable concurrent equipment initialization</li>
                        <li><strong>Add Conditional Logic:</strong> Reset only when equipment state requires it</li>
                    </ol>
                </div>

                <h3>💰 Estimated Total Time Savings</h3>
                <div class="dashboard">
                    <div class="metric-card">
                        <h3>Conservative Estimate</h3>
                        <div class="metric-value">30-60s</div>
                        <div class="metric-description">Per test procedure</div>
                    </div>
                    <div class="metric-card">
                        <h3>Aggressive Estimate</h3>
                        <div class="metric-value">60-120s</div>
                        <div class="metric-description">Per test procedure</div>
                    </div>
                    <div class="metric-card">
                        <h3>Annual Impact</h3>
                        <div class="metric-value">500-2000h</div>
                        <div class="metric-description">Assuming 10,000 test runs/year</div>
                    </div>
                </div>
            </section>

            <!-- Appendices -->
            <section id="appendix" class="section">
                <h2>📚 Appendices</h2>

                <button class="collapsible">📋 Appendix A: Detailed Code Examples</button>
                <div class="collapsible-content">
                    <h4>✅ Well-Documented Delay Example</h4>
                    <div class="code-snippet">
cmd = ':ATC:DME:POWER ' + str(signalLevel)
atc.write(cmd)
time.sleep(2)    # This delay allows enough time for ATC to be set with new lower power level and for the LRU to respond to the change in power level.
                    </div>
                    <p><strong>Analysis:</strong> This is an excellent example of proper documentation. The comment clearly explains both the equipment behavior (ATC power setting) and the system response requirement (LRU response time).</p>

                    <h4>❌ Poorly Documented Delay Example</h4>
                    <div class="code-snippet">
#Turn on RF
atc.gwrite(":ATC:XPDR:RF ON")
time.sleep(10)

atc.waitforstatus()

#Measure Frequency
time.sleep(2)
Frequencies[0] = atc.getPulseFrequency(4)
                    </div>
                    <p><strong>Analysis:</strong> These delays lack documentation. The 10-second delay is likely for RF stabilization, and the 2-second delay for measurement settling, but this should be explicitly documented.</p>

                    <h4>🔧 SPI Device Delay Pattern</h4>
                    <div class="code-snippet">
UUTReg.writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, genSPIQword(aterm, LINK_ID, DEVICE_ID, WRITE, DATA_BYTE_COUNT,SLAVE_ADDRESS, DATA))
time.sleep(0.5)
                    </div>
                    <p><strong>Analysis:</strong> This 0.5-second delay appears after every SPI register write. Modern SPI operations typically complete in microseconds, suggesting this delay could be significantly reduced.</p>
                </div>

                <button class="collapsible">⚠️ Appendix B: Risk Assessment Matrix</button>
                <div class="collapsible-content">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Risk Level</th>
                                    <th>Criteria</th>
                                    <th>Examples</th>
                                    <th>Mitigation Strategy</th>
                                    <th>Decision</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>LOW</strong></td>
                                    <td>Software timing, no hardware dependency</td>
                                    <td>SPI delays, file I/O</td>
                                    <td>Test with reduced delays in controlled environment</td>
                                    <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                                </tr>
                                <tr>
                                    <td><strong>MEDIUM</strong></td>
                                    <td>Equipment settling time, some variability</td>
                                    <td>Measurement delays, short equipment waits</td>
                                    <td>Gradual reduction with extensive testing</td>
                                    <td><span class="decision-badge decision-optimize">OPTIMIZE</span></td>
                                </tr>
                                <tr>
                                    <td><strong>HIGH</strong></td>
                                    <td>Critical equipment stabilization</td>
                                    <td>RF turn-on, mode switching</td>
                                    <td>Requires equipment specification review</td>
                                    <td><span class="decision-badge decision-investigate">INVESTIGATE</span></td>
                                </tr>
                                <tr>
                                    <td><strong>CRITICAL</strong></td>
                                    <td>Safety or equipment protection</td>
                                    <td>Power-on sequences, thermal settling</td>
                                    <td>Do not modify without vendor consultation</td>
                                    <td><span class="decision-badge decision-keep">KEEP</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <button class="collapsible">🚀 Appendix C: Implementation Roadmap</button>
                <div class="collapsible-content">
                    <h4>Phase 1: Low-Risk Optimizations (Weeks 1-2)</h4>
                    <ul style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                        <li><strong>SPI Delay Reduction:</strong> Test 0.5s → 0.05s reduction</li>
                        <li><strong>Documentation Addition:</strong> Add comments to undocumented delays</li>
                        <li><strong>Retry Loop Optimization:</strong> Reduce 1s → 0.3s delays</li>
                    </ul>

                    <h4>Phase 2: Medium-Risk Optimizations (Weeks 3-6)</h4>
                    <ul style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                        <li><strong>Equipment Delay Testing:</strong> Systematically test delay reductions</li>
                        <li><strong>Reset Consolidation:</strong> Eliminate redundant reset operations</li>
                        <li><strong>Standardization:</strong> Implement consistent timing patterns</li>
                    </ul>

                    <h4>Phase 3: High-Risk Investigations (Weeks 7-12)</h4>
                    <ul style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                        <li><strong>Long Delay Analysis:</strong> Investigate 30+ second delays</li>
                        <li><strong>Equipment Specification Review:</strong> Verify manufacturer requirements</li>
                        <li><strong>Parallel Operation Implementation:</strong> Enable concurrent operations</li>
                    </ul>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.nav-item').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    // Update active nav item
                    document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
                    this.classList.add('active');

                    // Smooth scroll to target
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Collapsible sections functionality
        document.querySelectorAll('.collapsible').forEach(button => {
            button.addEventListener('click', function() {
                this.classList.toggle('active');
                const content = this.nextElementSibling;

                if (content.classList.contains('active')) {
                    content.classList.remove('active');
                } else {
                    content.classList.add('active');
                }
            });
        });

        // Table sorting functionality
        function sortTable(table, column, asc = true) {
            const dirModifier = asc ? 1 : -1;
            const tBody = table.tBodies[0];
            const rows = Array.from(tBody.querySelectorAll('tr'));

            const sortedRows = rows.sort((a, b) => {
                const aColText = a.querySelector(`td:nth-child(${column + 1})`).textContent.trim();
                const bColText = b.querySelector(`td:nth-child(${column + 1})`).textContent.trim();

                return aColText > bColText ? (1 * dirModifier) : (-1 * dirModifier);
            });

            while (tBody.firstChild) {
                tBody.removeChild(tBody.firstChild);
            }

            tBody.append(...sortedRows);
        }

        // Add click handlers to table headers for sorting
        document.querySelectorAll('table th').forEach((headerCell, index) => {
            headerCell.addEventListener('click', () => {
                const table = headerCell.closest('table');
                const currentIsAscending = headerCell.classList.contains('asc');

                // Remove sorting classes from all headers
                table.querySelectorAll('th').forEach(th => th.classList.remove('asc', 'desc'));

                // Add appropriate class to current header
                headerCell.classList.toggle('asc', !currentIsAscending);
                headerCell.classList.toggle('desc', currentIsAscending);

                sortTable(table, index, !currentIsAscending);
            });
        });

        // Animate sections on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all sections
        document.querySelectorAll('.section').forEach(section => {
            observer.observe(section);
        });

        // Mobile menu toggle (for responsive design)
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('open');
        }

        // Add mobile menu button if needed
        if (window.innerWidth <= 768) {
            const mobileMenuBtn = document.createElement('button');
            mobileMenuBtn.innerHTML = '☰ Menu';
            mobileMenuBtn.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 1001;
                background: #3498db;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                cursor: pointer;
            `;
            mobileMenuBtn.onclick = toggleMobileMenu;
            document.body.appendChild(mobileMenuBtn);
        }

        // Update navigation active state on scroll
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('.section');
            const navItems = document.querySelectorAll('.nav-item');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === `#${current}`) {
                    item.classList.add('active');
                }
            });
        });

        // Print optimization
        window.addEventListener('beforeprint', () => {
            document.querySelectorAll('.collapsible-content').forEach(content => {
                content.classList.add('active');
            });
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            // Set first nav item as active
            const firstNavItem = document.querySelector('.nav-item');
            if (firstNavItem) {
                firstNavItem.classList.add('active');
            }

            // Animate initial load
            setTimeout(() => {
                document.querySelectorAll('.section').forEach((section, index) => {
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 300);
        });
    </script>
</body>
</html>
