## defines all of the Observation Point Selection Table descriptions
## and put them into an array to be called upon later
## Observation Point Selection Table corresponds to Table 3-54 in the TPLRF_ARD
## and with FPGA Revision ID 0xE056

description = []


## Selection Value = 0
description.append('ADC_INPUT')
## Selection Value = 1
description.append('TCAS_DDC_CIC2')
## Selection Value = 2
description.append('TCAS_DDC_FIR')
## Selection Value = 3
description.append('TCAS_DDC_MIXER')
## Selection Value = 4
description.append('TCAS_DDC_CIC2')
## Selection Value = 5
description.append('TCAS_DDC_FIR')
## Selection Value = 6
description.append('TCAS_BEAM_STEER_LOG')
## Selection Value = 7
description.append('DDC_IQ_OUTPUT')
## Selection Value = 8
description.append('TCAS_MERGED_LOG_PHASE')
## Selection Value = 9
description.append('TCAS_DDC_FIR_TOP')
## Selection Value = 10
description.append('TCAS_DDC_MIXER[2]')
## Selection Value = 11
description.append('XPDR_DDC_FIR_TOP')
## Selection Value = 12
description.append('XPDR_DDC_MIXER_TOP')
## Selection Value = 13
description.append('TCAS_DDC_MIXER[3]')
## Selection Value = 14
description.append('XPDR_DDC_FIR_BOT')
## Selection Value = 15
description.append('XPDR_DDC_MIXER_BOT')
## Selection Value = 16
description.append('TCAS_DDC_DOWN_SAMPLED_OUTPUT[2]')
## Selection Value = 17
description.append('XPDR LOG OUTPUT')
## Selection Value = 18
description.append('TCAS_DDC_LOG_AMP_OUTPUT[0]')
## Selection Value = 19
description.append('TCAS_DDC_LOG_AMP_OUTPUT[1]')
## Selection Value = 20
description.append('UAT_FIR_OUT')
## Selection Value = 21
description.append('TCAS_DDC_LOG_AMP_OUTPUT[3]')
## Selection Value = 22
description.append('TCAS_DDC_MAGNITUDE_OUTPUT[0]')
## Selection Value = 23
description.append('DME_DDC_FIR')
## Selection Value = 24
description.append('DME_DDC_MIXER')
## Selection Value = 25
description.append('DME_LOG_OUT')
## Selection Value = 26
description.append('TCAS_DDC_LOG_SUM')
## Selection Value = 27
description.append('XPDR_DDC_CIC2[0]')
## Selection Value = 28
description.append('XPDR_DDC_FIR[0]')
## Selection value = 29
description.append('XPDR_DDC_MIXER[0]')
## Selection Value = 30
description.append('CALIBRATION')
# ## Selection Value = 31
# description.append('XPDR_DDC_FIR[1]')
# ## Selection Value = 32
# description.append('XPDR_DDC_MIXER[1]')
# ## Selection Value = 33
# description.append('XPDR_DDC_CIC2[2]')
# ## Selection Value = 34
# description.append('XPDR_DDC_FIR[2]')
# ## Selection Value = 35
# description.append('XPDR_DDC_MIXER[2]')
# ## Selection Value = 36
# description.append('XPDR_DDC_CIC2[3]')
# ## Selection Value = 37
# description.append('XPDR_DDC_FIR[3]')
# ## Selection Value = 38
# description.append('XPDR_DDC_MIXER[3]')
# ## Selection Value = 39
# description.append('XPDR_DDC_OUTPUT[0]')
# ## Selection Value = 40
# description.append('XPDR_DDC_OUTPUT[1]')
# ## Selection Value = 41
# description.append('XPDR_DDC_OUTPUT[2]')
# ## Selection Value = 42
# description.append('XPDR_DDC_OUTPUT[3]')
# ## Selection Value = 43
# description.append('XPDR_DDC_LOG_AMP_OUTPUT[0]')
# ## Selection Value = 44
# description.append('XPDR_DDC_LOG_AMP_OUTPUT[1]')
# ## Selection Value = 45
# description.append('XPDR_DDC_LOG_AMP_OUTPUT[2]')
# ## Selection Value = 46
# description.append('XPDR_DDC_LOG_AMP_OUTPUT[3]')
# ## Selection Value = 47
# description.append('XPDR_DDC_MAGNITUDE_OUTPUT[0]')
# ## Selection Value = 48
# description.append('XPDR_DDC_MAGNITUDE_OUTPUT[1]')
# ## Selection Value = 49
# description.append('XPDR_DDC_MAGNITUDE_OUTPUT[3]')
# ## Selection Value = 50
# description.append('XPDR_DDC_MAGNITUDE_OUTPUT[4]')
# ## Selection Value = 51
# description.append('UAT_DDC_CIC2[0]')
# ## Selection Value = 52
# description.append('UAT_DDC_FIR[0]')
# ## Selection Value = 53
# description.append('UAT_DDC_MIXER[0]')
# ## Selection Value = 54
# description.append('UAT_DDC_CIC2[1]')
# ## Selection Value = 55
# description.append('UAT_DDC_FIR[1]')
# ## Selection Value = 56
# description.append('UAT_DDC_MIXER[1]')
# ## Selection Value = 57
# description.append('UAT_DDC_OUTPUT[0]')
# ## Selection Value = 58
# description.append('UAT_DDC_OUTPUT[1]')
# ## Selection Value = 59
# description.append('DME_DDC_CIC2')
# ## Selection Value = 60
# description.append('DME_DDC_FIR')
# ## Selection Value = 61
# description.append('DME_DDC_MIXER')
# ## Selection Value = 62
# description.append('DME_DDC_OUTPUT')
# ## Selection Value = 63
# description.append('DME_DDC_LOG_AMP_OUTPUT')
# ## Selection Value = 64
# description.append('DME_DDC_MAGNITUDE_OUTPUT')
# ## Selection Value = 65
# description.append('DAC_OUTPUT')
# ## Selection Value = 66
# description.append('DUC_PULSE_SHAPE_FIR_OUTPUT')
# # Selection Value = 67
# description.append('BEARING_PROCESSOR_CORDIC_PHASE')
# # Selection Value = 68
# description.append('BEARING PROCESSOR CORDIC PHASE AND MAGNITUDE')
# # Selection Value = 69
# description.append('BEARING PROCESSOR CALIBRATION & TX MONITOR ALIGN VECTORS')
# # Selection Value = 70
# description.append('BEARING PROCESSOR RX ALIGN VECTORS')

## end, updated for revision E056
