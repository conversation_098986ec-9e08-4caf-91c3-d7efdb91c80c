# -*- coding: utf-8 -*-
"""
Created on Wed Jun 24 11:43:40 2020

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS Qualification Test Group
         
Description:
    This class starts the ARINC_Server program and provides utility functions 
    to read/write to the server for ARINC 429 I/O.  Currently only DME labels
    are setup.
    
    NOTE: ONLY INSTANCIATE THIS CLASS ONCE, OHERWISE IT WILL TRY TO CREATE
          MULTIPLE INSTANCES OF THE SERVER.
    
Inputs:  see class methods
Outputs: see class methods

History:

06/24/2020   MRS  Initial Release
06/29/2020   AS   Added DME Decode Commands
07/10/2020   MRS  Added TCAS Commands
11/3/2020    MRS  Updates for ATE_RM
11/11/2022   CS   Added DME Standby function
11/18/2022   CS   Added TCAS Standby function
"""

import socket
import time
import subprocess
import os
import re


class ARINC_Client():
    def __init__(self, ate_rm, cmd_str):

        # Add conneciton to Resource Manager, ignore linting errors
        self.resourceManager = ate_rm        
       
        # Initialize IP, Port and Message buffer size
        self.TCP_IP = '127.0.0.1'
        self.TCP_PORT = 20633
        self.BUFFER_SIZE = 2048
        self.cmd_str = cmd_str
        self.data = ""
        
        #KILL the server if its already running
        kill_str = "taskkill /f /im " + self.cmd_str + ".exe"
        os.system(kill_str)
        time.sleep(5)

        # Start the ARINC Server Application
        result = subprocess.Popen([self.cmd_str])
        time.sleep(5)
        self.resourceManager.logMessage(1,"Server Started - ARINC 429 Server Running")
        
        #Create a Socket
        try:
            self.s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.resourceManager.logMessage(1,"Socket Created - ARINC 429 Socket Created")           
        except socket.error:
            self.resourceManager.logMessage(3,"Socket Not Created - Failed to Create Socket")
        
        #Create a Connection
        try:
             self.s.connect((self.TCP_IP, self.TCP_PORT))
        except socket.error:
              self.resourceManager.logMessage(3,"Connect Failed - Failed to Connect to Server")      


   
##################################################
# Basic Commands
##################################################
              
    def close_ARINC_Client(self):
        """Closes the Connections and Socket. """
        self.s.close()
        
    def kill_ARINC_Server(self):
        """Kills the A429 Server. """
        kill_str = "taskkill /f /im " + self.cmd_str + ".exe"
        os.system(kill_str)
        time.sleep(5)
        
    def DME_Read(self,cmd_str):
        """ This routine connect to the server and performs a DME Read.  The 'cmd_str' should be
        a comma separated string with the READ command,BUS, and Label ("READ,DME_OUT,035"). It 
        returns a string with the Label and 32 Data (or a Null string on error."""
        self.data = ""
        self.s.send(cmd_str)
        self.data = self.s.recv(self.BUFFER_SIZE)
        return self.data
    
    def DME_Write(self,cmd_str):
        """ This routine connect to the server and performs a DME Write.  The 'cmd_str' should be
        a comma separated string with the WRITE command,bus,label,ssm,sdi,data(bits 28 thru 11) 
        ("WRITE,DME_IN,035,3,1,0x00000ff). It returns a string echoing the command (or a Null string on error."""
        self.data = ""
        self.s.send(cmd_str)
        self.data = self.s.recv(self.BUFFER_SIZE)
        return self.data

    def DMEstandby(self):
        """ Writes to ARINC 429 word  """
        # Form statement to send all zeros in Mode bits 11-13 to  put DME in standby mode 
        # DME standby mode = 000 0000 0000 0110 0000 = 0x60     # bit 29 is left, bit 11 is right
        hex_str = '0000060' # must be 7 bit hex format 
        statement = "WRITE,DME_IN,035,0,0,0x" + hex_str 
        b1 = bytes(statement,'utf-8')
        return self.DME_Write(b1)
    
    def TCASstandby(self):
        #rm = ate_rm()
        #ARINC = ARINC_Client(rm,r"C:\Honeywell\TXD_Qual\TXDLib\Handlers\ARINC_Server")
        #time.sleep(5)
        """ Writes to ARINC 429 word  """
        # form statement to send 1 in TCAS Sensitivity bit 15 to put TCAS in standby mode 
        # TCAS standby (STBY) mode = 000 0000 0000 0001 0010 = 0x12
        hex_str = '0000012' # must be 7 bit hex format 
        statement = "WRITE,TCAS_IN,016,0,0,0x" +str(hex_str)
        b1 = bytes(statement,'utf-8')
        return self.DME_Write(b1)

##################################################
# Decode Command
##################################################
    
    def decode_arinc_word(self,data):
        """Decodes the A429 Word returned from Server. Returns array with 
        label at index 1, SDI at index 2, data at index 3, and SSM at index 4"""
        #print("DATA: ",data)
        #data = b'READ,DME_OUT,247 DME_247=000000D0 '
        #datab = bytes(data)
        #convert bytes to back to string
        data_s = data.decode('utf-8')
        
        #Split the string into 2 Tokens
        l = data_s.split(' ')     #split by spaces in the return line
        l1 = l[1]                 #only interested in 2nd half
        #find the '='
        x = l1.find('=')
        ls = l1[x+1:]               #substring only contains hex ascii 
        hex_int = int(ls, 16)       #convert to integer
        
        #parse the field in the ARINC word
        label = hex_int & 0x000000FF
        temp = '{0:03b}'.format(label)      # convert from int to binary string
        label = int(temp[::-1], 2)          # flip and convert back to int
        sdi =  (hex_int & 0x00000300) >> 8
        val =  (hex_int & 0x1ffffC00) >> 10
        ssm =  (hex_int & 0x70000000) >> 28
        print(("label: %o, sdi: %d, val: %x, ssm: %d") % (label,sdi,val,ssm))    
        return [label, sdi, val, ssm]
    
    def getSSM(self):
        """ Decodes A429 Data from 202 and returns SSM"""
        word = self.DME_Read(b"READ,DME_OUT,202")   # get word from 202
        decode = self.decode_arinc_word(word)
        data = decode[3]                            # pull out SSM
        ssm = (data & 0x6) >> 1
        #print("SSM:", '{0:02b}'.format(ssm))
        return '{0:02b}'.format(ssm)

    def getChannel_035(self):
        """ Decodes A429 Data from 035 and returns channel"""
        word = self.DME_Read(b"READ,DME_OUT,035") # get word from 035
        decode = self.decode_arinc_word(word)
        data = decode[2]   
        # grab each digit
        d1 = int((data >> 16) & 0x07)             # 10's place
        d2 = int((data >> 12) & 0x00F)            # 1's place
        d3 = int((data >> 8) & 0x000F)            # .1's place
        d4 = int((data >> 7) & 0x00001)           # .01's place
        
        # calculate channel
        channel = 100 + (10*d1) + d2 + (.1*d3) + (.05*d4)
        return channel

    def getDistance_201(self):
        """ Decodes A429 Data from 201 and returns distance"""
        word = self.DME_Read(b"READ,DME_OUT,201") # get word from 201
        decode = self.decode_arinc_word(word)
        data = decode[2]                            # pull out data bits
        #x = '{0:05x}'.format(data)
        #x = int(x) / 100
        #print(x)
        
        # get each digit
        d1 = int((data >> 16) & 0x07)             # 100's place
        d2 = int((data >> 12) & 0x00F)            # 10's place
        d3 = int((data >> 8) & 0x000F)           # 1's place
        d4 = int((data >> 4) & 0x0000F)          # .1's place
        d5 = int((data) & 0x00000F)         # .01's place
        distance = (100*d1) + (10*d2) + d3 + (.1*d4) + (.01*d5) 
 
        return distance


###################################################
#  Encode Command
###################################################     

    def writeChannel(self, channel):
        """ Writes to ARINC 429 word  """
        # separate channel number into each digit
        d1 = int(channel * .1) % 10           # 10's place
        d2 = int(channel) % 10            # 1's place
        d3 = int(channel * 10) % 10           # .1's place
        if((int(channel * 100) % 10) == 5):
            d4 = 1
        else: 
            d4 = 0
        
        # convert each digit to binary and appends them along with rest of data
        data = '{0:03b}'.format(d1)+'{0:04b}'.format(d2)+'{0:04b}'.format(d3)+'{0:01b}'.format(d4)+"1100001"    #"1100001"
        # convert string of bits to integer
        data_int = int(data, 2)

        # format as a 7-bit hex string
        hex_str = '{0:07x}'.format(data_int)

        # form statement to send 
        statement = "WRITE,DME_IN,035,0,0,0x" + hex_str
        b1 = bytes(statement,'utf-8')
        #self.DME_Write(b"WRITE,DME_IN,035,0,0,0x00179e1")
        self.DME_Write(b1)
        return 0
        #self.DME_Write(b"WRITE,DME_IN,035,1,0,0x" + hex_str)
        #self.DME_Write(b"WRITE,DME_IN,035,1,0,0x0011961")
        #print(statement)
        
###################################################
#  TCAS Commands
###################################################     

    def TCAS_Read(self,cmd_str):
        """ This routine connects to the server and performs a TCAS Read.  The 'cmd_str' should be
        a comma separated string with the READ command,BUS,and Command.  For TCAS there are only
        two valid commands 'INT' or 'ALL'.  Example: b"READ,TCAS_OUT,INT".  For the 'INT' command
        the server will return the number of Intruders, for the 'ALL' command the server will 
        return a comma separated list of all intruders with Range, Altitude and Bearing for each
        intruder. """
        
        self.s.send(cmd_str)
        self.data = self.s.recv(self.BUFFER_SIZE)
        return self.data
    
    def TCAS_Decode(self,data):
        """ This routined decodes the Server return string for TCAS.  It separates the return
        string into Lists where each entry in the list is the Intruder No, Range, Altitude
        and Bearing. """

        itr = []          #intruders
        rng = []          #range
        alt = []          #altitude
        brg = []          #bearings
        
        #convert bytes received from server back to python string
        data_s = data.decode('utf-8')
        #find all occurances of '-I:'
        print("retrun string: ",data_s)
        res = [i.start() for i in re.finditer("-I:", data_s)]
        
        #get all intruders, ranges, bearings, altitudes
        for x in range(len(res)):
            ss = data_s[res[x]:]
            sss = ss.split(',')
            #take the first 4 substrings as itr,rng,alt,brg
            ssss = sss[0][3:]       #intruder
            itr.append(int(ssss))
            ssss = sss[1][2:]       #range
            rng.append(float(ssss))
            ssss = sss[2][2:]       #altitude
            alt.append(float(ssss))
            ssss = sss[3][2:]       #bearing
            brg.append(float(ssss))
        
        return itr,rng,alt,brg

    
        