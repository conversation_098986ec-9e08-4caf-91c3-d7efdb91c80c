﻿# -*- coding: utf-8 -*-
"""
Created on Tue Jan  5 08:58:20 2021

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the FAR43 A Reply Frequency requiremenst for
             Reply Transmission Frequencies.
             
             (1) For all classes of ATCRBS transponders, interrogate the 
             transponder and verify that the reply frequency is 1090 ±3 Megahertz
             (MHz).
             (4) For classes 1A, 2A, 3A, and 4 Mode S transponders, interrogate 
             the transponder and verify that the reply frequency is 1090 ±1 MHz.
             
INPUTS:      ate_rm,ATC5000NG,RFBOB
OUTPUTS:     'Frequencies' array of frequencies for ModeA and ModeA/S

HISTORY:
01/05/2021   MRS    Initial Release.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import RFBOB
from TXDLib.Handlers import DigitalBOB
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################
def init_RFBOB(rfbob):
    """ initializes the RF BOB to Prim & Sec to BOTTOM port --- should be default. """
    #rfbob.connect()
    rfbob.setSwitch(0,0)   #Primary Bottom Port
    rfbob.setSwitch(1,0)   #Secondary Bottom Port
    rfbob.setSwitch(10,1)
    #rfbob.disconnect()

def RFBOB_Connect(rfbob):
    """ Connects to the RFBOB. """
    rfbob.connect()

def RFBOB_Disconnect(rfbob):
    """ Disconnect from the RFBOB. """
    rfbob.disconnect()

def Configure_DigitalBOB(dgbob):
    """ Configures Digital BOB for Power Meter Trigger. """
    dgbob.setTriggerSignal(2,15)
    dgbob.setTriggerSignal(8,11)



##############################################################################
################# MAIN     ##################################################
##############################################################################
        
def FAR43_A(rm,atc,rfbob):
    """ FAR43, A - Reply Frequency """
    rm.logMessage(2,"*** FAR43 A, Reply Frequency ***")
    
    #initialize rfbob
    init_RFBOB(rfbob)

    #Initialize Frequencies
    Frequencies = [0.0 , 0.0]                               #Values read by TestStand

    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set Up Transponder -- MODE A
    atc.transponderModeA()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna
        
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(25)
    
    atc.waitforstatus()    

    #Measure Frequency
    Frequencies[0] = atc.getPulseFrequency(2)    
    
    # fix for erroneous frequency
    count = 0
    while Frequencies[0] == 20 and count < 10:
        Frequencies[0] = atc.getPulseFrequency(2)
        count = count + 1            
            
    
       
    #print result at this frequency
    print(("RESULT1: Frequency %f") % (Frequencies[0])) 


    #Turn OFF RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    
    
    #Set Up Transponder -- MODE A/MODE S All Call
    atc.transponderModeAS()
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(25)
    
    atc.waitforstatus()    
        
   
    #Measure Frequency
    Frequencies[1] = atc.getPulseFrequency(2)    

    # fix for erroneous frequency reply
    count = 0
    while Frequencies[1] == 20 and count < 10:
        Frequencies[1] = atc.getPulseFrequency(2)
        count = count + 1            



    #print result at this frequency
    print(("RESULT2: Frequency %f") % (Frequencies[1])) 

    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
   
    rm.logMessage(2,"Frequecies: %f,%f" % (Frequencies[0],Frequencies[1]))   
    rm.logMessage(2,"Done, closing session")

    
    return Frequencies

##############################################################################
#run as main from command line
if __name__ == "__main__":
    
    #SetUP Resource Manager
    rm = ate_rm()
    
    #Initialize the RFBOB
    rf_obj = RFBOB(rm)
    rf_obj.connect()

    #initialize the DigitalBOB
    dg_obj = DigitalBOB(rm)
    dg_obj.connect()
  
    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()
  
    #Run the FAR
    res = FAR43_A(rm,atc_obj,rf_obj)
    
    atc_obj.close()
    rf_obj.disconnect()
    dg_obj.disconnect

