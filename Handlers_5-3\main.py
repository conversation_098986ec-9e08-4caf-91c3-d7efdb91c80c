from B4500CPwrMeter import *
from Compression import setupPwrMeterXPDRModeS
import ate_rm

ate = ate_rm.ate_rm()
bpm = B4500CPwrMeter(ate)

#setupPwrMeterXPDRModeS(ate)

markers = bpm.CH2_Marker_Power()
powerArray = bpm.basicQuery('FETCh1:ARRay:MARKer:POWer?')
print(powerArray)


print("Markers: ")
print(markers)
print("Should say list: " + str(type(markers))) #ensure it's a list
print("Marker types: " + str(type(markers[0])))  # check int or str


bpm.close()
ate.cleanup()