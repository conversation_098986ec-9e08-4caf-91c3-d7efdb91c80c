# -*- coding: utf-8 -*-
"""
SCRIPT IDENTIFIER:  DO282_24822.py

MODULE HISTORY:
        
AUTHOR: H157797

MODULE DESCRIPTION:
  This script creates the test scenario and sends the commands to RF generator like ATC5000NG 
  for DO-282 Section *******.2 Verification of Receiver Desired Signal Dynamic Range.

    Provide the UUT with Long ADS-B Messages having:
    . RF Power Level: -90 dBm
    . Frequency: 978 MHz
    . FM Deviation: 625 kHz (measured at the minimum eye pattern opening per $*******)
    . Message Contents: Long ADS-B Message with pseudo-random payload data, and valid FEC Parity field.
    . Message Rate: 100 per second(recommended minimum)
  
DEPENDENCY:
   Python v3.6 or above
   UAT_CONNECTION.py
   FEC.py

NOTE:
   The cable line loss need to be considered. The loss of the message power levels could be set by 
   this script's input argument. By default, it is set to 3db (possitive) assuming the cable line 
   loss is 3db.

HONEYWELL DIFFERENCE/DEVIATION: N/A

"""
import os
import sys
import time
import datetime
#import argparse
import csv

from TXDLib.Handlers import ate_rm
from TXDLib.Procedures.DO282 import UAT_CONNECTION as uc
from TXDLib.Procedures.DO282 import FEC as fec
from TXDLib.Procedures.DO282 import UAT_LOGGING as ul



#parser = argparse.ArgumentParser()
#parser = argparse.ArgumentParser(description='DO-282 *******.1.1 Test scenario script')
#parser.add_argument("-l", "--lineloss", default=3, type=int, help="Cable Line loss, unit: db")
#args = parser.parse_args()

#Cable line loss by default 3db
CABLE_LINE_LOSS = 3.0
INPUT_FILE_PATH = "DO282_24822_INPUT"
MINIMAL_SIGNAL_LEVEL = -90
MAX_SINGAL_LEVEL = -20


longADSB = ''
fecOfLongAdsb = ''

#Apply ADS-B Input Messages at maximum negative frequency offset.
#Parameters:
#   channelSel: 1: UAT RX1; 2: RX2 
#   powerValue: the power level of the squitter messages 
#   inputLogFile: The file which is used to log the random Long ADS-B messages
def test_case(channelSel=None, powerValue=None,inputLogFile=None):
    f = open(inputLogFile,'w',encoding='utf-8',newline='')

    if (channelSel != 1 and channelSel != 2):
        sys.exit("Select the UAT RX Channel: 1 for RX1; 2 for RX2")

    #connect to the ATC target
    uc.create_connection()
    
    # set mode to UAT mode
    uc.set_test_mode("UAT")
            
    #initial the UAT scenario    
    uc.set_scenario(reset=True, channel=channelSel, run_time=10, test_mode_type="Normal", mso_step=30)
        
    #100 Long ADS-B Message
    try:
        csvWriter = csv.writer(f)
        csvWriter.writerow(['SYSTEM TIME','MSG ID','MESSAGE'])
        for i in range(100):
            longAdsb = uc.generate_random_uat_msg(fec.LONG_ADSB_MSG_TYPE)
            fecOfLongAdsb = fec.fec_parity(fec.LONG_ADSB_MSG_TYPE, longAdsb)
            csvWriter.writerow([datetime.datetime.now().strftime('%H:%M:%S.%f'),i+1,longAdsb+fecOfLongAdsb])
            uc.set_sta_long_adsb(intruderNumber=i+1, data=longAdsb, fec=fecOfLongAdsb, power=powerValue)
    finally:
        f.close()
    
    uc.scenario_start(start=1)
    
    #Sleep 40 sec for the scenario loading into RF generator
    time.sleep(40)
        
    uc.close_connection()  

def receiver_diversity(rm):

    inputFile = os.getcwd()+"\\"+INPUT_FILE_PATH
    ul.mkdir(inputFile)
    caseId = 0
   
    if MINIMAL_SIGNAL_LEVEL > MAX_SINGAL_LEVEL:
        sys.exit("Err:  The minimum signal level " + str(MINIMAL_SIGNAL_LEVEL) + "dBm is greater than " + str(MAX_SINGAL_LEVEL) + "dBm\n")        
    
    #uc.set_test_mode("UAT")
    
    for rxNo in range(1,3):
        step = 0
        caseId += 1
        
        rm.logMessage(0,"-----------------------------------------------------------------------------------------")
        rm.logMessage(0,"Running Test Case #" + str(caseId) + " On UAT RX" + str(rxNo))
        rm.logMessage(0,"-----------------------------------------------------------------------------------------")    
        rm.logMessage(0,"		Apply the Desired Message Signal with the Center Frequency:")
        rm.logMessage(0,"                978 MHz")
        rm.logMessage(0,"		Modulation Deviation:  625 KHz")
        rm.logMessage(0,"		Message Content: 100 Long ADS-B Messages per second with pseudo-random payload data and valid FEC Parity")

    #@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    #TBD:  STEP 2: Measure the UUT receiver sensitivity (The measured value should be used in the step 3)
    #Details: Decrease the input power level and determine the minimum RF signal required to produce an 
    #         average reception rate of 99 percent by the UUT receiver. At least 1000 message receptions 
    #         should be measured in making this determination. Verify that this RF signal level is in 
    #         compliance with the limits specified in $2.2.8.2.2.       
    #@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
        
        rm.logMessage(0,"Increase the input signal by 10dB steps, up to a level of " + str(MAX_SINGAL_LEVEL) + "dBm.")
        rm.logMessage(0,"Verify the receiver properly detects and decodes at least 99% of the Desired Messages.")
          
        for i in range(MINIMAL_SIGNAL_LEVEL, MAX_SINGAL_LEVEL, 10):
            rm.logMessage(0,"Message Power Level: " + str(i))
            step += 1
            inputFilePath = inputFile+'\\CASE_'+str(caseId)+'_RX_'+str(rxNo)+'_step'+str(step)+'.csv'
            test_case(channelSel=rxNo, powerValue = i + CABLE_LINE_LOSS, inputLogFile=inputFilePath)
        
        rm.logMessage(0,"Sleep 10 secs...")
        time.sleep(10)
        
        rm.logMessage(0,"Max Message Power Level: " + str(MAX_SINGAL_LEVEL))
        step += 1
        inputFilePath = inputFile+'\\CASE_'+ str(caseId) +'_RX_'+str(rxNo)+'_step'+str(step)+'.csv'
        test_case(channelSel=rxNo, powerValue = MAX_SINGAL_LEVEL + CABLE_LINE_LOSS, inputLogFile=inputFilePath)


if __name__ == '__main__':
   
    #SetUP Resource Manager
    rm = ate_rm()


    receiver_diversity(rm)


    