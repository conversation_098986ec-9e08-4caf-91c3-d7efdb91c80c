# observation point api
import json
import time
import os
import glob
from datetime import date
import socket
import base64



def request(requestId, index, triggerDelay=0x0, triggerSelection=0x0):
    time.sleep(.002)
    today = date.today()
    this_year = str(today)[:4]
    this_month = str(today)[5:7]
    this_day = str(today)[8:10]
    current_directory = os.getcwd()
    os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\request")
    data = {
        "observation_point_request": {
            "requestId": requestId,
            "observationPointTriggerDelay": str(hex(triggerDelay)),
            "observationPointTriggerSelection": str(hex(triggerSelection)),
            "observationPointSelection": str(hex(index)),
            "observationPointFragment": -1
             }
    }
    filename = "obs_pnt_tst_msg_" + str(requestId) + ".json"
    for removable_file in glob.glob("*.json_processed"):
        os.remove(removable_file)
    with open(filename, "w") as write_file:
        json.dump(data, write_file)
    time.sleep(0.5)
    os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\response\\observation_point_response")
    return_values = ""
    count_limit = 50 # 5 second timer
    output_file = (glob.glob("observation_point_response_" + str(requestId) + "_*" + "_" + this_year + "_" + this_month + "_" + this_day +  "_*.json"))
    while ((len(output_file) < 256) and (count_limit != 0)):
        output_file = (glob.glob("observation_point_response_" + str(requestId) + "_*" + "_" + this_year + "_" + this_month + "_" + this_day +  "_*.json"))
        time.sleep(0.1)
        # print(len(output_file))
        count_limit -= 1
    print(len(output_file))
    for i in range(len(output_file)):
        print(glob.glob("observation_point_response_" + str(requestId) + "_" + str(i) + "_*.json"))
        try:
            current_file = glob.glob("observation_point_response_" + str(requestId) + "_" + str(i) + "_*.json")[0]
        except IndexError:
            continue
        with open(current_file, "r") as read_file:
            json_response = json.load(read_file)
            print(json_response["observation_point_response"]["observationPointDataHex"])
            data = (json_response["observation_point_response"]["observationPointDataHex"])
            return_values += data
            #time.sleep(0.1)
        os.remove(current_file)
    buffer = []
    i = 0
    while i < len(return_values):
        buffer.append(return_values[i:i+16].zfill(16))
        i = i + 16
    os.chdir(current_directory)
    return buffer

def request_direct(requestId, index, triggerDelay=0x0, triggerSelection=0x0):
    UDP_IP = "************"
    UDP_PORT = 5121

    sock = socket.socket(socket.AF_INET, # Internet
                      socket.SOCK_DGRAM) # UDP

    sock.bind((UDP_IP, UDP_PORT))
    
    r = {
        "observation_point_request": {
            "requestId": requestId,
            "observationPointTriggerDelay": str(hex(triggerDelay)),
            "observationPointTriggerSelection": str(hex(triggerSelection)),
            "observationPointSelection": str(hex(index)),
            "observationPointFragment": -1
             }
    }
    r = str.encode(json.dumps(r))
    startTime = time.time()
    #print("socket connected")
    sock.sendto(r,('*************',UDP_PORT))
    print("request sent")
    #print(time.time()-startTime)
    return_values = ""
    i=0
    while i<256:
        data = sock.recv(2048)
        data = data.decode('utf-8').split('\x00')[0]
        data = json.loads(data)
        try:
            data = (base64.b64decode(data['observation_point_response']['observationPointDataBase64']))
            dataarr = bytearray(data)
            #data = data.hex()
            flippedData = bytearray(data)
            index = 0
            while (index<len(dataarr)):
                for j in range(0,8):
                    flippedData[index+j]=dataarr[(7-j)+index]
                index+=8
            data=flippedData.hex()
            #print(data)
            #print("received message: %s, data: %s" % (data['observation_point_response']['sequentialNumber'],base64.b64decode(data['observation_point_response']['observationPointDataBase64']).hex()))
            return_values += data
            i+=1
        except KeyError:
            pass
    
    buffer = []
    i = 0
    while i < len(return_values):
        buffer.append(return_values[i:i+16].zfill(16))
        i = i + 16
    #print("data collected")
    #print(time.time()-startTime)
    sock.close()
    #print("socket closed")
    #print(time.time()-startTime)
    return buffer

def request_no_acq(requestId, index, triggerDelay=0x0, triggerSelection=0x0):
    today = date.today()
    this_year = str(today)[:4]
    this_month = str(today)[5:7]
    this_day = str(today)[8:10]
    current_directory = os.getcwd()
    os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\request")
    indexWord = "0x80000000000000" + str(hex(index)[2:].zfill(2))
    print("index Word: " + indexWord)
    data = {
        "observation_point_request": {
            "requestId": requestId,
            "observationPointTriggerDelay": str(hex(triggerDelay)),
            "observationPointTriggerSelection": str(hex(triggerSelection)),
            "observationPointSelection": indexWord,
            "observationPointFragment": -1
             }
    }
    filename = "obs_pnt_tst_msg_" + str(requestId) + ".json"
    for removable_file in glob.glob("*.json_processed"):
        os.remove(removable_file)
    with open(filename, "w") as write_file:
        json.dump(data, write_file)
    time.sleep(1.5)
    os.chdir("C:\\txd_xd\\vprfb_scope\\vprfb_scope\\response\\observation_point_response")
    return_values = ""
    time.sleep(0.5)
    output_file = (glob.glob("observation_point_response_" + str(requestId) + "_*" + "_" + this_year + "_" + this_month + "_" + this_day +  "_*.json"))
    for i in range(len(output_file)):
        print(glob.glob("observation_point_response_" + str(requestId) + "_" + str(i) + "_*.json"))
        try:
            current_file = glob.glob("observation_point_response_" + str(requestId) + "_" + str(i) + "_*.json")[0]
        except IndexError:
            continue
        with open(current_file, "r") as read_file:
            json_response = json.load(read_file)
            return_values += (json_response["observation_point_response"]["observationPointDataHex"])
            #time.sleep(0.1)
        os.remove(current_file)
    buffer = []
    i = 0
    while i < len(return_values):
        buffer.append(return_values[i:i+16].zfill(16))
        i = i + 16
    os.chdir(current_directory)
    return buffer

def kick_off_data_acquisition(index, triggerSelection, triggerDelay):


    # then send the cal write
    return request_no_acq(13, index, triggerDelay=triggerDelay, triggerSelection=triggerSelection)

    # select op

