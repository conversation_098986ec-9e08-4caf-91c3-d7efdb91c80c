# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Pulse Decoder Characterics, Section *******
             
			 Step8: Sync Phase Reversal Position Tolerance
             For a ModeS Only All Call, at MTL+3 dBm, vary the spacing between
             the P6 synch phase reversal and either P2 or P6 as applicable by
             200 nsec from nominal spacing.  Measure the range over which
             ModeS replies are received.  Repeat for P1 levels of -50 and
             -21 dBm.
             
             
INPUTS:      RM, ATC, PathLoss
OUTPUTS:     ModeS_Replies - array of %replies for P1/P6 spacing, at three power levels

HISTORY:

04/23/2020   MRS    Initial Release.
05/11/2020   MRS    Cleanup
03/03/2021   MRS    Updates for new Handlers and Lobster.

                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# FUNCTIONS ##################################################
##############################################################################

def vary_P16(atc,rm,duration):
    cmd = ":ATC:XPDR:PUL:P16SPACING " + str(duration)
    rm.logMessage(0,"Test_2_3_2_5_Step8" + cmd)   
    atc.gwrite(cmd)
    time.sleep(15)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(1)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    
    rmax = max(replyrate)
    
    return rmax


##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_5_Step8(rm, atc,PathLoss):
    
    rm.logMessage(2,"*** DO-181E, Pulse Decoder Characterics: Sect *******_Step8 ***")
       

    #Results read by TestStand
    Power_Levels = ['-73.0', '-50.0', '-21.0']
    ModeS_Replies = [0.0,0.0,0.0,0.0,0.0,0.0]                                #Values read by TestStand

    #Adjust Power Levels by PathLoss
    Power_Levels[0]  = str(float(Power_Levels[0]) + PathLoss)
    Power_Levels[1]  = str(float(Power_Levels[1]) + PathLoss)
    Power_Levels[2]  = str(float(Power_Levels[2]) + PathLoss)


    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))

    #Set Up Transponder -- ModeS All Call
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     
    atc.gwrite(":ATC:XPDR:UF 58000000FFFFFF") #Mode S Message UF11,PR0 AdrsFFFFFF   
    time.sleep(10)

    
    #Loop thru the three power levels
    k=0    
    for P in Power_Levels:
    
        #Set Up Transponder -- ModeS
        cmd = ':ATC:XPDR:POW ' + str(P)
        atc.gwrite(cmd)
        print(cmd)
        rm.logMessage(0,"Test_2_3_2_5_Step8 ModeS" + cmd)   
        
        #Turn on RF
        atc.gwrite(":ATC:XPDR:RF ON")
        time.sleep(15)
        atc.waitforstatus()
        
        #Vary P16
        ModeS_Replies[k] = vary_P16(atc,rm,3.52)    
        ModeS_Replies[k+1] = vary_P16(atc,rm,3.48)    
             
        #Turn Off RF
        atc.gwrite(":ATC:XPDR:RF OFF")
        rm.logMessage(0,"Test_2_3_2_5_Step8 - Done")
        
        #increment index
        k=k+2
    

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")

    rm.logMessage(0,"Test_2_3_2_5_Step8 - Done: " + str(ModeS_Replies))    
    rm.logMessage(2,"Done, closing session")

    
    return ModeS_Replies
##########################################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

     
    res = Test_2_3_2_5_Step8(rm,atc_obj,12.0)
    
    atc_obj.close()

