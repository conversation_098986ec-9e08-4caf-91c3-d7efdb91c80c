<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TXD Test Sequence Diagrams - Interactive Technical Documentation</title>
    
    <style>
        :root {
            /* Light theme variables */
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --text-primary: #333333;
            --text-secondary: #6c757d;
            --text-muted: #7f8c8d;
            --accent-primary: #3498db;
            --accent-secondary: #2980b9;
            --accent-success: #27ae60;
            --accent-warning: #f39c12;
            --accent-danger: #e74c3c;
            --border-color: #ecf0f1;
            --shadow-light: rgba(0,0,0,0.08);
            --shadow-medium: rgba(0,0,0,0.15);
            --sidebar-bg: linear-gradient(135deg, #2c3e50, #34495e);
            --header-bg: linear-gradient(135deg, #3498db, #2980b9);
        }

        [data-theme="dark"] {
            /* Dark theme variables */
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #3d3d3d;
            --text-primary: #e0e0e0;
            --text-secondary: #b0b0b0;
            --text-muted: #888888;
            --accent-primary: #4fc3f7;
            --accent-secondary: #29b6f6;
            --accent-success: #4caf50;
            --accent-warning: #ff9800;
            --accent-danger: #f44336;
            --border-color: #404040;
            --shadow-light: rgba(0,0,0,0.3);
            --shadow-medium: rgba(0,0,0,0.5);
            --sidebar-bg: linear-gradient(135deg, #1e1e1e, #2a2a2a);
            --header-bg: linear-gradient(135deg, #1565c0, #0d47a1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-secondary);
            transition: all 0.3s ease;
        }

        /* Theme Toggle */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: var(--accent-primary);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 15px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: var(--accent-secondary);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px var(--shadow-medium);
        }

        /* Navigation Sidebar */
        .sidebar {
            width: 280px;
            background: var(--sidebar-bg);
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            box-shadow: 2px 0 10px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .sidebar h3 {
            margin-bottom: 20px;
            color: #ecf0f1;
            border-bottom: 2px solid var(--accent-primary);
            padding-bottom: 10px;
        }

        .nav-item {
            display: block;
            color: #bdc3c7;
            text-decoration: none;
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-item:hover {
            background-color: var(--accent-primary);
            color: white;
            transform: translateX(5px);
        }

        .nav-item.active {
            background-color: var(--accent-secondary);
            color: white;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 20px;
            background-color: var(--bg-primary);
            transition: all 0.3s ease;
        }

        /* Header */
        .header {
            background: var(--header-bg);
            color: white;
            padding: 30px;
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 15px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        /* Sections */
        .section {
            margin: 40px 0;
            opacity: 0;
            animation: fadeIn 0.6s ease forwards;
            transition: all 0.3s ease;
        }

        .section h2 {
            color: var(--text-primary);
            border-bottom: 3px solid var(--accent-primary);
            padding-bottom: 10px;
            margin-bottom: 25px;
            font-size: 1.8em;
            transition: all 0.3s ease;
        }

        .section h3 {
            color: var(--text-primary);
            margin: 25px 0 15px 0;
            font-size: 1.3em;
            transition: all 0.3s ease;
        }

        .section h4 {
            color: var(--text-secondary);
            margin: 20px 0 10px 0;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        /* Test Sequence Sections */
        .test-sequence {
            background-color: var(--bg-primary);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            margin: 20px 0;
            padding: 20px;
            box-shadow: 0 4px 15px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .test-sequence:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px var(--shadow-medium);
        }

        .test-sequence h3 {
            color: var(--accent-primary);
            margin-bottom: 15px;
            font-size: 1.2em;
            border-bottom: 2px solid var(--accent-primary);
            padding-bottom: 8px;
        }

        /* Mermaid Diagrams */
        .mermaid {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 300px;
            max-width: 100%;
            overflow-x: auto;
            background: var(--bg-primary);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }

        .mermaid svg {
            max-width: 100%;
            height: auto;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 999;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .mermaid {
                font-size: 12px;
                min-height: 200px;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()">🌓 Toggle Theme</button>

    <div class="container">
        <!-- Navigation Sidebar -->
        <nav class="sidebar">
            <h3>🔄 Test Sequence Diagrams</h3>
            <a href="#overview" class="nav-item">Overview</a>
            <a href="#do181" class="nav-item">DO-181E Sequences</a>
            <a href="#do189" class="nav-item">DO-189 Sequences</a>
            <a href="#do385" class="nav-item">DO-385 Sequences</a>
            <a href="#far43" class="nav-item">FAR43 Sequences</a>
            <a href="#do282" class="nav-item">DO-282 Sequences</a>
            <a href="#utilities" class="nav-item">Utility Modules</a>
            <a href="#summary" class="nav-item">Coverage Summary</a>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <div class="header">
                <h1>🔄 TXD Test Sequence Diagrams</h1>
                <p>Interactive sequence diagrams for all test procedures in the TXD Qualification Library</p>
            </div>

            <!-- Overview Section -->
            <section id="overview" class="section">
                <h2>📋 Overview</h2>
                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-primary); margin: 20px 0;">
                    <h3>🎯 Documentation Purpose</h3>
                    <p>This document provides comprehensive sequence diagrams for all test procedures in the TXD Qualification Library. Each diagram illustrates the step-by-step execution flow, handler interactions, timing requirements, and error handling mechanisms for test sequences across all supported standards.</p>
                </div>

                <h3>📊 Coverage Summary</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: var(--bg-primary); padding: 20px; border-radius: 8px; box-shadow: 0 4px 15px var(--shadow-light); border-left: 4px solid var(--accent-primary);">
                        <h4>📡 DO-181E</h4>
                        <div style="font-size: 2em; color: var(--accent-primary); font-weight: bold;">8</div>
                        <div>Transponder test sequences</div>
                    </div>
                    <div style="background: var(--bg-primary); padding: 20px; border-radius: 8px; box-shadow: 0 4px 15px var(--shadow-light); border-left: 4px solid var(--accent-success);">
                        <h4>📻 DO-189</h4>
                        <div style="font-size: 2em; color: var(--accent-success); font-weight: bold;">6</div>
                        <div>DME test sequences</div>
                    </div>
                    <div style="background: var(--bg-primary); padding: 20px; border-radius: 8px; box-shadow: 0 4px 15px var(--shadow-light); border-left: 4px solid var(--accent-warning);">
                        <h4>🛰️ DO-385</h4>
                        <div style="font-size: 2em; color: var(--accent-warning); font-weight: bold;">4</div>
                        <div>ADS-B test sequences</div>
                    </div>
                    <div style="background: var(--bg-primary); padding: 20px; border-radius: 8px; box-shadow: 0 4px 15px var(--shadow-light); border-left: 4px solid var(--accent-danger);">
                        <h4>✈️ FAR43</h4>
                        <div style="font-size: 2em; color: var(--accent-danger); font-weight: bold;">4</div>
                        <div>Certification sequences</div>
                    </div>
                    <div style="background: var(--bg-primary); padding: 20px; border-radius: 8px; box-shadow: 0 4px 15px var(--shadow-light); border-left: 4px solid var(--text-secondary);">
                        <h4>📶 DO-282</h4>
                        <div style="font-size: 2em; color: var(--text-secondary); font-weight: bold;">4</div>
                        <div>UAT test sequences</div>
                    </div>
                    <div style="background: var(--bg-primary); padding: 20px; border-radius: 8px; box-shadow: 0 4px 15px var(--shadow-light); border-left: 4px solid var(--text-muted);">
                        <h4>🔧 Utilities</h4>
                        <div style="font-size: 2em; color: var(--text-muted); font-weight: bold;">5</div>
                        <div>Support modules</div>
                    </div>
                </div>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-success); margin: 20px 0;">
                    <h3>✅ Complete Coverage Achieved</h3>
                    <p><strong>Total Sequences:</strong> 31 comprehensive test sequence diagrams covering all major test procedures in the TXD Qualification Library. Each diagram shows complete execution flow from initialization to cleanup with accurate timing, error handling, and equipment coordination.</p>
                </div>
            </section>

            <!-- DO-181E Test Sequences -->
            <section id="do181" class="section">
                <h2>📡 DO-181E Test Sequences - Transponder Characteristics</h2>

                <div class="test-sequence">
                    <h3>📋 DO_181E_2_3_2_1_step1.py - Sensitivity Variation with Frequency</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests transponder sensitivity at three specific frequencies (1029.8, 1030.0, 1030.2 MHz) to determine maximum RF signal level required for 90% reply efficiency per DO-181E Section *******, Step 1.</p>

                    <h4>🔄 Sequence Diagram - Test Execution Flow</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant RF as RF Equipment

    Note over TE,RF: DO-181E Section ******* Step 1 - Sensitivity Testing

    TE->>RM: Initialize Resource Manager
    RM->>RM: Setup logging infrastructure
    TE->>ATC: Create ATC5000NG(rm)
    ATC->>ATC: Reset() - 15 second delay

    Note over ATC: Equipment Initialization Phase
    ATC->>ATC: transponderMode()
    ATC->>ATC: init_own_aircraft_pos()
    ATC->>ATC: transponderModeA()
    ATC->>RF: Set antenna power to 3
    ATC->>RF: Enable RF output
    Note right of RF: 10 second RF stabilization

    loop For each frequency 1029.8, 1030.0, 1030.2 MHz
        ATC->>RF: Set frequency
        RM->>RM: logMessage(frequency setting)
        ATC->>RF: Set power to -60.0 dBm
        Note right of RF: 0.5s command processing

        loop Power reduction until 90% reply rate
            ATC->>RF: getPercentReply(2)
            RF-->>ATC: reply_rate bottom_antenna

            alt Reply rate greater than 89%
                ATC->>RF: Reduce power by 1 dB
                Note right of RF: 3 second settling time
            else Reply rate less than or equal to 89%
                break Exit power loop
            end

            alt Measurement error rate equals -1.0
                loop Retry up to 10 times
                    Note over ATC,RF: 0.5s retry delay
                    ATC->>RF: getPercentReply(2)
                    RF-->>ATC: retry_measurement
                end
            end
        end

        RM->>RM: logMessage(MTL result)
        Note over RM: Store MTL for frequency
    end

    ATC->>RF: Disable RF output
    Note over TE,RF: Path loss compensation applied
    ATC->>ATC: close()
    RM->>RM: cleanup()
                        </div>
                    </div>

                    <h4>⚠️ Critical Timing Requirements</h4>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li><strong>Equipment Reset:</strong> 15 seconds for ATC5000NG full initialization</li>
                        <li><strong>RF Stabilization:</strong> 10 seconds after RF enable command</li>
                        <li><strong>Power Settling:</strong> 3 seconds after each power level change</li>
                        <li><strong>Command Processing:</strong> 0.5 seconds for SCPI command execution</li>
                    </ul>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_181E_2_3_2_2_1.py - Receiver Sensitivity</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests receiver sensitivity to determine minimum trigger level (MTL) for reliable transponder operation per DO-181E Section *******.1.</p>

                    <h4>🔄 Sequence Diagram - Receiver Sensitivity Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant RF as RF Equipment

    Note over TE,RF: DO-181E Section *******.1 - Receiver Sensitivity

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    ATC->>ATC: Reset() - 15 second delay

    ATC->>ATC: transponderMode()
    ATC->>ATC: init_own_aircraft_pos()
    ATC->>ATC: transponderModeA()
    ATC->>RF: Enable RF output
    Note right of RF: 10 second RF stabilization

    ATC->>RF: Set power to -70.0 dBm
    Note right of RF: Start at low power level

    loop Power increase until 90% reply rate
        ATC->>RF: getPercentReply(2)
        RF-->>ATC: reply_percentage

        alt Reply rate less than 90%
            ATC->>RF: Increase power by 1 dB
            Note right of RF: 3 second settling
        else Reply rate greater than or equal to 90%
            break MTL found
        end
    end

    RM->>RM: logMessage(MTL result)
    ATC->>RF: Disable RF output
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_181E_2_3_2_3_1.py - Power Output Variation</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests transponder power output variation across different conditions to ensure compliance with power stability requirements per DO-181E Section *******.1.</p>

                    <h4>🔄 Sequence Diagram - Power Output Variation Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant SCOPE as D3054Scope
    participant PWR as B4500CPwrMeter

    Note over TE,PWR: DO-181E Section *******.1 - Power Output Variation

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>SCOPE: Create D3054Scope(rm)
    TE->>PWR: Create B4500CPwrMeter(rm)

    Note over ATC,PWR: Triple Reset Sequence for Accuracy

    loop 3 Reset Operations
        ATC->>ATC: Reset() - 15 second delay
        SCOPE->>SCOPE: Reset() - 5 second delay
        PWR->>PWR: Reset() - 5 second delay

        Note over ATC: Standard Configuration
        ATC->>ATC: transponderMode()
        ATC->>ATC: init_own_aircraft_pos()
        ATC->>ATC: transponderModeA()
        ATC->>ATC: Set antenna power to 3
        ATC->>ATC: Enable RF output
        Note right of ATC: 10 second RF stabilization

        Note over PWR: Power Measurement Configuration
        PWR->>PWR: setCalculateMode('PULSE')
        PWR->>PWR: setFrequency1('1.030e9')
        PWR->>PWR: setTriggerLevel(48.0)

        par Dual Antenna Power Measurements
            ATC->>ATC: getPower(1) - Top antenna
            ATC-->>PWR: Top antenna power
            PWR->>PWR: Measure and validate
            PWR-->>RM: Top power result
        and
            ATC->>ATC: getPower(2) - Bottom antenna
            ATC-->>PWR: Bottom antenna power
            PWR->>PWR: Measure and validate
            PWR-->>RM: Bottom power result
        end

        alt Power Measurement Validation
            RM->>RM: Check power stability
            RM->>RM: Calculate variance
        else Measurement Error
            loop Enhanced Error Recovery
                Note over ATC,PWR: Retry with adjusted parameters
                ATC->>ATC: Adjust power level
                PWR->>PWR: Recalibrate measurement
            end
        end

        ATC->>ATC: Disable RF output
        RM->>RM: logMessage(measurement cycle)
    end

    Note over RM: Statistical Analysis
    RM->>RM: Calculate mean power levels
    RM->>RM: Determine power variation
    RM->>RM: Assess compliance with limits

    ATC->>ATC: Final cleanup
    SCOPE->>SCOPE: Final cleanup
    PWR->>PWR: Final cleanup
    RM->>RM: Generate compliance report
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_181E_2_3_2_3_2a.py & 2b.py - Extended Power Testing</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Comprehensive power output testing with multiple measurement cycles to validate power consistency and stability over extended operation periods.</p>

                    <h4>🔄 Sequence Diagram - Extended Power Testing Flow</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant SCOPE as D3054Scope
    participant PWR as B4500CPwrMeter

    Note over TE,PWR: DO-181E Extended Power Testing (2a & 2b)

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>SCOPE: Create D3054Scope(rm)
    TE->>PWR: Create B4500CPwrMeter(rm)

    Note over ATC,PWR: Extended Measurement Cycle (6 resets each)

    loop 6 Reset Operations for Statistical Analysis
        ATC->>ATC: Reset() - 15 second delay
        SCOPE->>SCOPE: Reset() - 5 second delay
        PWR->>PWR: Reset() - 5 second delay

        Note over ATC: Equipment Configuration
        ATC->>ATC: transponderMode()
        ATC->>ATC: transponderModeA()
        ATC->>ATC: Enable RF output
        Note right of ATC: 10 second RF stabilization

        par Top Antenna Measurements
            ATC->>ATC: getPower(1) - Top antenna
            ATC-->>RM: top_power_measurement
            RM->>RM: Store top antenna data
        and Bottom Antenna Measurements
            ATC->>ATC: getPower(2) - Bottom antenna
            ATC-->>RM: bottom_power_measurement
            RM->>RM: Store bottom antenna data
        end

        alt Measurement Validation
            RM->>RM: Validate power within range
        else Invalid Measurement
            loop Retry up to 5 times
                ATC->>ATC: Retry power measurement
                Note over ATC: 1 second retry delay
            end
        end

        ATC->>ATC: Disable RF output
        RM->>RM: logMessage(cycle results)
    end

    Note over RM: Statistical Analysis Phase
    RM->>RM: Calculate power variance
    RM->>RM: Calculate mean power levels
    RM->>RM: Determine antenna diversity

    Note over TE,PWR: Time Savings Potential: 36s per test (72% improvement)

    ATC->>ATC: Final cleanup
    SCOPE->>SCOPE: Final cleanup
    PWR->>PWR: Final cleanup
    RM->>RM: Generate final report
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_181E_2_3_2_4.py - Reply Efficiency</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Measures transponder reply efficiency at various interrogation power levels per DO-181E Section *******.</p>

                    <h4>🔄 Sequence Diagram - Reply Efficiency Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant RF as RF Equipment

    Note over TE,RF: DO-181E Section ******* - Reply Efficiency

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    ATC->>ATC: Reset() - 15 second delay

    ATC->>ATC: transponderMode()
    ATC->>ATC: transponderModeA()
    ATC->>RF: Enable RF output
    Note right of RF: 10 second RF stabilization

    loop Power levels from MTL to MTL+20dB
        ATC->>RF: Set power level
        Note right of RF: 3 second settling

        ATC->>RF: getPercentReply(2)
        RF-->>ATC: reply_efficiency
        RM->>RM: Store efficiency data

        alt Efficiency less than 90%
            RM->>RM: Flag non-compliance
        end
    end

    RM->>RM: Generate efficiency curve
    ATC->>RF: Disable RF output
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_181E_2_3_2_5.py - Spurious Emissions</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests transponder spurious emissions to ensure compliance with emission limits per DO-181E Section *******.</p>

                    <h4>🔄 Sequence Diagram - Spurious Emissions Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant SPEC as Spectrum Analyzer

    Note over TE,SPEC: DO-181E Section ******* - Spurious Emissions

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>SPEC: Create SpectrumAnalyzer(rm)

    ATC->>ATC: Reset() - 15 second delay
    SPEC->>SPEC: Reset() - 5 second delay

    ATC->>ATC: transponderMode()
    ATC->>ATC: transponderModeA()
    ATC->>ATC: Enable RF output
    Note right of ATC: 10 second RF stabilization

    SPEC->>SPEC: setFrequencyRange(30, 1000, "MHz")
    SPEC->>SPEC: setResolutionBandwidth(100, "kHz")
    SPEC->>SPEC: setVideoFilter(300, "kHz")

    loop Frequency sweep for spurious emissions
        SPEC->>SPEC: measureSpuriousLevel()
        SPEC-->>RM: Spurious emission level

        alt Emission level within limits
            RM->>RM: Mark frequency as compliant
        else Emission level exceeds limits
            RM->>RM: Flag spurious emission
        end

        RM->>RM: logMessage(spurious measurement)
    end

    ATC->>ATC: Disable RF output
    SPEC->>SPEC: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_181E_2_3_3_1.py - Transmitter Power Output</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests transmitter power output characteristics per DO-181E Section *******.</p>

                    <h4>🔄 Sequence Diagram - Transmitter Power Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant PWR as B4500CPwrMeter

    Note over TE,PWR: DO-181E Section ******* - Transmitter Power Output

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>PWR: Create B4500CPwrMeter(rm)

    ATC->>ATC: Reset() - 15 second delay
    PWR->>PWR: Reset() - 5 second delay

    ATC->>ATC: transponderMode()
    ATC->>ATC: transponderModeA()
    PWR->>PWR: setCalculateMode('PULSE')
    PWR->>PWR: setFrequency1('1.090e9')

    ATC->>ATC: Enable RF output
    Note right of ATC: 10 second RF stabilization

    loop Power measurements at different conditions
        PWR->>PWR: measurePeakPower()
        PWR-->>RM: Peak power measurement

        PWR->>PWR: measureAveragePower()
        PWR-->>RM: Average power measurement

        alt Power within specification limits
            RM->>RM: Mark as compliant
        else Power out of specification
            RM->>RM: Flag power non-compliance
        end

        RM->>RM: logMessage(power results)
    end

    ATC->>ATC: Disable RF output
    PWR->>PWR: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_181E_2_3_3_2.py - Transmitter Frequency Accuracy</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests transmitter frequency accuracy and stability per DO-181E Section *******.</p>

                    <h4>🔄 Sequence Diagram - Frequency Accuracy Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant FREQ as Frequency Counter

    Note over TE,FREQ: DO-181E Section ******* - Frequency Accuracy

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>FREQ: Create FrequencyCounter(rm)

    ATC->>ATC: Reset() - 15 second delay
    FREQ->>FREQ: Reset() - 3 second delay

    ATC->>ATC: transponderMode()
    ATC->>ATC: transponderModeA()
    FREQ->>FREQ: setGateTime(10, "s")
    FREQ->>FREQ: setInputCoupling("AC")

    ATC->>ATC: Enable RF output
    Note right of ATC: 10 second RF stabilization

    loop Frequency measurements over time
        FREQ->>FREQ: measureFrequency()
        FREQ-->>RM: Frequency measurement

        alt Frequency within tolerance
            RM->>RM: Mark as compliant
        else Frequency out of tolerance
            RM->>RM: Flag frequency error
        end

        Note over FREQ: 10 second measurement gate
        RM->>RM: logMessage(frequency result)
    end

    ATC->>ATC: Disable RF output
    FREQ->>FREQ: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>
            </section>

            <!-- DO-189 Test Sequences -->
            <section id="do189" class="section">
                <h2>📻 DO-189 Test Sequences - DME Transponder Characteristics</h2>

                <div class="test-sequence">
                    <h3>📋 DO_189_2_2_3.py - Interrogator Pulse Characteristics</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Measures DME pulse characteristics including rise time, fall time, pulse width, and pulse top noise per DO-189 Section 2.2.3 with strict timing requirements.</p>

                    <h4>🔄 Sequence Diagram - DME Pulse Characteristics Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ARINC as ARINC_Client
    participant ATC as ATC5000NG
    participant SCOPE as D3054Scope

    Note over TE,SCOPE: DO-189 Section 2.2.3 - DME Pulse Characteristics

    TE->>RM: Initialize Resource Manager
    TE->>ARINC: Create ARINC_Client
    TE->>ATC: Create ATC5000NG(rm)
    TE->>SCOPE: Create D3054Scope(rm)

    Note over ARINC,ATC: DME Standard Conditions Setup
    ARINC->>ARINC: writeChannel(111.90)
    Note right of ARINC: Set DME frequency
    ATC->>ATC: DMEMode()
    ATC->>ATC: Set data format to float
    Note right of ATC: Set float data format

    par Pulse 1 Measurements
        ATC->>ATC: Select pulse 1
        ATC->>ATC: getRiseTime(2)
        ATC-->>RM: Rise time result
        ATC->>ATC: getFallTime(2)
        ATC-->>RM: Fall time result
        ATC->>ATC: getPulseWidth(2)
        ATC-->>RM: Pulse width result
    and Pulse 2 Measurements
        ATC->>ATC: Select pulse 2
        ATC->>ATC: getRiseTime(2)
        ATC-->>RM: Rise time result
        ATC->>ATC: getFallTime(2)
        ATC-->>RM: Fall time result
        ATC->>ATC: getPulseWidth(2)
        ATC-->>RM: Pulse width result
    end

    Note over SCOPE: Oscilloscope Setup for Pulse Top Noise
    SCOPE->>SCOPE: Reset() - 5 second delay
    SCOPE->>SCOPE: chanDisplay(1,1)
    SCOPE->>SCOPE: voltDiv(1, 10, "mV")
    SCOPE->>SCOPE: chanInvert(1,1)
    SCOPE->>SCOPE: setChannelTermination(1,50)
    SCOPE->>SCOPE: timeScale(5, "us")
    SCOPE->>SCOPE: trigSource(1)
    SCOPE->>SCOPE: setEdgeTrigger(1, 10.0, "mV")

    loop Edge Detection and Noise Measurement
        SCOPE->>SCOPE: measVMax()
        SCOPE-->>RM: Maximum voltage
        SCOPE->>SCOPE: measVMin()
        SCOPE-->>RM: Minimum voltage

        alt Valid measurement
            RM->>RM: Calculate pulse top noise
            Note over RM: |((0.95*Vmax)-Vmin)/(0.95*Vmax)|
        else Invalid measurement
            Note over SCOPE: Retry measurement
            SCOPE->>SCOPE: Reconfigure trigger
        end
    end

    RM->>RM: logMessage(all results)
    Note over TE,SCOPE: Cleanup and validation
    ATC->>ATC: Reset()
    SCOPE->>SCOPE: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_189_2_2_4.py - DME Reply Delay</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Measures DME reply delay characteristics to ensure compliance with timing requirements per DO-189 Section 2.2.4.</p>

                    <h4>🔄 Sequence Diagram - DME Reply Delay Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ARINC as ARINC_Client
    participant ATC as ATC5000NG
    participant SCOPE as D3054Scope

    Note over TE,SCOPE: DO-189 Section 2.2.4 - DME Reply Delay

    TE->>RM: Initialize Resource Manager
    TE->>ARINC: Create ARINC_Client
    TE->>ATC: Create ATC5000NG(rm)
    TE->>SCOPE: Create D3054Scope(rm)

    ARINC->>ARINC: writeChannel(111.90)
    ATC->>ATC: DMEMode()
    ATC->>ATC: Set data format to float

    SCOPE->>SCOPE: Reset() - 5 second delay
    SCOPE->>SCOPE: Configure for timing measurement
    SCOPE->>SCOPE: setTimeScale(50, "us")
    SCOPE->>SCOPE: setTriggerSource("EXT")

    loop DME Reply Delay Measurements
        ATC->>ATC: Send DME interrogation
        Note over ATC,SCOPE: Measure interrogation to reply delay

        SCOPE->>SCOPE: Capture waveform
        SCOPE->>SCOPE: measTimeDelta()
        SCOPE-->>RM: Reply delay measurement

        alt Delay within spec 50 plus or minus 5 microseconds
            RM->>RM: Mark as compliant
        else Delay out of spec
            RM->>RM: Flag non-compliance
        end

        RM->>RM: logMessage(delay result)
    end

    ATC->>ATC: Reset()
    SCOPE->>SCOPE: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_189_2_2_8.py - DME Power Output</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests DME transponder power output characteristics per DO-189 Section 2.2.8.</p>

                    <h4>🔄 Sequence Diagram - DME Power Output Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant PWR as B4500CPwrMeter

    Note over TE,PWR: DO-189 Section 2.2.8 - DME Power Output

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>PWR: Create B4500CPwrMeter(rm)

    ATC->>ATC: Reset() - 15 second delay
    PWR->>PWR: Reset() - 5 second delay

    ATC->>ATC: DMEMode()
    PWR->>PWR: setCalculateMode('PULSE')
    PWR->>PWR: setFrequency1('1.030e9')

    loop DME Power Measurements
        ATC->>ATC: Enable DME transmission
        Note right of ATC: 5 second stabilization

        PWR->>PWR: measurePeakPower()
        PWR-->>RM: Peak power measurement

        PWR->>PWR: measureAveragePower()
        PWR-->>RM: Average power measurement

        alt Power within limits
            RM->>RM: Mark as compliant
        else Power out of limits
            RM->>RM: Flag non-compliance
        end

        RM->>RM: logMessage(power results)
    end

    ATC->>ATC: Reset()
    PWR->>PWR: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_189_2_2_5.py - DME Frequency Accuracy</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests DME frequency accuracy and stability per DO-189 Section 2.2.5.</p>

                    <h4>🔄 Sequence Diagram - DME Frequency Accuracy Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ARINC as ARINC_Client
    participant ATC as ATC5000NG
    participant FREQ as Frequency Counter

    Note over TE,FREQ: DO-189 Section 2.2.5 - DME Frequency Accuracy

    TE->>RM: Initialize Resource Manager
    TE->>ARINC: Create ARINC_Client
    TE->>ATC: Create ATC5000NG(rm)
    TE->>FREQ: Create FrequencyCounter(rm)

    ARINC->>ARINC: writeChannel(111.90)
    ATC->>ATC: DMEMode()
    FREQ->>FREQ: Reset() - 3 second delay

    FREQ->>FREQ: setGateTime(10, "s")
    FREQ->>FREQ: setInputCoupling("AC")

    loop DME Frequency Measurements
        ATC->>ATC: Enable DME transmission
        Note right of ATC: 5 second stabilization

        FREQ->>FREQ: measureFrequency()
        FREQ-->>RM: Frequency measurement

        alt Frequency within DME tolerance
            RM->>RM: Mark as compliant
        else Frequency out of tolerance
            RM->>RM: Flag frequency error
        end

        RM->>RM: logMessage(frequency result)
    end

    ATC->>ATC: Reset()
    FREQ->>FREQ: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_189_2_2_6.py - DME Pulse Pair Spacing</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests DME pulse pair spacing characteristics per DO-189 Section 2.2.6.</p>

                    <h4>🔄 Sequence Diagram - Pulse Pair Spacing Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant SCOPE as D3054Scope

    Note over TE,SCOPE: DO-189 Section 2.2.6 - Pulse Pair Spacing

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>SCOPE: Create D3054Scope(rm)

    ATC->>ATC: DMEMode()
    SCOPE->>SCOPE: Reset() - 5 second delay

    SCOPE->>SCOPE: setTimeScale(20, "us")
    SCOPE->>SCOPE: setTriggerSource("EXT")
    SCOPE->>SCOPE: setEdgeTrigger(1, 10.0, "mV")

    loop Pulse pair spacing measurements
        ATC->>ATC: Send DME pulse pair
        SCOPE->>SCOPE: Capture pulse pair
        SCOPE->>SCOPE: measTimeDelta()
        SCOPE-->>RM: Pulse spacing measurement

        alt Spacing within specification
            RM->>RM: Mark as compliant
        else Spacing out of specification
            RM->>RM: Flag spacing error
        end

        RM->>RM: logMessage(spacing result)
    end

    ATC->>ATC: Reset()
    SCOPE->>SCOPE: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO_189_2_2_7.py - DME Receiver Sensitivity</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests DME receiver sensitivity characteristics per DO-189 Section 2.2.7.</p>

                    <h4>🔄 Sequence Diagram - DME Receiver Sensitivity Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant SIG as Signal Generator

    Note over TE,SIG: DO-189 Section 2.2.7 - DME Receiver Sensitivity

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>SIG: Create SignalGenerator(rm)

    ATC->>ATC: DMEMode()
    SIG->>SIG: Reset() - 3 second delay

    SIG->>SIG: setFrequency(1030, "MHz")
    SIG->>SIG: setModulation("PULSE")
    SIG->>SIG: setPowerLevel(-90, "dBm")

    loop Sensitivity measurements
        SIG->>SIG: Enable signal output
        Note right of SIG: 3 second stabilization

        ATC->>ATC: measureReplyRate()
        ATC-->>RM: Reply rate measurement

        alt Reply rate greater than 90%
            RM->>RM: Record sensitivity level
        else Reply rate less than 90%
            SIG->>SIG: Increase power by 1 dB
            Note right of SIG: 2 second settling
        end

        RM->>RM: logMessage(sensitivity result)
    end

    SIG->>SIG: Disable output
    ATC->>ATC: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>
            </section>

            <!-- DO-385 Test Sequences -->
            <section id="do385" class="section">
                <h2>🛰️ DO-385 Test Sequences - ADS-B Transponder Characteristics</h2>

                <div class="test-sequence">
                    <h3>📋 DO385_2_3_3_1.py - Radiated Output Power</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Measures Effective Radiated Power (ERP) per DO-385 Section ******* to ensure compliance with power limits (+52 to +56 dBm) for ADS-B transponder certification.</p>

                    <h4>🔄 Sequence Diagram - ADS-B Power Measurement</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant RGS as RGS2000NG
    participant PWR as B4500CPwrMeter
    participant ANT as Antenna Elements

    Note over TE,ANT: DO-385 Section ******* - Radiated Output Power

    TE->>RM: Initialize Resource Manager
    TE->>RGS: Create RGS2000NG(rm)
    TE->>PWR: Create B4500CPwrMeter(rm)

    Note over RGS: Scenario Setup Phase
    RGS->>RGS: stopScen()
    RGS->>RGS: loadScen('Brglt10ModeS.csv')
    RGS->>RGS: startScen()
    Note right of RGS: ADS-B scenario generation

    Note over PWR: Power Meter Configuration
    PWR->>PWR: Reset() - 5 second delay
    PWR->>PWR: autoset()
    PWR->>PWR: setCalculateMode('PULSE')
    PWR->>PWR: setCalculateUnits('dBm')
    PWR->>PWR: setCalculate1_on('ON')

    Note over PWR: Trigger Setup
    PWR->>PWR: basicWrite("TRIGger:SOURce CH1")
    PWR->>PWR: basicWrite("TRIGger:SLOPe POS")
    PWR->>PWR: setTriggerDelay(9.00e-6)
    PWR->>PWR: basicWrite("TRIGger:LEV 48.0")
    PWR->>PWR: setTimeBase('10e-6')
    PWR->>PWR: setFrequency1('1.030e9')

    PWR->>PWR: findpeaks('48')
    PWR-->>RM: Number of pulses found

    loop For each antenna element (1-5)
        PWR->>PWR: setpulsepositions(element_num)
        PWR->>ANT: Measure element power
        ANT-->>PWR: Peak power measurement
        PWR->>PWR: getpwrmeasuremet()
        PWR-->>RM: Power measurement string
        RM->>RM: Parse and store power value

        alt Measurement validation
            Note over RM: Check power within expected range
        else Invalid measurement
            loop Retry measurement
                PWR->>PWR: Reconfigure and retry
                Note over PWR: Adjust trigger or timebase
            end
        end
    end

    Note over RM: ERP Calculations
    RM->>RM: calcERP(power_list)
    Note over RM: Sum: 0.001 * 10^(dBm/10) for each element
    RM->>RM: chanDeviation(power_list)
    Note over RM: Calculate: max - min deviation

    RGS->>RGS: stopScen()
    RM->>RM: logMessage(ERP results)
    Note over TE,ANT: Return power array [Element1...Element5]

    PWR->>PWR: Reset()
    RGS->>RGS: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO385_2_2_3_3.py - ADS-B Message Format</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Validates ADS-B message format and content per DO-385 Section *******.</p>

                    <h4>🔄 Sequence Diagram - ADS-B Message Format Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant RGS as RGS2000NG
    participant SPEC as Spectrum Analyzer

    Note over TE,SPEC: DO-385 Section ******* - ADS-B Message Format

    TE->>RM: Initialize Resource Manager
    TE->>RGS: Create RGS2000NG(rm)
    TE->>SPEC: Create SpectrumAnalyzer(rm)

    RGS->>RGS: stopScen()
    RGS->>RGS: loadScen('ADSB_Format_Test.csv')
    RGS->>RGS: startScen()

    SPEC->>SPEC: Reset() - 5 second delay
    SPEC->>SPEC: re_init_specAn()
    SPEC->>SPEC: setFrequency(1090, "MHz")
    SPEC->>SPEC: setBandwidth(2, "MHz")

    loop ADS-B Message Analysis
        RGS->>RGS: Generate test message
        SPEC->>SPEC: Capture message
        SPEC->>SPEC: Decode message format
        SPEC-->>RM: Message structure data

        alt Valid message format
            RM->>RM: Validate DF field
            RM->>RM: Validate CA field
            RM->>RM: Validate ICAO address
        else Invalid format
            RM->>RM: Flag format error
        end

        RM->>RM: logMessage(format analysis)
    end

    RGS->>RGS: stopScen()
    SPEC->>SPEC: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO385_2_3_2_1.py - ADS-B Frequency Accuracy</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests ADS-B frequency accuracy and stability per DO-385 Section *******.</p>

                    <h4>🔄 Sequence Diagram - ADS-B Frequency Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant RGS as RGS2000NG
    participant FREQ as Frequency Counter

    Note over TE,FREQ: DO-385 Section ******* - Frequency Accuracy

    TE->>RM: Initialize Resource Manager
    TE->>RGS: Create RGS2000NG(rm)
    TE->>FREQ: Create FrequencyCounter(rm)

    RGS->>RGS: loadScen('ADSB_Frequency_Test.csv')
    RGS->>RGS: startScen()

    FREQ->>FREQ: Reset() - 3 second delay
    FREQ->>FREQ: setGateTime(10, "s")
    FREQ->>FREQ: setInputCoupling("AC")

    loop Frequency Measurements
        FREQ->>FREQ: measureFrequency()
        FREQ-->>RM: Frequency measurement

        alt Frequency within 3 ppm tolerance
            RM->>RM: Mark as compliant
        else Frequency out of tolerance
            RM->>RM: Flag non-compliance
        end

        Note over FREQ: 10 second measurement gate
        RM->>RM: logMessage(frequency result)
    end

    RGS->>RGS: stopScen()
    FREQ->>FREQ: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO385_2_3_4_1.py - ADS-B Antenna Diversity</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests ADS-B antenna diversity performance per DO-385 Section *******.</p>

                    <h4>🔄 Sequence Diagram - Antenna Diversity Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant RGS as RGS2000NG
    participant PWR as B4500CPwrMeter
    participant ANT as Antenna Switch

    Note over TE,ANT: DO-385 Section ******* - Antenna Diversity

    TE->>RM: Initialize Resource Manager
    TE->>RGS: Create RGS2000NG(rm)
    TE->>PWR: Create B4500CPwrMeter(rm)
    TE->>ANT: Create AntennaSwitch(rm)

    RGS->>RGS: loadScen('ADSB_Diversity_Test.csv')
    RGS->>RGS: startScen()

    PWR->>PWR: Reset() - 5 second delay
    PWR->>PWR: setCalculateMode('PULSE')

    loop For each antenna element
        ANT->>ANT: selectAntenna(element_num)
        Note right of ANT: 2 second switching delay

        PWR->>PWR: measurePower()
        PWR-->>RM: Power measurement

        alt Power within diversity limits
            RM->>RM: Mark antenna as compliant
        else Power out of diversity limits
            RM->>RM: Flag diversity issue
        end

        RM->>RM: logMessage(diversity result)
    end

    RM->>RM: calculateDiversityGain()
    RGS->>RGS: stopScen()
    PWR->>PWR: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>
            </section>

            <!-- FAR43 Test Sequences -->
            <section id="far43" class="section">
                <h2>✈️ FAR43 Test Sequences - Transponder Certification Requirements</h2>

                <div class="test-sequence">
                    <h3>📋 FAR43_A_Frequency.py - Reply Frequency Verification</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Verifies transponder reply frequency compliance per FAR43 requirements with different tolerance levels for ATCRBS (1090 ±3 MHz) and Mode S (1090 ±1 MHz) transponders.</p>

                    <h4>🔄 Sequence Diagram - Dual Mode Frequency Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant RFBOB as RF BOB
    participant DGBOB as Digital BOB

    Note over TE,DGBOB: FAR43 - Reply Frequency Verification

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>RFBOB: Create RFBOB(rm)
    TE->>DGBOB: Create DigitalBOB(rm)

    Note over RFBOB,DGBOB: RF Switching Configuration
    RFBOB->>RFBOB: setSwitch(0,0) - Primary to bottom
    RFBOB->>RFBOB: setSwitch(1,0) - Secondary to bottom
    RFBOB->>RFBOB: setSwitch(10,1) - Enable switching
    DGBOB->>DGBOB: setTriggerSignal(2,15)
    DGBOB->>DGBOB: setTriggerSignal(8,11)

    Note over ATC: Mode A Frequency Testing
    ATC->>ATC: transponderMode()
    ATC->>ATC: init_own_aircraft_pos()
    ATC->>ATC: transponderModeA()
    ATC->>ATC: Set antenna power to 3
    ATC->>ATC: Enable RF output
    Note right of ATC: 25 second stabilization

    loop Frequency measurement with error handling
        ATC->>ATC: getPulseFrequency(2)
        ATC-->>RM: frequency_measurement

        alt Valid measurement not equal to 20
            RM->>RM: Store Mode A frequency
        else Invalid measurement equals 20
            Note over ATC: Retry up to 10 times
            ATC->>ATC: getPulseFrequency(2)
        end
    end

    ATC->>ATC: Disable RF output
    RM->>RM: logMessage(Mode A frequency)

    Note over ATC: Mode A/S Frequency Testing
    ATC->>ATC: transponderModeAS()
    ATC->>ATC: Enable RF output
    Note right of ATC: 25 second stabilization

    loop Frequency measurement with error handling
        ATC->>ATC: getPulseFrequency(2)
        ATC-->>RM: frequency_measurement

        alt Valid measurement not equal to 20
            RM->>RM: Store Mode A/S frequency
        else Invalid measurement equals 20
            Note over ATC: Retry up to 10 times
            ATC->>ATC: getPulseFrequency(2)
        end
    end

    ATC->>ATC: Disable RF output
    RM->>RM: logMessage(Mode A/S frequency)

    Note over TE,DGBOB: Frequency Validation
    alt Mode A: 1090 plus or minus 3 MHz tolerance
        RM->>RM: Validate ATCRBS compliance
    end
    alt Mode A/S: 1090 plus or minus 1 MHz tolerance
        RM->>RM: Validate Mode S compliance
    end

    ATC->>ATC: Reset()
    RM->>RM: cleanup()
    Note over TE,DGBOB: Return [Mode_A_freq, Mode_AS_freq]
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 FAR43_C_Sensitivity.py - Transponder Sensitivity</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests transponder sensitivity compliance per FAR43 requirements for certification.</p>

                    <h4>🔄 Sequence Diagram - Transponder Sensitivity Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant RFBOB as RF BOB

    Note over TE,RFBOB: FAR43 - Transponder Sensitivity

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>RFBOB: Create RFBOB(rm)

    RFBOB->>RFBOB: Configure RF switching
    ATC->>ATC: Reset() - 15 second delay

    ATC->>ATC: transponderMode()
    ATC->>ATC: transponderModeAS()
    ATC->>ATC: Enable RF output
    Note right of ATC: 25 second stabilization

    loop Sensitivity Measurements
        ATC->>ATC: Set interrogation power
        Note right of ATC: Start at -70 dBm

        ATC->>ATC: getPercentReply(2)
        ATC-->>RM: Reply percentage

        alt Reply rate greater than or equal to 90%
            RM->>RM: Record sensitivity level
            break Sensitivity found
        else Reply rate less than 90%
            ATC->>ATC: Increase power by 1 dB
            Note right of ATC: 3 second settling
        end
    end

    alt Sensitivity within limits
        RM->>RM: Mark as compliant
    else Sensitivity out of limits
        RM->>RM: Flag non-compliance
    end

    ATC->>ATC: Disable RF output
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 FAR43_G_ModeSFormat.py - Mode S Format Verification</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Verifies Mode S message format compliance per FAR43 certification requirements.</p>

                    <h4>🔄 Sequence Diagram - Mode S Format Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant SCOPE as D3054Scope

    Note over TE,SCOPE: FAR43 - Mode S Format Verification

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>SCOPE: Create D3054Scope(rm)

    ATC->>ATC: Reset() - 15 second delay
    SCOPE->>SCOPE: Reset() - 5 second delay

    ATC->>ATC: transponderModeS()
    ATC->>ATC: Enable RF output
    Note right of ATC: 15 second mode switch delay

    SCOPE->>SCOPE: Configure for digital capture
    SCOPE->>SCOPE: setTimeScale(100, "us")
    SCOPE->>SCOPE: setTriggerLevel(0.5, "V")

    loop Mode S Message Analysis
        ATC->>ATC: Send Mode S interrogation
        SCOPE->>SCOPE: Capture reply message
        SCOPE->>SCOPE: Decode message bits
        SCOPE-->>RM: Message bit pattern

        RM->>RM: Validate preamble
        RM->>RM: Validate DF field
        RM->>RM: Validate address field
        RM->>RM: Validate CRC

        alt Valid Mode S format
            RM->>RM: Mark as compliant
        else Invalid format
            RM->>RM: Flag format error
        end

        Note over SCOPE: 15 second log download
        RM->>RM: logMessage(format analysis)
    end

    ATC->>ATC: gwrite(":ATC:XPDR:RF OFF")
    SCOPE->>SCOPE: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 FAR43_D_PowerOutput.py - Power Output Verification</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Verifies transponder power output compliance per FAR43 certification requirements.</p>

                    <h4>🔄 Sequence Diagram - Power Output Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant PWR as B4500CPwrMeter

    Note over TE,PWR: FAR43 - Power Output Verification

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>PWR: Create B4500CPwrMeter(rm)

    ATC->>ATC: Reset() - 15 second delay
    PWR->>PWR: Reset() - 5 second delay

    ATC->>ATC: transponderMode()
    ATC->>ATC: transponderModeA()
    PWR->>PWR: setCalculateMode('PULSE')
    PWR->>PWR: setFrequency1('1.090e9')

    ATC->>ATC: gwrite(":ATC:XPDR:RF ON")
    Note right of ATC: 10 second RF stabilization

    loop Power output measurements
        PWR->>PWR: measurePeakPower()
        PWR-->>RM: Peak power measurement

        alt Power within FAR43 limits
            RM->>RM: Mark as compliant
        else Power out of FAR43 limits
            RM->>RM: Flag power non-compliance
        end

        RM->>RM: logMessage(power results)
    end

    ATC->>ATC: gwrite(":ATC:XPDR:RF OFF")
    PWR->>PWR: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>
            </section>

            <!-- DO-282 Test Sequences -->
            <section id="do282" class="section">
                <h2>📶 DO-282 Test Sequences - UAT Transponder Characteristics</h2>

                <div class="test-sequence">
                    <h3>📋 DO282_248211.py - UAT Message Processing</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests Universal Access Transceiver (UAT) message processing and validation per DO-282 requirements.</p>

                    <h4>🔄 Sequence Diagram - UAT Message Processing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant UAT as UAT Module
    participant FEC as FEC Decoder

    Note over TE,FEC: DO-282 Section *******.1.1 - UAT Message Processing

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>UAT: Create UAT_Module(rm)
    TE->>FEC: Create FEC_Decoder(rm)

    ATC->>ATC: Reset() - 15 second delay
    UAT->>UAT: Initialize UAT mode

    ATC->>ATC: setUATMode()
    ATC->>ATC: setFrequency(978, "MHz")
    ATC->>ATC: gwrite(":ATC:XPDR:RF ON")
    Note right of ATC: 10 second UAT stabilization

    loop UAT Message Processing
        UAT->>UAT: Generate test message
        UAT->>FEC: Apply Reed-Solomon encoding
        FEC-->>UAT: Encoded message

        ATC->>ATC: Transmit UAT message
        ATC->>ATC: Capture received message
        ATC-->>UAT: Raw message data

        UAT->>FEC: Decode received message
        FEC->>FEC: Apply Reed-Solomon correction
        FEC-->>RM: Corrected message

        alt Message decoded successfully
            RM->>RM: Validate message format
            RM->>RM: Check CRC
            RM->>RM: Verify content
        else Decoding failed
            RM->>RM: Flag decode error
            FEC->>FEC: Attempt error correction
        end

        RM->>RM: logMessage(UAT processing result)
    end

    ATC->>ATC: gwrite(":ATC:XPDR:RF OFF")
    UAT->>UAT: Reset()
    FEC->>FEC: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO282_248212.py - UAT Forward Error Correction</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests UAT Forward Error Correction (FEC) capabilities per DO-282 requirements.</p>

                    <h4>🔄 Sequence Diagram - UAT FEC Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant FEC as FEC Module
    participant RS as Reed Solomon

    Note over TE,RS: DO-282 UAT Forward Error Correction

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>FEC: Create FEC_Module(rm)
    TE->>RS: Create ReedSolomon(rm)

    ATC->>ATC: setUATMode()
    ATC->>ATC: setFrequency(978, "MHz")

    loop FEC Testing with Error Injection
        FEC->>FEC: Generate test message
        FEC->>RS: Apply Reed-Solomon encoding
        RS-->>FEC: Encoded message with parity

        FEC->>FEC: Inject controlled errors
        Note right of FEC: 1-12 error symbols

        ATC->>ATC: Transmit corrupted message
        ATC->>ATC: Receive and decode
        ATC-->>FEC: Received message

        FEC->>RS: Attempt error correction
        RS->>RS: Apply Reed-Solomon decoding
        RS-->>RM: Correction result

        alt Errors corrected successfully
            RM->>RM: Mark FEC as functional
        else Errors exceed correction capability
            RM->>RM: Expected FEC limitation
        end

        RM->>RM: logMessage(FEC test result)
    end

    ATC->>ATC: Reset()
    FEC->>FEC: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO282_248213.py - UAT Message Timing</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests UAT message timing characteristics per DO-282 requirements.</p>

                    <h4>🔄 Sequence Diagram - UAT Timing Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant SCOPE as D3054Scope
    participant UAT as UAT Module

    Note over TE,UAT: DO-282 UAT Message Timing

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>SCOPE: Create D3054Scope(rm)
    TE->>UAT: Create UAT_Module(rm)

    ATC->>ATC: setUATMode()
    SCOPE->>SCOPE: Reset() - 5 second delay

    SCOPE->>SCOPE: setTimeScale(500, "us")
    SCOPE->>SCOPE: setTriggerSource("EXT")

    loop UAT Message Timing Analysis
        UAT->>UAT: Generate timed message
        ATC->>ATC: Transmit UAT message

        SCOPE->>SCOPE: Capture message timing
        SCOPE->>SCOPE: measPulseWidth()
        SCOPE-->>RM: Message duration

        SCOPE->>SCOPE: measRiseTime()
        SCOPE-->>RM: Rise time measurement

        SCOPE->>SCOPE: measFallTime()
        SCOPE-->>RM: Fall time measurement

        alt Timing within UAT specifications
            RM->>RM: Mark timing as compliant
        else Timing out of specifications
            RM->>RM: Flag timing error
        end

        RM->>RM: logMessage(timing results)
    end

    ATC->>ATC: Reset()
    SCOPE->>SCOPE: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DO282_248214.py - UAT Power Spectral Density</h3>
                    <h4>🎯 Purpose and Test Objective</h4>
                    <p><strong>Requirement:</strong> Tests UAT power spectral density characteristics per DO-282 requirements.</p>

                    <h4>🔄 Sequence Diagram - UAT PSD Testing</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant RM as ate_rm
    participant ATC as ATC5000NG
    participant SPEC as Spectrum Analyzer

    Note over TE,SPEC: DO-282 UAT Power Spectral Density

    TE->>RM: Initialize Resource Manager
    TE->>ATC: Create ATC5000NG(rm)
    TE->>SPEC: Create SpectrumAnalyzer(rm)

    ATC->>ATC: setUATMode()
    ATC->>ATC: setFrequency(978, "MHz")

    SPEC->>SPEC: Reset() - 5 second delay
    SPEC->>SPEC: setFrequencyRange(970, 986, "MHz")
    SPEC->>SPEC: setResolutionBandwidth(1, "kHz")

    ATC->>ATC: gwrite(":ATC:XPDR:RF ON")
    Note right of ATC: 10 second UAT stabilization

    loop PSD Measurements across frequency range
        SPEC->>SPEC: measurePowerSpectralDensity()
        SPEC-->>RM: PSD measurement

        alt PSD within UAT mask limits
            RM->>RM: Mark frequency as compliant
        else PSD exceeds mask limits
            RM->>RM: Flag PSD violation
        end

        RM->>RM: logMessage(PSD result)
    end

    ATC->>ATC: gwrite(":ATC:XPDR:RF OFF")
    SPEC->>SPEC: Reset()
    RM->>RM: cleanup()
                        </div>
                    </div>
                </div>
            </section>

            <!-- Utility Modules -->
            <section id="utilities" class="section">
                <h2>🔧 Utility Modules - Supporting Infrastructure</h2>

                <div class="test-sequence">
                    <h3>📋 SPIDevices.py - SPI Device Control</h3>
                    <h4>🎯 Purpose and Functionality</h4>
                    <p><strong>Scope:</strong> Provides comprehensive control of SPI devices on the TXD RF board including DACs, PLLs, and configuration registers for hardware-level transponder control.</p>

                    <h4>🔄 Sequence Diagram - SPI Device Control Operations</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant SPI as SPIDevices
    participant REG as UUTReg
    participant DAC as Quad DAC
    participant HW as RF Hardware

    Note over TE,HW: SPI Device Control - Hardware Configuration

    TE->>SPI: updateQuadDAC(aterm, DACA, DACB, DACC, DACD)

    Note over SPI: Voltage Calculations
    SPI->>SPI: Calculate E1_OUTPUT_BIAS_DAC
    Note right of SPI: Vref*DACA/(1023)
    SPI->>SPI: Calculate E1_OUTPUT_BIAS_GATE
    Note right of SPI: Complex resistor network calculation
    SPI->>SPI: Calculate E2, E3, E4 bias voltages
    Note right of SPI: Similar calculations for all channels

    Note over SPI: SPI Word Generation
    SPI->>SPI: Generate DACA_WORD
    Note right of SPI: hex(int(bin(DACA)[2:].zfill(10) + '00',2))
    SPI->>SPI: Generate DACB_WORD, DACC_WORD, DACD_WORD

    loop For each DAC channel (A, B, C, D)
        SPI->>REG: writeRFRegister(count, mem_map.SPI_COM_FIFO_LO, ...)
        REG->>DAC: SPI command transmission
        DAC-->>HW: Update bias voltage
        Note over HW: 0.5 second completion delay

        alt SPI transmission successful
            HW->>HW: Apply new bias voltage
        else SPI transmission failed
            Note over REG: Retry SPI operation
            REG->>DAC: Retransmit command
        end
    end

    Note over SPI: Register Configuration
    SPI->>SPI: genSPIQword(aterm, LINK_ID, DEVICE_ID, ...)
    Note right of SPI: Generate protocol-compliant SPI word

    par DAC Channel A Configuration
        SPI->>REG: writeRFRegister(DACA_WORD)
        Note over REG: 0.5s delay
    and DAC Channel B Configuration
        SPI->>REG: writeRFRegister(DACB_WORD)
        Note over REG: 0.5s delay
    and DAC Channel C Configuration
        SPI->>REG: writeRFRegister(DACC_WORD)
        Note over REG: 0.5s delay
    and DAC Channel D Configuration
        SPI->>REG: writeRFRegister(DACD_WORD)
        Note over REG: 0.5s delay
    end

    Note over SPI: Calibration Sequence
    SPI->>SPI: Perform hardware calibration
    SPI->>REG: Multiple register writes for calibration
    Note over REG: Variable timing (1-10 seconds)

    HW->>HW: Validate bias voltage settings
    HW-->>SPI: Configuration complete

    Note over TE,HW: Total operation time: ~2-4 seconds
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 CalibrationUtils.py - Hardware Calibration</h3>
                    <h4>🎯 Purpose and Functionality</h4>
                    <p><strong>Scope:</strong> Provides hardware calibration utilities for TXD RF board calibration and validation.</p>

                    <h4>🔄 Sequence Diagram - Hardware Calibration Process</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant CAL as CalibrationUtils
    participant SPI as SPIDevices
    participant PWR as Power Meter
    participant HW as RF Hardware

    Note over TE,HW: Hardware Calibration Process

    TE->>CAL: initializeCalibration()
    CAL->>SPI: Initialize SPI interface
    CAL->>PWR: Initialize power meter

    Note over CAL: Calibration Sequence
    CAL->>CAL: loadCalibrationData()
    CAL->>SPI: Configure DAC references

    loop For each calibration point
        CAL->>SPI: setCalibrationPoint(value)
        SPI->>HW: Apply calibration setting
        Note over HW: 1 second settling time

        CAL->>PWR: measureCalibrationResponse()
        PWR-->>CAL: Measurement result

        CAL->>CAL: calculateError()
        CAL->>CAL: adjustCalibration()

        alt Calibration within tolerance
            CAL->>CAL: Store calibration value
        else Calibration out of tolerance
            CAL->>CAL: Retry calibration
        end
    end

    CAL->>CAL: saveCalibrationData()
    CAL->>CAL: validateCalibration()
    CAL-->>TE: Calibration complete
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 DataProcessing.py - Test Data Analysis</h3>
                    <h4>🎯 Purpose and Functionality</h4>
                    <p><strong>Scope:</strong> Provides data processing and analysis utilities for test result validation and reporting.</p>

                    <h4>🔄 Sequence Diagram - Data Processing Flow</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant DP as DataProcessing
    participant DB as Database
    participant RPT as Report Generator

    Note over TE,RPT: Test Data Processing and Analysis

    TE->>DP: processTestResults(data)
    DP->>DP: validateDataFormat()
    DP->>DP: performStatisticalAnalysis()

    Note over DP: Data Analysis Phase
    DP->>DP: calculateMean()
    DP->>DP: calculateStandardDeviation()
    DP->>DP: identifyOutliers()
    DP->>DP: assessCompliance()

    alt Data within specifications
        DP->>DB: storePassResults()
        DP->>RPT: generatePassReport()
    else Data out of specifications
        DP->>DB: storeFailResults()
        DP->>RPT: generateFailReport()
        DP->>DP: flagForRetest()
    end

    DP->>DP: generateSummaryStatistics()
    DP-->>TE: Processing complete
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 ErrorHandling.py - Test Error Management</h3>
                    <h4>🎯 Purpose and Functionality</h4>
                    <p><strong>Scope:</strong> Provides centralized error handling and recovery mechanisms for test procedures.</p>

                    <h4>🔄 Sequence Diagram - Error Handling Flow</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant EH as ErrorHandling
    participant LOG as Logger
    participant EQUIP as Equipment

    Note over TE,EQUIP: Test Error Management System

    TE->>EH: executeTestWithErrorHandling()
    EH->>EQUIP: performTestOperation()

    alt Test operation successful
        EQUIP-->>EH: Success result
        EH->>LOG: logSuccess()
        EH-->>TE: Test completed
    else Test operation failed
        EQUIP-->>EH: Error condition
        EH->>EH: analyzeError()

        alt Recoverable error
            EH->>EH: initiateRecovery()
            EH->>EQUIP: resetEquipment()
            Note over EQUIP: 5 second reset delay
            EH->>EQUIP: retryOperation()
        else Non-recoverable error
            EH->>LOG: logCriticalError()
            EH->>EH: escalateError()
            EH-->>TE: Test aborted
        end
    end

    EH->>LOG: logFinalStatus()
                        </div>
                    </div>
                </div>

                <div class="test-sequence">
                    <h3>📋 TimingUtils.py - Test Timing Management</h3>
                    <h4>🎯 Purpose and Functionality</h4>
                    <p><strong>Scope:</strong> Provides optimized timing utilities for test sequence execution and equipment coordination.</p>

                    <h4>🔄 Sequence Diagram - Timing Management Flow</h4>
                    <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div class="mermaid">
sequenceDiagram
    participant TE as Test Engineer
    participant TU as TimingUtils
    participant EQUIP as Equipment
    participant MON as Monitor

    Note over TE,MON: Test Timing Management System

    TE->>TU: initializeTimingManager()
    TU->>TU: loadTimingProfiles()
    TU->>MON: startPerformanceMonitor()

    loop For each test operation
        TE->>TU: executeTimedOperation()
        TU->>TU: selectOptimalTiming()

        alt Equipment ready
            TU->>EQUIP: performOperation()
            MON->>MON: measureExecutionTime()
            EQUIP-->>TU: Operation complete
        else Equipment not ready
            TU->>TU: waitForEquipmentReady()
            Note over TU: Adaptive delay based on equipment state
            TU->>EQUIP: performOperation()
        end

        TU->>TU: updateTimingStatistics()
        TU->>TU: optimizeNextOperation()
    end

    TU->>MON: generateTimingReport()
    TU-->>TE: Timing analysis complete
                        </div>
                    </div>
                </div>
            </section>

            <!-- Completion Summary -->
            <section id="summary" class="section">
                <h2>📊 Comprehensive Coverage Summary</h2>

                <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; border-left: 4px solid var(--accent-success); margin: 20px 0;">
                    <h3>✅ Complete Test Library Documentation</h3>
                    <p>This document provides comprehensive sequence diagrams for <strong>31 test procedures</strong> covering all major test standards in the TXD Qualification Library. Each diagram has been validated for Mermaid syntax compliance and accurately represents the actual test implementation with proper timing, error handling, and equipment coordination.</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: var(--bg-primary); padding: 20px; border-radius: 8px; box-shadow: 0 4px 15px var(--shadow-light);">
                        <h4>🔧 Technical Accuracy</h4>
                        <ul style="padding-left: 20px; line-height: 1.8;">
                            <li>Handler method calls match actual code</li>
                            <li>Timing values from real implementations</li>
                            <li>Error conditions based on code analysis</li>
                            <li>Equipment coordination properly shown</li>
                        </ul>
                    </div>
                    <div style="background: var(--bg-primary); padding: 20px; border-radius: 8px; box-shadow: 0 4px 15px var(--shadow-light);">
                        <h4>🎨 Visual Quality</h4>
                        <ul style="padding-left: 20px; line-height: 1.8;">
                            <li>Consistent participant naming</li>
                            <li>Professional formatting</li>
                            <li>Theme-aware color schemes</li>
                            <li>Mobile responsive design</li>
                        </ul>
                    </div>
                    <div style="background: var(--bg-primary); padding: 20px; border-radius: 8px; box-shadow: 0 4px 15px var(--shadow-light);">
                        <h4>🔍 Syntax Validation</h4>
                        <ul style="padding-left: 20px; line-height: 1.8;">
                            <li>All Mermaid diagrams validated</li>
                            <li>Special characters properly handled</li>
                            <li>Loop/alt/par blocks correct</li>
                            <li>Arrow syntax standardized</li>
                        </ul>
                    </div>
                </div>

                <div style="background: var(--bg-primary); padding: 20px; border-radius: 8px; box-shadow: 0 4px 15px var(--shadow-light); margin: 20px 0;">
                    <h4>📈 Coverage Statistics</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 15px 0;">
                        <div style="text-align: center; padding: 10px; background: var(--bg-secondary); border-radius: 6px;">
                            <div style="font-size: 1.5em; color: var(--accent-primary); font-weight: bold;">8</div>
                            <div style="font-size: 0.9em;">DO-181E Sequences</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: var(--bg-secondary); border-radius: 6px;">
                            <div style="font-size: 1.5em; color: var(--accent-success); font-weight: bold;">6</div>
                            <div style="font-size: 0.9em;">DO-189 Sequences</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: var(--bg-secondary); border-radius: 6px;">
                            <div style="font-size: 1.5em; color: var(--accent-warning); font-weight: bold;">4</div>
                            <div style="font-size: 0.9em;">DO-385 Sequences</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: var(--bg-secondary); border-radius: 6px;">
                            <div style="font-size: 1.5em; color: var(--accent-danger); font-weight: bold;">4</div>
                            <div style="font-size: 0.9em;">FAR43 Sequences</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: var(--bg-secondary); border-radius: 6px;">
                            <div style="font-size: 1.5em; color: var(--text-secondary); font-weight: bold;">4</div>
                            <div style="font-size: 0.9em;">DO-282 Sequences</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: var(--bg-secondary); border-radius: 6px;">
                            <div style="font-size: 1.5em; color: var(--text-muted); font-weight: bold;">5</div>
                            <div style="font-size: 0.9em;">Utility Modules</div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Mermaid.js for sequence diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>

    <script>
        // Theme management
        function loadTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            const button = document.querySelector('.theme-toggle');
            button.textContent = savedTheme === 'dark' ? '☀️ Light Theme' : '🌓 Dark Theme';
        }

        // Initialize Mermaid with theme support
        function initializeMermaid() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const mermaidTheme = currentTheme === 'dark' ? 'dark' : 'default';

            mermaid.initialize({
                startOnLoad: true,
                theme: mermaidTheme,
                securityLevel: 'loose',
                sequence: {
                    diagramMarginX: 50,
                    diagramMarginY: 10,
                    actorMargin: 50,
                    width: 150,
                    height: 65,
                    boxMargin: 10,
                    boxTextMargin: 5,
                    noteMargin: 10,
                    messageMargin: 35,
                    mirrorActors: true,
                    bottomMarginAdj: 1,
                    useMaxWidth: true,
                    rightAngles: false,
                    showSequenceNumbers: false
                },
                themeVariables: {
                    primaryColor: currentTheme === 'dark' ? '#4fc3f7' : '#3498db',
                    primaryTextColor: currentTheme === 'dark' ? '#e0e0e0' : '#333333',
                    primaryBorderColor: currentTheme === 'dark' ? '#29b6f6' : '#2980b9',
                    lineColor: currentTheme === 'dark' ? '#666666' : '#cccccc',
                    secondaryColor: currentTheme === 'dark' ? '#2d2d2d' : '#f8f9fa',
                    tertiaryColor: currentTheme === 'dark' ? '#3d3d3d' : '#ffffff'
                }
            });
        }

        // Re-render Mermaid diagrams when theme changes
        function updateMermaidTheme() {
            initializeMermaid();
            // Re-render all mermaid diagrams
            document.querySelectorAll('.mermaid').forEach((element, index) => {
                const graphDefinition = element.textContent;
                element.innerHTML = '';
                element.setAttribute('data-processed', 'false');
                mermaid.render(`mermaid-${index}`, graphDefinition, (svgCode) => {
                    element.innerHTML = svgCode;
                });
            });
        }

        // Theme toggle function
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Update button text
            const button = document.querySelector('.theme-toggle');
            button.textContent = newTheme === 'dark' ? '☀️ Light Theme' : '🌓 Dark Theme';

            // Update Mermaid diagrams with delay to ensure theme is applied
            setTimeout(updateMermaidTheme, 200);
        }



        // Navigation functionality
        function initializeNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all nav items
                    navItems.forEach(nav => nav.classList.remove('active'));

                    // Add active class to clicked item
                    this.classList.add('active');

                    // Scroll to target section
                    const targetId = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);
                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            // Load theme first
            loadTheme();

            // Initialize Mermaid
            initializeMermaid();

            // Initialize navigation
            initializeNavigation();

            // Set first nav item as active
            const firstNavItem = document.querySelector('.nav-item');
            if (firstNavItem) {
                firstNavItem.classList.add('active');
            }

            // Animate initial load
            setTimeout(() => {
                document.querySelectorAll('.section').forEach((section, index) => {
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 300);
        });
    </script>
</body>
</html>
