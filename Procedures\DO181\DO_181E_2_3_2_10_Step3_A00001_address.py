# -*- coding: utf-8 -*-
"""

@author: E282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Diversity Operation, Section ********
             
			 Step 3 Delay Selection Test:
             A)Synchronize interrogation to Channels A and B so that they are 0.375usec
               appart, where Channel A is first.
               Use a ATCRBS ModeC and ModeS (UF=4) to interrogate at a power level on Chan A of MTL+6 and
               a power level on Chan B of -50dBm.
               Verify the correct Reply Channel is Chan A.
             B)Synchronize interrogation to Channels A and B so that they are 0.375usec
               appart, where Channel B is first.
               Use a ATCRBS ModeC and ModeS (UF=4) to interrogate at a power level on Chan A of -50dBm and
               a power level on Chan B of MTL + 6.
               Verify the correct Reply Channel is Chan B.
                         
             
INPUTS:      RM,ATC,Top_PathLoss, Bot_PathLoss
OUTPUTS:     A_ReplyRate_ChanA       #Reply Rate ChanA (Top) - Test A,ModeC
             A_ReplyRate_ChanB       #Reply Rate ChanB (Bot) - Test A,ModeC
             B_ReplyRate_ChanA       #Reply Rate ChanA (Top) - Test B,ModeC
             B_ReplyRate_ChanB       #Reply Rate ChanB (Bot) - Test B,ModeC
             C_ReplyRate_ChanA       #Reply Rate ChanA (Top) - Test C,ModeS
             C_ReplyRate_ChanB       #Reply Rate ChanB (Bot) - Test C,ModeS
             D_ReplyRate_ChanA       #Reply Rate ChanA (Top) - Test D,ModeS
             D_ReplyRate_ChanB       #Reply Rate ChanB (Bot) - Test D,ModeS
HISTORY:

04/27/2020   MRS    Initial Release.
05/11/2020   MRS    Cleanup.
03/08/2021   MRS    Updates for New Handlers and Lobster.
05/16/2024   CS     Updated for ATR Prequal                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG
from TXDLib.Handlers import RFBOB
from TXDLib.Handlers import B4500CPwrMeter



##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_10_Step3(rm,atc,PathLossTop,PathLossBot):

    rm.logMessage(2,"*** DO-181E, Diversity Operation: Sect ********_Step3 ***")
	
 
    #Set the ATC Scope 1 to SYNCH
    atc.gwrite(":ATC:SET:SCO:CH1 24")     #used as trigger for power meter

    #Initialize Power Levels
    A_ReplyRate_ChanA  = 0.0       #Reply Rate ChanA (Top) - Test A
    A_ReplyRate_ChanB  = 0.0       #Reply Rate ChanB (Bot) - Test A
    B_ReplyRate_ChanA  = 0.0       #Reply Rate ChanA (Top) - Test B
    B_ReplyRate_ChanB  = 0.0       #Reply Rate ChanB (Bot) - Test B
    C_ReplyRate_ChanA  = 0.0       #Reply Rate ChanA (Top) - Test C
    C_ReplyRate_ChanB  = 0.0       #Reply Rate ChanB (Bot) - Test C
    D_ReplyRate_ChanA  = 0.0       #Reply Rate ChanA (Top) - Test D
    D_ReplyRate_ChanB  = 0.0       #Reply Rate ChanB (Bot) - Test D
    
    #Adjust Power Levels by RF PathLoss
    MTL_Top = -73.0 + 6.0 + PathLossTop
    MTL_Bot = -73.0 + 6.0 + PathLossBot
    FiftyDb_Top = -50.0 + PathLossTop
    FiftyDb_Bot = -50.0 + PathLossBot
    
    
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set Up Transponder -- MODE C
    atc.transponderModeC()
    
    #Test A - Set Chan A 0.375usec ahead of Chan B
    rm.logMessage(1,"TestA SetUp for Top Ant")
    atc.gwrite(":ATC:XPDR:ANT:TIM 0.375")  #Set Time Difference 
    
    #Set Chan A and B Relative Power Levels
    atc.gwrite(":ATC:SET:GENA:POWER " + str(MTL_Top))     #Power Levels, Two TOP Generators
    atc.gwrite(":ATC:SET:GENB:POWER " + str(MTL_Top))     #Power Levels, Two TOP Generators
    atc.gwrite(":ATC:SET:GENC:POWER " + str(FiftyDb_Bot)) #Power Levels, BOTTOM Generators
    atc.gwrite(":ATC:SET:GEND:POWER " + str(FiftyDb_Bot)) #Power Levels, BOTTOM Generators
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   

    atc.waitforstatus()    

    #Get Reply Percent
    replyrate = atc.getPercentReply(2)
    time.sleep(1)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while (replyrate[1] == -1.0 or replyrate[1] == 20) and count < 10:
        replyrate = atc.getPercentReply(2)
        time.sleep(1)
        count = count + 1

    #Read Percent Replies for ChanA and ChanB (top/bot antennas)
    A_ReplyRate_ChanA = replyrate[0]
    A_ReplyRate_ChanB = replyrate[1]
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")


    #Test B - Set Chan B 0.375usec ahead of Chan A
    rm.logMessage(1,"TestB SetUp for Bot Ant")
    atc.gwrite(":ATC:XPDR:ANT:TIM -0.375")  #Set Time Difference 
    
    #Set Chan A and B Relative Power Levels
    atc.gwrite(":ATC:SET:GENA:POWER " + str(FiftyDb_Top))     #Power Levels, Two TOP Generators
    atc.gwrite(":ATC:SET:GENB:POWER " + str(FiftyDb_Top))     #Power Levels, Two TOP Generators
    atc.gwrite(":ATC:SET:GENC:POWER " + str(MTL_Bot))         #Power Levels, BOTTOM Generators
    atc.gwrite(":ATC:SET:GEND:POWER " + str(MTL_Bot))         #Power Levels, BOTTOM Generators
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   

    atc.waitforstatus()    

    #Get Reply Percent
    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate,try 10 time
    time.sleep(1)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while (replyrate[1] == -1.0 or replyrate[1] == 20) and count < 10:
        replyrate = atc.getPercentReply(2)
        time.sleep(1)
        count = count + 1

    #Read Percent Replies for ChanA and ChanB (top/bot antennas)
    B_ReplyRate_ChanA = replyrate[0]
    B_ReplyRate_ChanB = replyrate[1]
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")


    #Set Up Transponder -- MODE S
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:UF 20000000A00001") #Mode S Message DF4,PR0 Adrs000004 -- changed to address A00001
    
    #Test C - Set Chan A 0.375usec ahead of Chan B
    rm.logMessage(1,"TestC SetUp for Top Ant")
    atc.gwrite(":ATC:XPDR:ANT:TIM 0.375")  #Set Time Difference 
    
    #Set Chan A and B Relative Power Levels
    atc.gwrite(":ATC:SET:GENA:POWER " + str(MTL_Top))     #Power Levels, Two TOP Generators
    atc.gwrite(":ATC:SET:GENB:POWER " + str(MTL_Top))     #Power Levels, Two TOP Generators
    atc.gwrite(":ATC:SET:GENC:POWER " + str(FiftyDb_Bot)) #Power Levels, BOTTOM Generators
    atc.gwrite(":ATC:SET:GEND:POWER " + str(FiftyDb_Bot)) #Power Levels, BOTTOM Generators
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   

    atc.waitforstatus()    

    #Get Reply Percent
    replyrate = atc.getPercentReply(2)
    time.sleep(1)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while (replyrate[1] == -1.0 or replyrate[1] == 20) and count < 10:
        replyrate = atc.getPercentReply(2)
        time.sleep(1)
        count = count + 1

    #Read Percent Replies for ChanA and ChanB (top/bot antennas)
    C_ReplyRate_ChanA = replyrate[0]
    C_ReplyRate_ChanB = replyrate[1]
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")


    #Test D - Set Chan B 0.375usec ahead of Chan A
    rm.logMessage(1,"TestD SetUp for Bot Ant")
    atc.gwrite(":ATC:XPDR:ANT:TIM -0.375")  #Set Time Difference 
    
    #Set Chan A and B Relative Power Levels
    atc.gwrite(":ATC:SET:GENA:POWER " + str(FiftyDb_Top))     #Power Levels, Two TOP Generators
    atc.gwrite(":ATC:SET:GENB:POWER " + str(FiftyDb_Top))     #Power Levels, Two TOP Generators
    atc.gwrite(":ATC:SET:GENC:POWER " + str(MTL_Bot))         #Power Levels, BOTTOM Generators
    atc.gwrite(":ATC:SET:GEND:POWER " + str(MTL_Bot))         #Power Levels, BOTTOM Generators
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)   

    atc.waitforstatus()    

    #Get Reply Percent
    replyrate = atc.getPercentReply(2)
    time.sleep(1)
    # fix for erroneous reply rate,try 10 time
    count = 0
    while (replyrate[1] == -1.0 or replyrate[1] == 20) and count < 10:
        replyrate = atc.getPercentReply(2)
        time.sleep(1)
        count = count + 1

    #Read Percent Replies for ChanA and ChanB (top/bot antennas)
    D_ReplyRate_ChanA = replyrate[0]
    D_ReplyRate_ChanB = replyrate[1]
    
    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")


    rm.logMessage(0,"Test A: Reply Rates: %f,%f" % (A_ReplyRate_ChanA,A_ReplyRate_ChanB))   
    rm.logMessage(0,"Test B: Reply Rates: %f,%f" % (B_ReplyRate_ChanA,B_ReplyRate_ChanB))   
    rm.logMessage(0,"Test C: Reply Rates: %f,%f" % (C_ReplyRate_ChanA,C_ReplyRate_ChanB))   
    rm.logMessage(0,"Test D: Reply Rates: %f,%f" % (D_ReplyRate_ChanA,D_ReplyRate_ChanB))   
    rm.logMessage(2,"Done, closing session")
    

    #Return results back to teststand.
    return [A_ReplyRate_ChanA,A_ReplyRate_ChanB,B_ReplyRate_ChanA,B_ReplyRate_ChanB,
            C_ReplyRate_ChanA,C_ReplyRate_ChanB,D_ReplyRate_ChanA,D_ReplyRate_ChanB]

##############################################################################
#run as main from command line
if __name__ == "__main__":
    
    rm = ate_rm()

    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

    
    res = Test_2_3_2_10_Step3(rm,atc_obj,12.75,12.69)
    
    atc_obj.close()


