"""
        Information regarding the Observation Point Selection Table
        can be located on Table 3-54 on the TPLRF_ARD

        This module is has specific parsing applicible to <PERSON>'s test
        FPGA builds, redmond_00B in particular.
"""
import math
import os
from TXDLib.Procedures import twos_comp

def initiate(variables):

    try:
        os.mkdir(os.getcwd() + "\\Data")
    except FileExistsError:
        pass
    
    if any(x==variables['value'] for x in ['0']):
        return ADC_INPUT(variables)
    # elif any(x==variables['value'] for x in ['1']):
    #     DME_ADC_INPUT(variables)
    elif any(x==variables['value'] for x in ['19']):
        return I_Q_18_BIT(variables)
    elif any(x==variables['value'] for x in ['1','2','3','4','5','6','10','11','12','13','14','15','20','21','23','24']):
        return I_Q_20_BIT(variables)
    elif any(x==variables['value'] for x in ['17']):
        return XPDR_LOG_OUTPUT(variables)
    elif any(x==variables['value'] for x in ['26']):
        return LOG_12_BIT(variables)        
    elif any(x==variables['value'] for x in ['8']):
        return TCAS_MERGED_LOG_AND_PHASE(variables)
    elif any(x==variables['value'] for x in ['30']):
        CALIBRATION(variables)
    # elif any(x==variables['value'] for x in ['63']):
    #     if variables['fpga_revision'] > 81: # build E052
    #         TWELVE_BIT_UNSIGNED(variables)
    #     else:
    #         EIGHT_BIT_UNSIGNED(variables)
    # elif any(x==variables['value'] for x in ['22','23','24','25','47',
    #                                          '48','49','50','64']):
    #     TCAS_DDC_MAGNITUDE(variables)
    # elif any(x==variables['value'] for x in ['65']):
    #     DAC_OUTPUT(variables)
    # elif any(x==variables['value'] for x in ['67']):
    #     BEARING_PROCESSOR_CORDIC_PHASE(variables)
    # elif any(x==variables['value'] for x in ['68']):
    #     BEARING_PROCESSOR_CORDIC_PHASE_AND_MAG(variables)
    # elif any(x==variables['value'] for x in ['69']):
    #     BEARING_PROCESSOR_TX_MONITOR(variables)
    # elif any(x==variables['value'] for x in ['70']):
    #     BEARING_PROCESSOR_RX_ALIGNMENT_VECTORS(variables)
    else:
        GENERIC_PRINT_OUT(variables)

# ADC_Data
# as of E034, ADC data corresponds to OPST (Observation Point Selection Table)
# index 0
def ADC_INPUT(variables):
    print("ADC_INPUT")
    ADC_E1 = []
    ADC_E2 = []
    ADC_E3 = []
    ADC_E4 = []
    if variables['hex_format'] == True:
        ADC_E1_HEX = []
        ADC_E2_HEX = []
        ADC_E3_HEX = []
        ADC_E4_HEX = []
        
    for i in range(len(variables['csv_data'])):
        current_data = bin(int(variables['csv_data'][i],16))[2:]
        current_data = current_data.zfill(64)
        ADC_E1_value = current_data[2:16].zfill(14)
        ADC_E2_value = current_data[18:32].zfill(14)
        ADC_E3_value = current_data[34:48].zfill(14)
        ADC_E4_value = current_data[50:].zfill(14)
        ADC_E1_value = twos_comp.convert(int(ADC_E1_value,2),len(ADC_E1_value))
        ADC_E2_value = twos_comp.convert(int(ADC_E2_value,2),len(ADC_E2_value))
        ADC_E3_value = twos_comp.convert(int(ADC_E3_value,2),len(ADC_E3_value))
        ADC_E4_value = twos_comp.convert(int(ADC_E4_value,2),len(ADC_E4_value))
        ADC_E1_BIN_value = twos_comp.convert_return_binary_string(bin(ADC_E1_value),14)
        ADC_E2_BIN_value = twos_comp.convert_return_binary_string(bin(ADC_E2_value),14)
        ADC_E3_BIN_value = twos_comp.convert_return_binary_string(bin(ADC_E3_value),14)
        ADC_E4_BIN_value = twos_comp.convert_return_binary_string(bin(ADC_E4_value),14)
        if variables['hex_format'] == True:
            ADC_E1_HEX_value = hex(int(ADC_E1_BIN_value,2))
            ADC_E2_HEX_value = hex(int(ADC_E2_BIN_value,2))
            ADC_E3_HEX_value = hex(int(ADC_E3_BIN_value,2))
            ADC_E4_HEX_value = hex(int(ADC_E4_BIN_value,2))
            ADC_E1_HEX_value = '0x' + ADC_E1_HEX_value[2:].zfill(4)
            ADC_E2_HEX_value = '0x' + ADC_E2_HEX_value[2:].zfill(4)
            ADC_E3_HEX_value = '0x' + ADC_E3_HEX_value[2:].zfill(4)
            ADC_E4_HEX_value = '0x' + ADC_E4_HEX_value[2:].zfill(4)
            ADC_E1_HEX.append(ADC_E1_HEX_value)
            ADC_E2_HEX.append(ADC_E2_HEX_value)
            ADC_E3_HEX.append(ADC_E3_HEX_value)
            ADC_E4_HEX.append(ADC_E4_HEX_value)
        ADC_E1.append(ADC_E1_value)
        ADC_E2.append(ADC_E2_value)
        ADC_E3.append(ADC_E3_value)
        ADC_E4.append(ADC_E4_value)


    # decimal printout
    if variables['dec_format'] == True:
        if variables['freq_sweep'] == True:
            ADC_E1.insert(0,'ADC_E1_' + str(variables['frequency']))
            ADC_E2.insert(0,'ADC_E2_' + str(variables['frequency']))
            ADC_E3.insert(0,'ADC_E3_' + str(variables['frequency']))
            ADC_E4.insert(0,'ADC_E4_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            ADC_E1.insert(0,'ADC_E1_' + str(variables['power_level']))
            ADC_E2.insert(0,'ADC_E2_' + str(variables['power_level']))
            ADC_E3.insert(0,'ADC_E3_' + str(variables['power_level']))
            ADC_E4.insert(0,'ADC_E4_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            ADC_E1.insert(0,'ADC_E1_' + str(variables['loop']))
            ADC_E2.insert(0,'ADC_E2_' + str(variables['loop']))
            ADC_E3.insert(0,'ADC_E3_' + str(variables['loop']))
            ADC_E4.insert(0,'ADC_E4_' + str(variables['loop']))
        else:
            ADC_E1.insert(0,'ADC_E1')
            ADC_E2.insert(0,'ADC_E2')
            ADC_E3.insert(0,'ADC_E3')
            ADC_E4.insert(0,'ADC_E4')
            
        variables['dec_csv'].tuple_add(ADC_E1)
        variables['dec_csv'].tuple_add(ADC_E2)
        variables['dec_csv'].tuple_add(ADC_E3)
        variables['dec_csv'].tuple_add(ADC_E4)

        print("ADC_INPUT END")
        return (ADC_E1, ADC_E2, ADC_E3, ADC_E4)

    # hex format
    if variables['hex_format'] == True:
        if variables['freq_sweep'] == True:
            ADC_E1_HEX.insert(0,'ADC_E1_' + str(variables['frequency']))
            ADC_E2_HEX.insert(0,'ADC_E2_' + str(variables['frequency']))
            ADC_E3_HEX.insert(0,'ADC_E3_' + str(variables['frequency']))
            ADC_E4_HEX.insert(0,'ADC_E4_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            ADC_E1_HEX.insert(0,'ADC_E1_' + str(variables['power_level']))
            ADC_E2_HEX.insert(0,'ADC_E2_' + str(variables['power_level']))
            ADC_E3_HEX.insert(0,'ADC_E3_' + str(variables['power_level']))
            ADC_E4_HEX.insert(0,'ADC_E4_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            ADC_E1_HEX.insert(0,'ADC_E1_' + str(variables['loop']))
            ADC_E2_HEX.insert(0,'ADC_E2_' + str(variables['loop']))
            ADC_E3_HEX.insert(0,'ADC_E3_' + str(variables['loop']))
            ADC_E4_HEX.insert(0,'ADC_E4_' + str(variables['loop']))
        else:
            ADC_E1.insert(0,'ADC_E1_HEX')
            ADC_E2.insert(0,'ADC_E2_HEX')
            ADC_E3.insert(0,'ADC_E3_HEX')
            ADC_E4.insert(0,'ADC_E4_HEX')
  
        variables['hex_csv'].tuple_add(ADC_E1_HEX)
        variables['hex_csv'].tuple_add(ADC_E2_HEX)
        variables['hex_csv'].tuple_add(ADC_E3_HEX)
        variables['hex_csv'].tuple_add(ADC_E4_HEX)

        return (ADC_E1_HEX, ADC_E2_HEX, ADC_E3_HEX, ADC_E4_HEX)


def GENERIC_PRINT_OUT(variables):
    QWORD_DATA = []
    if variables['hex_format'] == True:
        QWORD_DATA_HEX = []
        
    for i in range(len(variables['csv_data'])):
        current_data = bin(int(variables['csv_data'][i],16))[2:]
        current_data = current_data.zfill(64)
        current_data_twos_comp = twos_comp.convert(int(current_data,2),len(current_data))
        QWORD_DATA_HEX_value = hex(int(current_data,2))
        QWORD_DATA_HEX_value = '0x' + QWORD_DATA_HEX_value[2:].zfill(16)
        QWORD_DATA.append(current_data_twos_comp)

    if variables['freq_sweep'] == True:
        QWORD_DATA.insert(0,'DATA_' + str(variables['frequency']))
    elif variables['power_sweep'] == True:
        QWORD_DATA.insert(0,'DATA_' + str(variables['power_level']))
    elif variables['no_sweep'] == True:
        QWORD_DATA.insert(0,'DATA_' + str(variables['loop']))
    else:
        QWORD_DATA.insert(0, "DATA")
            
        variables['dec_csv'].tuple_add(QWORD_DATA)

# as of E056, DME_ADC_INPUT corresponds to indices:
# 1
def DME_ADC_INPUT(variables): # 14-bit ADC
    DME_ADC_DATA = []
    if variables['hex_format'] == True:
        DME_ADC_HEX_DATA = []

    for i in range(len(variables['csv_data'])):
        current_data = bin(variables['csv_data'][i])[2:]
        current_data = current_data.zfill(64)
        DME_ADC_VALUE = current_data[18:32].zfill(14)
        DME_ADC_VALUE = twos_comp.convert(int(DME_ADC_VALUE,2),len(DME_ADC_VALUE))
        DME_ADC_BIN_VALUE = twos_comp.convert_return_binary_string(bin(DME_ADC_VALUE),14)
        if variables['hex_format'] == True:
            DME_ADC_HEX_VALUE = hex(int(DME_ADC_BIN_VALUE,2))
            DME_ADC_HEX_VALUE = '0x' + DME_ADC_HEX_VALUE[2:].zfill(4)
            DME_ADC_HEX_DATA.append(DME_ADC_HEX_VALUE)
        DME_ADC_DATA.append(DME_ADC_VALUE)

    # decimal printout
    if variables['dec_format'] == True:
        if variables['freq_sweep'] == True:
            DME_ADC_DATA.insert(0,'DME_ADC_DATA_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            DME_ADC_DATA.insert(0,'DME_ADC_DATA_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            DME_ADC_DATA.insert(0,'DME_ADC_DATA_' + str(variables['loop']))         
            
        variables['dec_csv'].tuple_add(DME_ADC_DATA)

    # hex format
    if variables['hex_format'] == True:
        if variables['freq_sweep'] == True:
            DME_ADC_HEX_DATA.insert(0,'DME_ADC_HEX_DATA_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            DME_ADC_HEX_DATA.insert(0,'DME_ADC_HEX_DATA_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            DME_ADC_HEX_DATA.insert(0,'DME_ADC_HEX_DATA_' + str(variables['loop']))
  
        variables['hex_csv'].tuple_add(DME_ADC_HEX_DATA)

# as of E034, I_Q 18 bit (signed extended) values correspond to indices:
# 19
def I_Q_18_BIT(variables): # sign extended

    I = []
    Q = []
    RESULT = []
    if variables['hex_format'] == True:
        I_HEX = []
        Q_HEX = []
        RESULT_HEX = []

    for i in range(len(variables['csv_data'])):
        current_data = bin(int(variables['csv_data'][i],16))[2:]
        current_data = current_data.zfill(64)
        I_value = parse_bits(current_data, 49, 32)
        Q_value = parse_bits(current_data, 17, 0)
        I_value = twos_comp.convert(int(I_value,2),len(I_value)) ##returns an int
        Q_value = twos_comp.convert(int(Q_value,2),len(Q_value))
        # I_value_bin = twos_comp.convert_return_binary_string(bin(I_value),18)
        # Q_value_bin = twos_comp.convert_return_binary_string(bin(Q_value),18)
        result = math.sqrt(pow(I_value,2) + pow(Q_value,2))
        if result != 0:
            result = 20*math.log10(result)
        else:
            result = 0
        I.append(int(I_value))
        Q.append(int(Q_value))
        RESULT.append(result)

    if variables['dec_format'] == True:
        if variables['freq_sweep'] == True:
            I.insert(0,'I_Values_' + str(variables['frequency']))
            Q.insert(0,'Q_Values_' + str(variables['frequency']))
            RESULT.insert(0,'MAG_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            I.insert(0,'I_Values_' + str(variables['power_level']))
            Q.insert(0,'Q_Values_' + str(variables['power_level']))
            RESULT.insert(0,'MAG_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            I.insert(0,'I_Values_' + str(variables['loop']))
            Q.insert(0,'Q_Values_' + str(variables['loop']))
            RESULT.insert(0,'MAG_' + str(variables['loop']))
        else:
            I.insert(0,'I_Values')
            Q.insert(0,'Q_Values')
            RESULT.insert(0,'MAG')
            
        variables['dec_csv'].tuple_add(I)
        variables['dec_csv'].tuple_add(Q)
        variables['dec_csv'].tuple_add(RESULT)
        # print(len(I))
        # print(len(Q))
        # print(len(RESULT))
        return (RESULT,RESULT)

            
    if variables['hex_format'] == True:
        if variables['freq_sweep'] == True:
            I_HEX.insert(0,'I_Values_' + str(variables['frequency']))
            Q_HEX.insert(0,'Q_Values_' + str(variables['frequency']))
            RESULT.insert(0,'MAG_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            I_HEX.insert(0,'I_Values_' + str(variables['power_level']))
            Q_HEX.insert(0,'Q_Values_' + str(variables['power_level']))
            RESULT_HEX.insert(0,'MAG_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            I_HEX.insert(0,'I_Values_' + str(variables['loop']))
            Q_HEX.insert(0,'Q_Values_' + str(variables['loop']))
            RESULT_HEX.insert(0,'MAG_' + str(variables['loop']))

        variables['hex_csv'].tuple_add(I_HEX)
        variables['hex_csv'].tuple_add(Q_HEX)
        variables['hex_csv'].tuple_add(RESULT_HEX)
        return (RESULT,RESULT)


# as of E034, I_Q 20 bit (signed extended) values correspond to indices:
# 2,3,5,6,8,9,11,12,13,14,15,16,27,28,30,31,33,34,36,37,38,39,40,41,51,
# 52,54,55,56,57,
def I_Q_20_BIT(variables): # sign extended

    I = []
    Q = []
    RESULT = []
    if variables['hex_format'] == True:
        I_HEX = []
        Q_HEX = []
        RESULT_HEX = []
        
    for i in range(len(variables['csv_data'])):
        current_data = bin(int(variables['csv_data'][i],16))[2:]
        current_data = current_data.zfill(64)
        I_value = parse_bits(current_data, 51, 32)
        Q_value = parse_bits(current_data, 19, 0)
        I_value = twos_comp.convert(int(I_value,2),len(I_value)) ##returns an int
        Q_value = twos_comp.convert(int(Q_value,2),len(Q_value))
        I_value_bin = twos_comp.convert_return_binary_string(bin(I_value),20)
        Q_value_bin = twos_comp.convert_return_binary_string(bin(Q_value),20)
        result = math.sqrt(pow(I_value,2) + pow(Q_value,2))
        if result != 0:
            result = 20*math.log10(result)
        else:
            result = 0
        I.append(int(I_value))
        Q.append(int(Q_value))
        RESULT.append(result)
        if variables['hex_format'] == True:
            I_HEX_value = hex(int(I_value_bin,2))
            Q_HEX_value = hex(int(Q_value_bin,2))
            I_HEX_value = '0x' + I_HEX_value[2:].zfill(5)
            Q_HEX_value = '0x' + Q_HEX_value[2:].zfill(5)
            I_HEX.append(I_HEX_value)
            Q_HEX.append(Q_HEX_value)
            RESULT_HEX.append(result)        
        
    if variables['dec_format'] == True:
        if variables['freq_sweep'] == True:
            I.insert(0,'I_Values_' + str(variables['frequency']))
            Q.insert(0,'Q_Values_' + str(variables['frequency']))
            RESULT.insert(0,'MAG_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            I.insert(0,'I_Values_' + str(variables['power_level']))
            Q.insert(0,'Q_Values_' + str(variables['power_level']))
            RESULT.insert(0,'MAG_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            I.insert(0,'I_Values_' + str(variables['loop']))
            Q.insert(0,'Q_Values_' + str(variables['loop']))
            RESULT.insert(0,'MAG_' + str(variables['loop']))
        else:
            I.insert(0,'I_Values')
            Q.insert(0,'Q_Values')
            RESULT.insert(0,'MAG')

        variables['dec_csv'].tuple_add(I)
        variables['dec_csv'].tuple_add(Q)
        variables['dec_csv'].tuple_add(RESULT)
        
    if variables['hex_format'] == True:
        if variables['freq_sweep'] == True:
            I_HEX.insert(0,'I_Values_' + str(variables['frequency']))
            Q_HEX.insert(0,'Q_Values_' + str(variables['frequency']))
            RESULT.insert(0,'MAG_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            I_HEX.insert(0,'I_Values_' + str(variables['power_level']))
            Q_HEX.insert(0,'Q_Values_' + str(variables['power_level']))
            RESULT_HEX.insert(0,'MAG_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            I_HEX.insert(0,'I_Values_' + str(variables['loop']))
            Q_HEX.insert(0,'Q_Values_' + str(variables['loop']))
            RESULT_HEX.insert(0,'MAG_' + str(variables['loop']))

        variables['hex_csv'].tuple_add(I_HEX)
        variables['hex_csv'].tuple_add(Q_HEX)
        variables['hex_csv'].tuple_add(RESULT_HEX)

    return RESULT

def XPDR_LOG_OUTPUT(variables):
    top = []
    bottom = []

    top.append("TOP")
    bottom.append("BOTTOM")

    for i in range(len(variables['csv_data'])):
        current_data = bin(int(variables['csv_data'][i],16))[2:]
        current_data = current_data.zfill(64)
        top_value = int(current_data[16:24].zfill(8), 2)
        bottom_value = int(current_data[24:32].zfill(8), 2)
        top.append(top_value)
        bottom.append(bottom_value)


    variables['dec_csv'].tuple_add(top)
    variables['dec_csv'].tuple_add(bottom)

    return (top, bottom)

# see HSID3191 #8
def TCAS_MERGED_LOG_AND_PHASE(variables):
    # entry 0
    Log_Secondary = []
    Log_Primary = []
    Log_BS3 = []
    Log_BS2 = []
    Log_BS1 = []
    Log_BS0 = []
    # entry 1
    Phase_Secondary = []
    Phase_Primary = []
    Phase_BS3 = []
    Phase_BS2 = []
    Phase_BS1 = []
    Phase_BS0 = []

    Phase_Secondary.append("Phase_Secondary")
    Phase_Primary.append("Phase_Primary")
    Phase_BS3.append("Phase_BS3")
    Phase_BS2.append("Phase_BS2")
    Phase_BS1.append("Phase_BS1")
    Phase_BS0.append("Phase_BS0")

    Log_Secondary.append("Log_Secondary")
    Log_Primary.append("Log_Primary")
    Log_BS3.append("Log_BS3")
    Log_BS2.append("Log_BS2")
    Log_BS1.append("Log_BS1")
    Log_BS0.append("Log_BS0")


    for i in range(len(variables['csv_data'])):
        current_data = bin(int(variables['csv_data'][i],16))[2:]
        current_data = current_data.zfill(64)
        if current_data[0] == '0':
            Log_Secondary_Value = int(parse_bits(current_data, 47, 40), 2)
            Log_Primary_Value = int(parse_bits(current_data, 39, 32), 2)
            Log_BS3_Value = int(parse_bits(current_data, 31, 24), 2)
            Log_BS2_Value = int(parse_bits(current_data, 23, 16), 2)
            Log_BS1_Value = int(parse_bits(current_data, 15, 8), 2)
            Log_BS0_Value = int(parse_bits(current_data, 7, 0), 2)
            Log_Secondary.append(Log_Secondary_Value)
            Log_Primary.append(Log_Primary_Value)
            Log_BS3.append(Log_BS3_Value)
            Log_BS2.append(Log_BS2_Value)
            Log_BS1.append(Log_BS1_Value)
            Log_BS0.append(Log_BS0_Value)
        else:
            Phase_Secondary_Value = twos_comp.convert(int(parse_bits(current_data, 61, 52), 2), 10)
            Phase_Primary_Value = twos_comp.convert(int(parse_bits(current_data, 51, 42), 2), 10)
            Phase_BS3_Value = twos_comp.convert(int(parse_bits(current_data, 41, 32), 2), 10)
            Phase_BS2_Value = twos_comp.convert(int(parse_bits(current_data, 29, 20), 2), 10)
            Phase_BS1_Value = twos_comp.convert(int(parse_bits(current_data, 19, 10), 2), 10)
            Phase_BS0_Value = twos_comp.convert(int(parse_bits(current_data, 9, 0), 2), 10)
            #Phase_Secondary_Value = int(parse_bits(current_data, 61, 52), 2)
            #Phase_Primary_Value = int(parse_bits(current_data, 51, 42), 2)
            #Phase_BS3_Value = int(parse_bits(current_data, 41, 32), 2)
            #Phase_BS2_Value = int(parse_bits(current_data, 29, 20), 2)
            #Phase_BS1_Value = int(parse_bits(current_data, 19, 10), 2)
            #Phase_BS0_Value = int(parse_bits(current_data, 9, 0), 2)
            Phase_Secondary.append(Phase_Secondary_Value)
            Phase_Primary.append(Phase_Primary_Value)
            Phase_BS3.append(Phase_BS3_Value)
            Phase_BS2.append(Phase_BS2_Value)
            Phase_BS1.append(Phase_BS1_Value)
            Phase_BS0.append(Phase_BS0_Value)

    variables['dec_csv'].tuple_add(Log_Secondary)
    variables['dec_csv'].tuple_add(Log_Primary)
    variables['dec_csv'].tuple_add(Log_BS3)
    variables['dec_csv'].tuple_add(Log_BS2)
    variables['dec_csv'].tuple_add(Log_BS1)
    variables['dec_csv'].tuple_add(Log_BS0)
    variables['dec_csv'].tuple_add(Phase_Secondary)
    variables['dec_csv'].tuple_add(Phase_Primary)
    variables['dec_csv'].tuple_add(Phase_BS3)
    variables['dec_csv'].tuple_add(Phase_BS2)
    variables['dec_csv'].tuple_add(Phase_BS1)
    variables['dec_csv'].tuple_add(Phase_BS0)

    return (Log_Primary, Log_Secondary)


def CALIBRATION(variables):
    PHASE_CH0 = []
    PHASE_CH1 = []
    LOG_CH0 = []
    LOG_CH1 = []
    CAL_SEQ_TIMER = []

    PHASE_CH0.append("PHASE_CH0")
    PHASE_CH1.append("PHASE_CH1")
    CAL_SEQ_TIMER.append("CAL_SEQUENC_TIMER")
    LOG_CH0.append("LOG_CH0")
    LOG_CH1.append("LOG_CH1")

    for i in range(len(variables['csv_data'])):
        current_data = bin(int(variables['csv_data'][i],16))[2:]
        current_data = current_data.zfill(64)
        CAL_SEQ_TIMER_value = current_data[5:16].zfill(11)
        LOG_CH1_value = current_data[16:24].zfill(8)
        LOG_CH0_value = current_data[24:32].zfill(8)
        PHASE_CH1_value = current_data[32:48].zfill(16)
        PHASE_CH0_value = current_data[48:].zfill(16)
        PHASE_CH0.append(int(PHASE_CH0_value,2))
        PHASE_CH1.append(int(PHASE_CH1_value,2))
        LOG_CH0.append(int(LOG_CH0_value,2))
        LOG_CH1.append(int(LOG_CH1_value,2))
        CAL_SEQ_TIMER.append(int(CAL_SEQ_TIMER_value,2))
    
    
    variables['dec_csv'].tuple_add(CAL_SEQ_TIMER)
    variables['dec_csv'].tuple_add(PHASE_CH0)
    variables['dec_csv'].tuple_add(PHASE_CH1)
    variables['dec_csv'].tuple_add(LOG_CH0)
    variables['dec_csv'].tuple_add(LOG_CH1)


def parse_bits(current_data, a, b):
    # index a to (inclusive) index b
    # take starting from MSB and parse it based on HSID
    a = 63 - a # swap MSB and LSB to go from integer parsing to string
    b = 63 - b # swap MSB and LSB to go from integer parsing to string
    return current_data[a:b+1]

# as of E052, 8 bit (unsigned) data corresponds to indices:
# 8
# TCAS DDC Log Amp & Log Sum Output 
# XPDR DDC Log Amp Output
def EIGHT_BIT_UNSIGNED(variables):
    iterator = []
    PRM_DDC_LOG_SUM = []
    SEC_DDC_LOG_SUM = []
    if variables['hex_format'] == True:
        PRM_DDC_LOG_SUM_HEX = []
        PRM_DDC_LOG_SUM_HEX = []

    for i in range(len(variables['csv_data'])):
        current_data = bin(int(variables['csv_data'][i],16))[2:]
        current_data = current_data.zfill(64)
##        I_value = current_data[12:32].zfill(20)
##        Q_value = current_data[44:].zfill(20)
##        current_data = bin(int(variables['csv_data'][i],16))[2:]
##        current_data = current_data.zfill(64)
##        current_data = bin(variables['csv_data'][i])[2:]
##        current_data = current_data.zfill(64)
        PRM_DDC_value = int(current_data[24:32],2)
        SEC_DDC_value = int(current_data[16:24],2)
        PRM_DDC_LOG_SUM.append(PRM_DDC_value)
        SEC_DDC_LOG_SUM.append(SEC_DDC_value)
##        if variables['hex_format'] == True:
##            DDC_HEX_value = '0x' + hex(DDC_value)[2:].zfill(2)
##            DDC_LOG_SUM_HEX.append(DDC_HEX_value)
        
        
    if variables['dec_format'] == True:
        if variables['freq_sweep'] == True:
            PRM_DDC_LOG_SUM.insert(0,'PRM_DDC_LOG_SUM_' + str(variables['frequency']))
            SEC_DDC_LOG_SUM.insert(0,'SEC_DDC_LOG_SUM_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            PRM_DDC_LOG_SUM.insert(0,'PRM_DDC_LOG_SUM_' + str(variables['power_level']))
            SEC_DDC_LOG_SUM.insert(0,'SEC_DDC_LOG_SUM_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            PRM_DDC_LOG_SUM.insert(0,'PRM_DDC_LOG_SUM_' + str(variables['loop']))
            SEC_DDC_LOG_SUM.insert(0,'SEC_DDC_LOG_SUM_' + str(variables['loop']))
            
        variables['dec_csv'].tuple_add(PRM_DDC_LOG_SUM)
        variables['dec_csv'].tuple_add(SEC_DDC_LOG_SUM)        
            
##    if variables['hex_format'] == True:
##        if variables['freq_sweep'] == True:
##            DDC_LOG_SUM_HEX.insert(0,'DDC_LOG_SUM_' + str(variables['frequency']))
##        elif variables['power_sweep'] == True:
##            DDC_LOG_SUM_HEX.insert(0,'DDC_LOG_SUM_' + str(variables['power_level']))
##        elif variables['no_sweep'] == True:
##            DDC_LOG_SUM_HEX.insert(0,'DDC_LOG_SUM_' + str(variables['loop']))
    
##        variables['hex_csv'].tuple_add(DDC_LOG_SUM_HEX)
        

# as of E052, Magnitude data corresponds to indices:
# 25
# DME Log Amp Output
def LOG_12_BIT(variables): # sign extended

    LOG_MAG = []
    LOG_AMP = []

    LOG_MAG.append("DME LOG MAG")
    LOG_AMP.append("DME LOG AMP")

    # if variables['hex_format'] == True:
    #     LOG_HEX = []

    # Log_Secondary_Value = int(parse_bits(current_data, 47, 40), 2)
    for i in range(len(variables['csv_data'])):
        current_data = bin(int(variables['csv_data'][i],16))[2:]
        current_data = current_data.zfill(64)
        LOG_MAG_value = parse_bits(current_data,63,32)
        LOG_MAG_value = twos_comp.convert(int(LOG_MAG_value,2),32) ##returns an int
        LOG_MAG_value_bin = twos_comp.convert_return_binary_string(bin(LOG_MAG_value),32)
        LOG_MAG.append(LOG_MAG_value)
        LOG_AMP_value = int(parse_bits(current_data,11,0),2)
        #LOG_AMP_value = int(current_data[52:64].zfill(32), 2)
        #LOG_AMP_value = twos_comp.convert(int(LOG_AMP_value,2),32) ##returns an int
        LOG_AMP.append(LOG_AMP_value)

    variables['dec_csv'].tuple_add(LOG_MAG)
    variables['dec_csv'].tuple_add(LOG_AMP)

    return (LOG_MAG, LOG_AMP)

    
# as of E034, Magnitude data corresponds to indices:
# 21,22,23,24,46,47,48,49
# TCAS_DDC_MAGNITUDE
# XPDR_DDC_MAGNITUDE
def TCAS_DDC_MAGNITUDE(variables):
    MAGNITUDES = []
    if variables['hex_format'] == True:
        MAGNITUDES_HEX = []

    for i in range(len(variables['csv_data'])):
        current_data = bin(variables['csv_data'][i])[2:]
        current_data = current_data.zfill(64)
        MAG_VALUE = int(current_data[12:32],2) #20 bit
        MAGNITUDES.append(MAG_VALUE)
        if variables['hex_format'] == True:
            MAG_HEX_VALUE = '0x' + hex(MAG_VALUE)[2:].zfill(5)
            MAGNITUDES_HEX.append(MAG_HEX_VALUE)
        
    if variables['dec_format'] == True:
        if variables['freq_sweep'] == True:
            MAGNITUDES.insert(0,'TCAS_DDC_MAGNITUDE_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            MAGNITUDES.insert(0,'TCAS_DDC_MAGNITUDE_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            MAGNITUDES.insert(0,'TCAS_DDC_MAGNITUDE_' + str(variables['loop']))

        variables['dec_csv'].tuple_add(MAGNITUDES)
        
    if variables['hex_format'] == True:
        if variables['freq_sweep'] == True:
            MAGNITUDES.insert(0,'TCAS_DDC_MAGNITUDE_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            MAGNITUDES.insert(0,'TCAS_DDC_MAGNITUDE_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            MAGNITUDES.insert(0,'TCAS_DDC_MAGNITUDE_' + str(variables['loop']))

        variables['hex_csv'].tuple_add(MAGNITUDES_HEX)        

# as of E034, DAC_OUTPUT data corresponds to indices:
# 58 
def DAC_OUTPUT(variables):
    DAC_E1_DATA = []
    DAC_E2_DATA = []
    DAC_E3_DATA = []
    DAC_E4_DATA = []

    for i in range(len(variables['csv_data'])):
        current_data = bin(variables['csv_data'][i])[2:]
        current_data = current_data.zfill(64)
        DAC_E1_VALUE = current_data[0:16]
        DAC_E2_VALUE = current_data[16:32]
        DAC_E3_VALUE = current_data[32:48]
        DAC_E4_VALUE = current_data[48:]
        DAC_E1_DATA.append(DAC_E1_VALUE)
        DAC_E2_DATA.append(DAC_E2_VALUE)
        DAC_E3_DATA.append(DAC_E3_VALUE)
        DAC_E4_DATA.append(DAC_E4_VALUE)

    if variables['freq_sweep'] == True: 
        DAC_E1_DATA.insert(0,'DAC_E1_' + str(variables['frequency']))
        DAC_E2_DATA.insert(0,'DAC_E2_' + str(variables['frequency']))
        DAC_E3_DATA.insert(0,'DAC_E3_' + str(variables['frequency']))
        DAC_E4_DATA.insert(0,'DAC_E4_' + str(variables['frequency']))
    elif variables['power_sweep'] == True:
        DAC_E1_DATA.insert(0,'DAC_E1_' + str(variables['power_level']))
        DAC_E2_DATA.insert(0,'DAC_E2_' + str(variables['power_level']))
        DAC_E3_DATA.insert(0,'DAC_E3_' + str(variables['power_level']))
        DAC_E4_DATA.insert(0,'DAC_E4_' + str(variables['power_level']))
    elif variables['no_sweep'] == True:
        DAC_E1_DATA.insert(0,'DAC_E1_' + str(variables['loop']))
        DAC_E2_DATA.insert(0,'DAC_E2_' + str(variables['loop']))
        DAC_E3_DATA.insert(0,'DAC_E3_' + str(variables['loop']))
        DAC_E4_DATA.insert(0,'DAC_E4_' + str(variables['loop']))        

    variables['dec_csv'].tuple_add(DAC_E1_DATA)
    variables['dec_csv'].tuple_add(DAC_E2_DATA)
    variables['dec_csv'].tuple_add(DAC_E3_DATA)
    variables['dec_csv'].tuple_add(DAC_E4_DATA)


# as of E051, Bearing Processor CORDIC Phase data corresponds to indices:
# 66
# Bearing Processor CORDIC Phase
# [63] = scal_s  (bp_samplecal_adjusted and stretched)
# [62] = tx_rx_non_delay - removed in E04a
# [61:52] = magnit_1_dbg  <= magnit_ffi(61 downto 52); 10 bits
# [51:39] = phase_4_dbg   <= phase_ffi(51 downto 39); 13 bits
# [38:26] = phase_3_dbg   <= phase_ffi(38 downto 26); 13 bits
# [25:13] = phase_2_dbg   <= phase_ffi(25 downto 13); 13 bits
# [12:0] =   phase_1_dbg   <= phase_ffi(12 downto 0); 13 bits
def BEARING_PROCESSOR_CORDIC_PHASE(variables):
    PHASE_E1_DATA = []
    PHASE_E2_DATA = []
    PHASE_E3_DATA = []
    PHASE_E4_DATA = []
    MAG_E1_DATA = []
    SAMPLE_CAL_DATA = []
    TX_RX_NON_DELAY_DATA = []
    if variables['hex_format'] == True:
        PHASE_E1_HEX_DATA = []
        PHASE_E2_HEX_DATA = []
        PHASE_E3_HEX_DATA = []
        PHASE_E4_HEX_DATA = []
        MAG_E1_HEX_DATA = [] 
    for i in range(len(variables['csv_data'])):
        current_data = bin(variables['csv_data'][i])[2:]
        current_data = current_data.zfill(64)
        SCAL_S_VALUE = current_data[0]
        TX_RX_NON_DELAY_VALUE = current_data[1]
        MAG_E1_VALUE = int(current_data[2:12], 2)
        PHASE_E4_VALUE = current_data[12:25].zfill(13)
        PHASE_E3_VALUE = current_data[25:38].zfill(13)
        PHASE_E2_VALUE = current_data[38:51].zfill(13)
        PHASE_E1_VALUE = current_data[51:64].zfill(13)
        PHASE_E4_VALUE = twos_comp.convert(int(PHASE_E4_VALUE, 2), len(PHASE_E4_VALUE))
        PHASE_E3_VALUE = twos_comp.convert(int(PHASE_E3_VALUE, 2), len(PHASE_E3_VALUE))
        PHASE_E2_VALUE = twos_comp.convert(int(PHASE_E2_VALUE, 2), len(PHASE_E2_VALUE))
        PHASE_E1_VALUE = twos_comp.convert(int(PHASE_E1_VALUE, 2), len(PHASE_E1_VALUE))
        PHASE_E4_BIN_VALUE = twos_comp.convert_return_binary_string(bin(PHASE_E4_VALUE), 13)
        PHASE_E3_BIN_VALUE = twos_comp.convert_return_binary_string(bin(PHASE_E3_VALUE), 16)
        PHASE_E2_BIN_VALUE = twos_comp.convert_return_binary_string(bin(PHASE_E2_VALUE), 16)
        PHASE_E1_BIN_VALUE = twos_comp.convert_return_binary_string(bin(PHASE_E1_VALUE), 16)
        MAG_E1_DATA.append(MAG_E1_VALUE)
        PHASE_E1_DATA.append(PHASE_E1_VALUE)
        PHASE_E2_DATA.append(PHASE_E2_VALUE)
        PHASE_E3_DATA.append(PHASE_E3_VALUE)
        PHASE_E4_DATA.append(PHASE_E4_VALUE)
        SAMPLE_CAL_DATA.append(SCAL_S_VALUE)
        TX_RX_NON_DELAY_DATA.append(TX_RX_NON_DELAY_VALUE)
        if variables['hex_format'] == True:
            MAG_E1_HEX_DATA.append('0x' + hex(MAG_E1_VALUE)[2:].zfill(4))
            PHASE_E1_HEX_VALUE = hex(int(PHASE_E1_BIN_VALUE,2))
            PHASE_E2_HEX_VALUE = hex(int(PHASE_E2_BIN_VALUE,2))
            PHASE_E3_HEX_VALUE = hex(int(PHASE_E3_BIN_VALUE,2))
            PHASE_E4_HEX_VALUE = hex(int(PHASE_E4_BIN_VALUE,2))
            PHASE_E1_HEX_VALUE = '0x' + PHASE_E1_HEX_VALUE[2:].zfill(4)
            PHASE_E2_HEX_VALUE = '0x' + PHASE_E2_HEX_VALUE[2:].zfill(4)
            PHASE_E3_HEX_VALUE = '0x' + PHASE_E3_HEX_VALUE[2:].zfill(4)
            PHASE_E4_HEX_VALUE = '0x' + PHASE_E4_HEX_VALUE[2:].zfill(4)
            PHASE_E1_HEX_DATA.append(PHASE_E1_HEX_VALUE)
            PHASE_E2_HEX_DATA.append(PHASE_E2_HEX_VALUE)
            PHASE_E3_HEX_DATA.append(PHASE_E3_HEX_VALUE)
            PHASE_E4_HEX_DATA.append(PHASE_E4_HEX_VALUE)

    if variables['dec_format'] == True:
        if variables['freq_sweep'] == True:
            MAG_E1_DATA.insert(0, 'magnit_1_dbg_' + str(variables['frequency']))
            PHASE_E1_DATA.insert(0, 'phase_1_dbg_' + str(variables['frequency']))
            PHASE_E2_DATA.insert(0, 'phase_2_dbg_' + str(variables['frequency']))
            PHASE_E3_DATA.insert(0, 'phase_3_dbg_' + str(variables['frequency']))
            PHASE_E4_DATA.insert(0, 'phase_4_dbg_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            MAG_E1_DATA.insert(0, 'magnit_1_dbg_' + str(variables['power_level']))
            PHASE_E1_DATA.insert(0,'phase_1_dbg_' + str(variables['power_level']))
            PHASE_E2_DATA.insert(0,'phase_2_dbg_' + str(variables['power_level']))
            PHASE_E3_DATA.insert(0,'phase_3_dbg_' + str(variables['power_level']))
            PHASE_E4_DATA.insert(0,'phase_4_dbg_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            MAG_E1_DATA.insert(0, 'magnit_1_dbg_' + str(variables['loop']))
            PHASE_E1_DATA.insert(0,'phase_1_dbg_' + str(variables['loop']))
            PHASE_E2_DATA.insert(0,'phase_2_dbg_' + str(variables['loop']))
            PHASE_E3_DATA.insert(0,'phase_3_dbg_' + str(variables['loop']))
            PHASE_E4_DATA.insert(0,'phase_4_dbg_' + str(variables['loop']))

        variables['dec_csv'].tuple_add(MAG_E1_DATA)
        variables['dec_csv'].tuple_add(PHASE_E1_DATA)
        variables['dec_csv'].tuple_add(PHASE_E2_DATA)
        variables['dec_csv'].tuple_add(PHASE_E3_DATA)
        variables['dec_csv'].tuple_add(PHASE_E4_DATA)
        
    if variables['hex_format'] == True:
        if variables['freq_sweep'] == True:
            MAG_E1_HEX_DATA.insert(0, 'magnit_1_dbg_' + str(variables['frequency']))
            PHASE_E1_HEX_DATA.insert(0, 'phase_1_dbg_' + str(variables['frequency']))
            PHASE_E2_HEX_DATA.insert(0, 'phase_2_dbg_' + str(variables['frequency']))
            PHASE_E3_HEX_DATA.insert(0, 'phase_3_dbg_' + str(variables['frequency']))
            PHASE_E4_HEX_DATA.insert(0, 'phase_4_dbg_' + str(variables['frequency']))             
        elif variables['power_sweep'] == True:
            MAG_E1_HEX_DATA.insert(0, 'magnit_1_dbg_' + str(variables['power_level']))
            PHASE_E1_HEX_DATA.insert(0,'phase_1_dbg_' + str(variables['power_level']))
            PHASE_E2_HEX_DATA.insert(0,'phase_2_dbg_' + str(variables['power_level']))
            PHASE_E3_HEX_DATA.insert(0,'phase_3_dbg_' + str(variables['power_level']))
            PHASE_E4_HEX_DATA.insert(0,'phase_4_dbg_' + str(variables['power_level'])) 
        elif variables['no_sweep'] == True:
            MAG_E1_HEX_DATA.insert(0, 'magnit_1_dbg_' + str(variables['loop']))
            PHASE_E1_HEX_DATA.insert(0,'phase_1_dbg_' + str(variables['loop']))
            PHASE_E2_HEX_DATA.insert(0,'phase_2_dbg_' + str(variables['loop']))
            PHASE_E3_HEX_DATA.insert(0,'phase_3_dbg_' + str(variables['loop']))
            PHASE_E4_HEX_DATA.insert(0,'phase_4_dbg_' + str(variables['loop']))

        variables['hex_csv'].tuple_add(MAG_E1_HEX_DATA)
        variables['hex_csv'].tuple_add(PHASE_E1_HEX_DATA)
        variables['hex_csv'].tuple_add(PHASE_E2_HEX_DATA)
        variables['hex_csv'].tuple_add(PHASE_E3_HEX_DATA)
        variables['hex_csv'].tuple_add(PHASE_E4_HEX_DATA)


# as of E051, Bearing Processor CORDIC Phase data corresponds to indices:
# 67
# Bearing Processor CORDIC Phase and Magnitude
# [63] = scal_s  (bp_samplecal_adjusted and stretched)
# [62] = tx_rx_non_delay
# [61:52] = magnit_out_1_dbg  <= magnit_ffo(61 downto 52);
# [51:39] = phase_out_4_dbg   <= phase_ffo(51 downto 39); 13 bits
# [38:26] = phase_out_3_dbg   <= phase_ffo(38 downto 26);
# [25:13] = phase_out_2_dbg   <= phase_ffo(25 downto 13);
# [12:0] =  phase_out_1_dbg  <= phase_ffo(12 downto  0);
def BEARING_PROCESSOR_CORDIC_PHASE_AND_MAG(variables):
    PHASE_E1_DATA = []
    PHASE_E2_DATA = []
    PHASE_E3_DATA = []
    PHASE_E4_DATA = []
    MAG_E1_DATA = []
    SAMPLE_CAL_DATA = []
    TX_RX_NON_DELAY_DATA = []
    if variables['hex_format'] == True:
        PHASE_E1_HEX_DATA = []
        PHASE_E2_HEX_DATA = []
        PHASE_E3_HEX_DATA = []
        PHASE_E4_HEX_DATA = []
        MAG_E1_HEX_DATA = [] 
    for i in range(len(variables['csv_data'])):
        current_data = bin(variables['csv_data'][i])[2:]
        current_data = current_data.zfill(64)
        # SCAL_S_VALUE = current_data[0]
        # TX_RX_NON_DELAY_VALUE = current_data[1]
        # MAG_E1_VALUE = int(current_data[2:12], 2)
        # PHASE_E4_VALUE = current_data[12:25].zfill(13)
        # PHASE_E3_VALUE = current_data[25:38].zfill(13)
        # PHASE_E2_VALUE = current_data[38:51].zfill(13)
        # PHASE_E1_VALUE = current_data[51:].zfill(13)
        SCAL_S_VALUE = current_data[0]
        TX_RX_NON_DELAY_VALUE = current_data[1]
        MAG_E1_VALUE = int(current_data[2:12], 2)
        PHASE_E4_VALUE = current_data[12:25].zfill(13)
        PHASE_E3_VALUE = current_data[25:38].zfill(13)
        PHASE_E2_VALUE = current_data[38:51].zfill(13)
        PHASE_E1_VALUE = current_data[51:64].zfill(13)
        PHASE_E4_VALUE = twos_comp.convert(int(PHASE_E4_VALUE, 2), len(PHASE_E4_VALUE))
        PHASE_E3_VALUE = twos_comp.convert(int(PHASE_E3_VALUE, 2), len(PHASE_E3_VALUE))
        PHASE_E2_VALUE = twos_comp.convert(int(PHASE_E2_VALUE, 2), len(PHASE_E2_VALUE))
        PHASE_E1_VALUE = twos_comp.convert(int(PHASE_E1_VALUE, 2), len(PHASE_E1_VALUE))
        PHASE_E4_BIN_VALUE = twos_comp.convert_return_binary_string(bin(PHASE_E4_VALUE), 13)
        PHASE_E3_BIN_VALUE = twos_comp.convert_return_binary_string(bin(PHASE_E3_VALUE), 13)
        PHASE_E2_BIN_VALUE = twos_comp.convert_return_binary_string(bin(PHASE_E2_VALUE), 13)
        PHASE_E1_BIN_VALUE = twos_comp.convert_return_binary_string(bin(PHASE_E1_VALUE), 13)
        MAG_E1_DATA.append(MAG_E1_VALUE)
        PHASE_E1_DATA.append(PHASE_E1_VALUE)
        PHASE_E2_DATA.append(PHASE_E2_VALUE)
        PHASE_E3_DATA.append(PHASE_E3_VALUE)
        PHASE_E4_DATA.append(PHASE_E4_VALUE)
        SAMPLE_CAL_DATA.append(SCAL_S_VALUE)
        TX_RX_NON_DELAY_DATA.append(TX_RX_NON_DELAY_VALUE)
        if variables['hex_format'] == True:
            MAG_E1_HEX_DATA.append('0x' + hex(MAG_E1_VALUE)[2:].zfill(4))
            PHASE_E1_HEX_VALUE = hex(int(PHASE_E1_BIN_VALUE,2))
            PHASE_E2_HEX_VALUE = hex(int(PHASE_E2_BIN_VALUE,2))
            PHASE_E3_HEX_VALUE = hex(int(PHASE_E3_BIN_VALUE,2))
            PHASE_E4_HEX_VALUE = hex(int(PHASE_E4_BIN_VALUE,2))
            PHASE_E1_HEX_VALUE = '0x' + PHASE_E1_HEX_VALUE[2:].zfill(4)
            PHASE_E2_HEX_VALUE = '0x' + PHASE_E2_HEX_VALUE[2:].zfill(4)
            PHASE_E3_HEX_VALUE = '0x' + PHASE_E3_HEX_VALUE[2:].zfill(4)
            PHASE_E4_HEX_VALUE = '0x' + PHASE_E4_HEX_VALUE[2:].zfill(4)
            PHASE_E1_HEX_DATA.append(PHASE_E1_HEX_VALUE)
            PHASE_E2_HEX_DATA.append(PHASE_E2_HEX_VALUE)
            PHASE_E3_HEX_DATA.append(PHASE_E3_HEX_VALUE)
            PHASE_E4_HEX_DATA.append(PHASE_E4_HEX_VALUE)
        
    if variables['dec_format'] == True:
        if variables['freq_sweep'] == True:
            if variables['value'] == '66':
                MAG_E1_DATA.insert(0, 'magnit_1_dbg_' + str(variables['frequency']))
                PHASE_E1_DATA.insert(0, 'phase_1_dbg_' + str(variables['frequency']))
                PHASE_E2_DATA.insert(0, 'phase_2_dbg_' + str(variables['frequency']))
                PHASE_E3_DATA.insert(0, 'phase_3_dbg_' + str(variables['frequency']))
                PHASE_E4_DATA.insert(0, 'phase_4_dbg_' + str(variables['frequency']))
            elif variables['value'] == '67':
                MAG_E1_DATA.insert(0, 'magnit_out_1_dbg_' + str(variables['frequency']))
                PHASE_E1_DATA.insert(0, 'phase_out_1_dbg_' + str(variables['frequency']))
                PHASE_E2_DATA.insert(0, 'phase_out_2_dbg_' + str(variables['frequency']))
                PHASE_E3_DATA.insert(0, 'phase_out_3_dbg_' + str(variables['frequency']))
                PHASE_E4_DATA.insert(0, 'phase_out_4_dbg_' + str(variables['frequency']))
            SAMPLE_CAL_DATA.insert(0, 'SAMPLE_CAL_' + str(variables['frequency']))
            TX_RX_NON_DELAY_DATA.insert(0, 'TX_RX_NON_DELAY_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            if variables['value'] == '66':
                MAG_E1_DATA.insert(0, 'magnit_1_dbg_' + str(variables['power_level']))
                PHASE_E1_DATA.insert(0,'phase_1_dbg_' + str(variables['power_level']))
                PHASE_E2_DATA.insert(0,'phase_2_dbg_' + str(variables['power_level']))
                PHASE_E3_DATA.insert(0,'phase_3_dbg_' + str(variables['power_level']))
                PHASE_E4_DATA.insert(0,'phase_4_dbg_' + str(variables['power_level']))
            elif variables['value'] == '61':
                MAG_E1_DATA.insert(0, 'magnit_out_1_dbg_' + str(variables['power_level']))
                PHASE_E1_DATA.insert(0,'phase_out_1_dbg_' + str(variables['power_level']))
                PHASE_E2_DATA.insert(0,'phase_out_2_dbg_' + str(variables['power_level']))
                PHASE_E3_DATA.insert(0,'phase_out_3_dbg_' + str(variables['power_level']))
                PHASE_E4_DATA.insert(0,'phase_out_4_dbg_' + str(variables['power_level']))                
            SAMPLE_CAL_DATA.insert(0, 'SAMPLE_CAL_' + str(variables['power_level']))
            TX_RX_NON_DELAY_DATA.insert(0, 'TX_RX_NON_DELAY_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            if variables['value'] == '66':
                MAG_E1_DATA.insert(0, 'magnit_1_dbg_' + str(variables['loop']))
                PHASE_E1_DATA.insert(0,'phase_1_dbg_' + str(variables['loop']))
                PHASE_E2_DATA.insert(0,'phase_2_dbg_' + str(variables['loop']))
                PHASE_E3_DATA.insert(0,'phase_3_dbg_' + str(variables['loop']))
                PHASE_E4_DATA.insert(0,'phase_4_dbg_' + str(variables['loop']))
            elif variables['value'] == '67':
                MAG_E1_DATA.insert(0, 'magnit_out_1_dbg_' + str(variables['loop']))
                PHASE_E1_DATA.insert(0,'phase_out_1_dbg_' + str(variables['loop']))
                PHASE_E2_DATA.insert(0,'phase_out_2_dbg_' + str(variables['loop']))
                PHASE_E3_DATA.insert(0,'phase_out_3_dbg_' + str(variables['loop']))
                PHASE_E4_DATA.insert(0,'phase_out_4_dbg_' + str(variables['loop']))                
            SAMPLE_CAL_DATA.insert(0, 'SAMPLE_CAL_' + str(variables['loop']))
            TX_RX_NON_DELAY_DATA.insert(0, 'TX_RX_NON_DELAY_' + str(variables['loop']))

        variables['dec_csv'].tuple_add(MAG_E1_DATA)
        variables['dec_csv'].tuple_add(PHASE_E1_DATA)
        variables['dec_csv'].tuple_add(PHASE_E2_DATA)
        variables['dec_csv'].tuple_add(PHASE_E3_DATA)
        variables['dec_csv'].tuple_add(PHASE_E4_DATA)
        variables['dec_csv'].tuple_add(SAMPLE_CAL_DATA)
        variables['dec_csv'].tuple_add(TX_RX_NON_DELAY_DATA)
        
    if variables['hex_format'] == True:
        if variables['freq_sweep'] == True:
            if variables['value'] == '66':
                MAG_E1_HEX_DATA.insert(0, 'magnit_1_dbg_' + str(variables['frequency']))
                PHASE_E1_HEX_DATA.insert(0, 'phase_1_dbg_' + str(variables['frequency']))
                PHASE_E2_HEX_DATA.insert(0, 'phase_2_dbg_' + str(variables['frequency']))
                PHASE_E3_HEX_DATA.insert(0, 'phase_3_dbg_' + str(variables['frequency']))
                PHASE_E4_HEX_DATA.insert(0, 'phase_4_dbg_' + str(variables['frequency']))
            elif variables['value'] == '67':
                MAG_E1_HEX_DATA.insert(0, 'magnit_out_1_dbg_' + str(variables['frequency']))
                PHASE_E1_HEX_DATA.insert(0, 'phase_out_1_dbg_' + str(variables['frequency']))
                PHASE_E2_HEX_DATA.insert(0, 'phase_out_2_dbg_' + str(variables['frequency']))
                PHASE_E3_HEX_DATA.insert(0, 'phase_out_3_dbg_' + str(variables['frequency']))
                PHASE_E4_HEX_DATA.insert(0, 'phase_out_4_dbg_' + str(variables['frequency']))               
        elif variables['power_sweep'] == True:
            if variables['value'] == '66':
                MAG_E1_HEX_DATA.insert(0, 'magnit_1_dbg_' + str(variables['power_level']))
                PHASE_E1_HEX_DATA.insert(0,'phase_1_dbg_' + str(variables['power_level']))
                PHASE_E2_HEX_DATA.insert(0,'phase_2_dbg_' + str(variables['power_level']))
                PHASE_E3_HEX_DATA.insert(0,'phase_3_dbg_' + str(variables['power_level']))
                PHASE_E4_HEX_DATA.insert(0,'phase_4_dbg_' + str(variables['power_level']))
            elif variables['value'] == '67':
                MAG_E1_DATA.insert(0, 'magnit_out_1_dbg_' + str(variables['power_level']))
                PHASE_E1_HEX_DATA.insert(0,'phase_out_1_dbg_' + str(variables['power_level']))
                PHASE_E2_HEX_DATA.insert(0,'phase_out_2_dbg_' + str(variables['power_level']))
                PHASE_E3_HEX_DATA.insert(0,'phase_out_3_dbg_' + str(variables['power_level']))
                PHASE_E4_HEX_DATA.insert(0,'phase_out_4_dbg_' + str(variables['power_level']))  
        elif variables['no_sweep'] == True:
            if variables['value'] == '66':
                MAG_E1_HEX_DATA.insert(0, 'magnit_1_dbg_' + str(variables['loop']))
                PHASE_E1_HEX_DATA.insert(0,'phase_1_dbg_' + str(variables['loop']))
                PHASE_E2_HEX_DATA.insert(0,'phase_2_dbg_' + str(variables['loop']))
                PHASE_E3_HEX_DATA.insert(0,'phase_3_dbg_' + str(variables['loop']))
                PHASE_E4_HEX_DATA.insert(0,'phase_4_dbg_' + str(variables['loop']))
            elif variables['value'] == '67':
                MAG_E1_DATA.insert(0, 'magnit_out_1_dbg_' + str(variables['loop']))
                PHASE_E1_HEX_DATA.insert(0,'phase_out_1_dbg_' + str(variables['loop']))
                PHASE_E2_HEX_DATA.insert(0,'phase_out_2_dbg_' + str(variables['loop']))
                PHASE_E3_HEX_DATA.insert(0,'phase_out_3_dbg_' + str(variables['loop']))
                PHASE_E4_HEX_DATA.insert(0,'phase_out_4_dbg_' + str(variables['loop']))  

        variables['hex_csv'].tuple_add(MAG_E1_HEX_DATA)
        variables['hex_csv'].tuple_add(PHASE_E1_HEX_DATA)
        variables['hex_csv'].tuple_add(PHASE_E2_HEX_DATA)
        variables['hex_csv'].tuple_add(PHASE_E3_HEX_DATA)
        variables['hex_csv'].tuple_add(PHASE_E4_HEX_DATA)
        variables['hex_csv'].tuple_add(SAMPLE_CAL_DATA)
        variables['hex_csv'].tuple_add(TX_RX_NON_DELAY_DATA)

# as of E056, Bearing Processor Calibration and TX Monitor Alignment Vectors corresponds to indices:
# 69
# Bearing Processor Calibration and TX Monitor Alignment Vectors
# [63] = xpdr_tx_brkt_non_delay
# [62] = tx_rx_non_delay
# [61] = tx_mon1b_non_delay 
# [60] = xpdr_tx_monitor_adjusted
# [59] = tcas_tx_monitor_adjusted
# [58] = bp_tx_monitor_adjusted 
# [57] = samplecal_non_delay
# [56] = tcas_samplecal_ld_adjusted 
# [55] = bp_samplecal_adjusted
# [54] = bp_samplecal_ld_adjusted
# [53] = MSATC 
# [52] = STRB_MSS
# [51] = STRB_MSR
# [50:43] = tcas_log_sum 
# [42:33] = magnit_1_dbg  <= magnit_ffi(12 downto 3);
# [32:31] = "00" two padded zeroes
# [30:23] = tcas_logamp_out
# [22:13] = magnit_out_1_dbg  <= magnit_ffo(12 downto 3);
# [12:0] = phase_out_1_dbg   <= phase_ffo(15 downto  3);
def BEARING_PROCESSOR_TX_MONITOR(variables):
    # single bit
    xpdr_tx_brkt_non_delay = []
    tx_rx_non_delay = []
    tx_mon1b_non_delay = []
    xpdr_tx_monitor_adjusted = []
    tcas_tx_monitor_adjusted = []
    bp_tx_monitor_adjusted = []
    samplecal_non_delay = []
    tcas_samplecal_ld_adjusted = [] 
    bp_samplecal_adjusted = []
    bp_samplecal_ld_adjusted = []
    MSATC = []
    STRB_MSS = []
    STRB_MSR = []
    
    # multiple bits
    tcas_log_sum = []
    mag_e1 = []
    # mag_out_e2 = []
    tcas_logamp_out = []
    mag_out_e1 = []
    phase_out_e1 = []
    if variables['hex_format'] == True:
        print('logamp hex format true')
        tcas_log_sum_HEX = []
        mag_e1_HEX = []
        # mag_out_e2_HEX = []
        tcas_logamp_out_HEX = []
        mag_out_e1_HEX = []
        phase_out_e1_HEX = []        
    
    for i in range(len(variables['csv_data'])):
        current_data = bin(variables['csv_data'][i])[2:]
        current_data = current_data.zfill(64)
        # print(current_data)
        
        # single bit
        xpdr_tx_brkt_non_delay.append(current_data[0])
        tx_rx_non_delay.append(current_data[1])
        tx_mon1b_non_delay.append(current_data[2])
        xpdr_tx_monitor_adjusted.append(current_data[3])
        tcas_tx_monitor_adjusted.append(current_data[4])
        bp_tx_monitor_adjusted.append(current_data[5])
        samplecal_non_delay.append(current_data[6])
        tcas_samplecal_ld_adjusted.append(current_data[7])
        bp_samplecal_adjusted.append(current_data[8])
        bp_samplecal_ld_adjusted.append(current_data[9])
        MSATC.append(current_data[10])
        STRB_MSS.append(current_data[11])
        STRB_MSR.append(current_data[12])
        
        # multiple bits
        tcas_log_sum.append(int(current_data[13:21],2))
        mag_e1.append(int(current_data[21:31],2))
        # mag_out_e2.append(int(current_data[31:41],2))
        tcas_logamp_out.append(int(current_data[33:41],2))
        mag_out_e1.append(int(current_data[41:51],2))
        phase_out_e1_value = current_data[51:].zfill(13)
        phase_out_e1_value = twos_comp.convert(int(phase_out_e1_value, 2), len(phase_out_e1_value))
        phase_out_e1_bin_value = twos_comp.convert_return_binary_string(bin(phase_out_e1_value), 13)
        phase_out_e1.append(phase_out_e1_value)
        # print(current_data[31:41])
        if variables['hex_format'] == True:
            tcas_log_sum_HEX.append('0x' + hex(int(current_data[13:21],2))[2:].zfill(4))
            mag_e1_HEX.append('0x' + hex(int(current_data[21:31],2))[2:].zfill(4))
            # mag_out_e2_HEX.append('0x' + hex(int(current_data[31:41],2))[2:].zfill(4))
            tcas_logamp_out_HEX.append('0x' + hex(int(current_data[33:41],2))[2:].zfill(4))
            mag_out_e1_HEX.append('0x' + hex(int(current_data[41:51],2))[2:].zfill(4))
            phase_out_e1_hex_value = hex(int(phase_out_e1_bin_value,2))
            phase_out_e1_hex_value = '0x' + phase_out_e1_hex_value[2:].zfill(4)
            phase_out_e1_HEX.append(phase_out_e1_hex_value)
        
    if variables['dec_format'] == True:
        if variables['freq_sweep'] == True:
            xpdr_tx_brkt_non_delay.insert(0, 'xpdr_tx_brkt_non_delay_' + str(variables['frequency']))
            tx_rx_non_delay.insert(0, 'tx_rx_non_delay_' + str(variables['frequency']))
            tx_mon1b_non_delay.insert(0, 'tx_mon1b_non_delay_' + str(variables['frequency']))
            xpdr_tx_monitor_adjusted.insert(0, 'xpdr_tx_monitor_adjusted_' + str(variables['frequency']))
            tcas_tx_monitor_adjusted.insert(0, 'tcas_tx_monitor_adjusted_' + str(variables['frequency']))
            bp_tx_monitor_adjusted.insert(0, 'bp_tx_monitor_adjusted_' + str(variables['frequency']))
            samplecal_non_delay.insert(0, 'samplecal_non_delay_' + str(variables['frequency']))
            tcas_samplecal_ld_adjusted.insert(0, 'tcas_samplecal_ld_adjusted_' + str(variables['frequency']))
            bp_samplecal_adjusted.insert(0, 'bp_samplecal_adjusted_' + str(variables['frequency']))
            bp_samplecal_ld_adjusted.insert(0, 'bp_samplecal_ld_adjusted_' + str(variables['frequency']))
            MSATC.insert(0, 'MSATC_' + str(variables['frequency']))
            STRB_MSS.insert(0, 'STRB_MSS_' + str(variables['frequency']))
            STRB_MSR.insert(0, 'STRB_MSR_' + str(variables['frequency']))
            tcas_log_sum.insert(0, 'tcas_log_sum_' + str(variables['frequency']))
            mag_e1.insert(0, 'magnit_e1_dbg_' + str(variables['frequency']))
            # mag_out_e2.insert(0, 'magnit_out_2_dbg_' + str(variables['frequency']))
            tcas_logamp_out.insert(0, 'tcas_logamp_out_' + str(variables['frequency']))
            mag_out_e1.insert(0, 'magnit_out_1_dbg_' + str(variables['frequency']))
            phase_out_e1.insert(0, 'phase_out_1_dbg_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            xpdr_tx_brkt_non_delay.insert(0, 'xpdr_tx_brkt_non_delay_' + str(variables['power_level']))
            tx_rx_non_delay.insert(0, 'tx_rx_non_delay_' + str(variables['power_level']))
            tx_mon1b_non_delay.insert(0, 'tx_mon1b_non_delay_' + str(variables['power_level']))
            xpdr_tx_monitor_adjusted.insert(0, 'xpdr_tx_monitor_adjusted_' + str(variables['power_level']))
            tcas_tx_monitor_adjusted.insert(0, 'tcas_tx_monitor_adjusted_' + str(variables['power_level']))
            bp_tx_monitor_adjusted.insert(0, 'bp_tx_monitor_adjusted_' + str(variables['power_level']))
            samplecal_non_delay.insert(0, 'samplecal_non_delay_' + str(variables['power_level']))
            tcas_samplecal_ld_adjusted.insert(0, 'tcas_samplecal_ld_adjusted_' + str(variables['power_level']))
            bp_samplecal_adjusted.insert(0, 'bp_samplecal_adjusted_' + str(variables['power_level']))
            bp_samplecal_ld_adjusted.insert(0, 'bp_samplecal_ld_adjusted_' + str(variables['power_level']))
            MSATC.insert(0, 'MSATC_' + str(variables['power_level']))
            STRB_MSS.insert(0, 'STRB_MSS_' + str(variables['power_level']))
            STRB_MSR.insert(0, 'STRB_MSR_' + str(variables['power_level']))
            tcas_log_sum.insert(0, 'tcas_log_sum_' + str(variables['power_level']))
            mag_e1.insert(0, 'magnit_e1_dbg_' + str(variables['power_level']))
            # mag_out_e2.insert(0, 'magnit_out_2_dbg_' + str(variables['power_level']))
            tcas_logamp_out.insert(0, 'tcas_logamp_out_' + str(variables['power_level']))
            mag_out_e1.insert(0, 'magnit_out_1_dbg_' + str(variables['power_level']))
            phase_out_e1.insert(0, 'phase_out_1_dbg_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            xpdr_tx_brkt_non_delay.insert(0, 'xpdr_tx_brkt_non_delay_' + str(variables['loop']))
            tx_rx_non_delay.insert(0, 'tx_rx_non_delay_' + str(variables['loop']))
            tx_mon1b_non_delay.insert(0, 'tx_mon1b_non_delay_' + str(variables['loop']))
            xpdr_tx_monitor_adjusted.insert(0, 'xpdr_tx_monitor_adjusted_' + str(variables['loop']))
            tcas_tx_monitor_adjusted.insert(0, 'tcas_tx_monitor_adjusted_' + str(variables['loop']))
            bp_tx_monitor_adjusted.insert(0, 'bp_tx_monitor_adjusted_' + str(variables['loop']))
            samplecal_non_delay.insert(0, 'samplecal_non_delay_' + str(variables['loop']))
            tcas_samplecal_ld_adjusted.insert(0, 'tcas_samplecal_ld_adjusted_' + str(variables['loop']))
            bp_samplecal_adjusted.insert(0, 'bp_samplecal_adjusted_' + str(variables['loop']))
            bp_samplecal_ld_adjusted.insert(0, 'bp_samplecal_ld_adjusted_' + str(variables['loop']))
            MSATC.insert(0, 'MSATC_' + str(variables['loop']))
            STRB_MSS.insert(0, 'STRB_MSS_' + str(variables['loop']))
            STRB_MSR.insert(0, 'STRB_MSR_' + str(variables['loop']))
            tcas_log_sum.insert(0, 'tcas_log_sum_' + str(variables['loop']))
            mag_e1.insert(0, 'magnit_e1_dbg_' + str(variables['loop']))
            # mag_out_e2.insert(0, 'magnit_out_2_dbg_' + str(variables['loop']))
            tcas_logamp_out.insert(0, 'tcas_logamp_out_' + str(variables['loop']))
            mag_out_e1.insert(0, 'magnit_out_1_dbg_' + str(variables['loop']))
            phase_out_e1.insert(0, 'phase_out_1_dbg_' + str(variables['loop']))

        # multiple bits
        variables['dec_csv'].tuple_add(tcas_log_sum)
        variables['dec_csv'].tuple_add(mag_e1)
        # variables['dec_csv'].tuple_add(mag_out_e2)
        variables['dec_csv'].tuple_add(tcas_logamp_out)
        variables['dec_csv'].tuple_add(mag_out_e1)
        variables['dec_csv'].tuple_add(phase_out_e1)
        
        # single bit
        variables['dec_csv'].tuple_add(xpdr_tx_brkt_non_delay)
        variables['dec_csv'].tuple_add(tx_rx_non_delay)
        variables['dec_csv'].tuple_add(tx_mon1b_non_delay)
        variables['dec_csv'].tuple_add(xpdr_tx_monitor_adjusted)
        variables['dec_csv'].tuple_add(tcas_tx_monitor_adjusted)
        variables['dec_csv'].tuple_add(bp_tx_monitor_adjusted)
        variables['dec_csv'].tuple_add(samplecal_non_delay)
        variables['dec_csv'].tuple_add(tcas_samplecal_ld_adjusted)
        variables['dec_csv'].tuple_add(bp_samplecal_adjusted)
        variables['dec_csv'].tuple_add(bp_samplecal_ld_adjusted)
        variables['dec_csv'].tuple_add(MSATC)
        variables['dec_csv'].tuple_add(STRB_MSS)
        variables['dec_csv'].tuple_add(STRB_MSR)
        
    if variables['hex_format'] == True:
        if variables['freq_sweep'] == True:
            tcas_log_sum_HEX.insert(0, 'tcas_log_sum_' + str(variables['frequency']))
            mag_e1_HEX.insert(0, 'magnit_e1_dbg_' + str(variables['frequency']))
            # mag_out_e2_HEX.insert(0, 'magnit_out_2_dbg_' + str(variables['frequency']))
            tcas_logamp_out_HEX.insert(0, 'tcas_logamp_out_' + str(variables['frequency']))
            mag_out_e1_HEX.insert(0, 'magnit_out_1_dbg_' + str(variables['frequency']))
            phase_out_e1_HEX.insert(0, 'phase_out_1_dbg_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            tcas_log_sum_HEX.insert(0, 'tcas_log_sum_' + str(variables['power_level']))
            mag_e1_HEX.insert(0, 'magnit_e1_dbg_' + str(variables['power_level']))
            # mag_out_e2_HEX.insert(0, 'magnit_out_2_dbg_' + str(variables['power_level']))
            tcas_logamp_out_HEX.insert(0, 'tcas_logamp_out_' + str(variables['power_level']))
            mag_out_e1_HEX.insert(0, 'magnit_out_1_dbg_' + str(variables['power_level']))
            phase_out_e1_HEX.insert(0, 'phase_out_1_dbg_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            tcas_log_sum_HEX.insert(0, 'tcas_log_sum_' + str(variables['loop']))
            mag_e1_HEX.insert(0, 'magnit_e1_dbg_' + str(variables['loop']))
            # mag_out_e2_HEX.insert(0, 'magnit_out_2_dbg_' + str(variables['loop']))
            tcas_logamp_out_HEX.insert(0, 'tcas_logamp_out_' + str(variables['loop']))
            mag_out_e1_HEX.insert(0, 'magnit_out_1_dbg_' + str(variables['loop']))
            phase_out_e1_HEX.insert(0, 'phase_out_1_dbg_' + str(variables['loop']))

        # multiple bits
        variables['hex_csv'].tuple_add(tcas_log_sum_HEX)
        variables['hex_csv'].tuple_add(mag_e1_HEX)
        # variables['hex_csv'].tuple_add(mag_out_e2_HEX)
        variables['hex_csv'].tuple_add(tcas_logamp_out_HEX)
        variables['hex_csv'].tuple_add(mag_out_e1_HEX)
        variables['hex_csv'].tuple_add(phase_out_e1_HEX)
        
        # single bit
        variables['hex_csv'].tuple_add(xpdr_tx_brkt_non_delay)
        variables['hex_csv'].tuple_add(tx_rx_non_delay)
        variables['hex_csv'].tuple_add(tx_mon1b_non_delay)
        variables['hex_csv'].tuple_add(xpdr_tx_monitor_adjusted)
        variables['hex_csv'].tuple_add(tcas_tx_monitor_adjusted)
        variables['hex_csv'].tuple_add(bp_tx_monitor_adjusted)
        variables['hex_csv'].tuple_add(samplecal_non_delay)
        variables['hex_csv'].tuple_add(tcas_samplecal_ld_adjusted)
        variables['hex_csv'].tuple_add(bp_samplecal_adjusted)
        variables['hex_csv'].tuple_add(bp_samplecal_ld_adjusted)
        variables['hex_csv'].tuple_add(MSATC)
        variables['hex_csv'].tuple_add(STRB_MSS)
        variables['hex_csv'].tuple_add(STRB_MSR)
            
# as of E04E, Bearing Processor RX Alignment Vectors corresponds to indices:
# 69
# Bearing Processor RX Alignment Vectors
# [63] = xpdr_tx_brkt_non_delay
# [62] = tx_rx_non_delay
# [61] = PLOC preamble lock
# [60] = QV quantized video
# [59] = COND confidence data
# [58] = bp_mode_s_enable_adjusted
# [57] = SLE sum leading edge
# [56] = RPD reply data shift register
# [55] = LDB repd and ldb signals range counter not active
# [54:40] = GARBUS(14 downto 0)
# [39:32] = tcas_log_sum
# [31:16] = magnit_ffo(15 downto 0)
# [15:0] = phase_ffo(15 downto 0)
def BEARING_PROCESSOR_RX_ALIGNMENT_VECTORS(variables):
    # single bit
    xpdr_tx_brkt_non_delay = []
    tx_rx_non_delay = []
    PLOC = [] # preamble lock
    QV = [] # quantized video
    COND = [] # confidence data
    bp_mode_s_enable_adjusted = []
    SLE =[] # sum leading edge
    RPD = [] # reply data shift register
    LBD = [] # repd and ldb signal range counter not acive
    
    # multiple bits
    GARBUS = []
    tcas_log_sum = []
    magnit_ffo = []
    phase_ffo = []
    if variables['hex_format'] == True:
        GARBUS_HEX = []
        tcas_log_sum_HEX = []
        magnit_ffo_HEX = []
        phase_ffo_HEX = []
        
    for i in range(len(variables['csv_data'])):
        current_data = bin(variables['csv_data'][i])[2:]
        current_data = current_data.zfill(64)

        # single bit
        xpdr_tx_brkt_non_delay.append(current_data[0])
        tx_rx_non_delay.append(current_data[1])
        PLOC.append(current_data[2])
        QV.append(current_data[3])
        COND.append(current_data[4])
        bp_mode_s_enable_adjusted.append(current_data[5])
        SLE.append(current_data[6])
        RPD.append(current_data[7])
        LBD.append(current_data[8])
        
        # multiple bits
        GARBUS.append(int(current_data[9:24],2))
        tcas_log_sum.append(int(current_data[24:32],2))
        magnit_ffo.append(int(current_data[32:48],2))
        phase_ffo_value = current_data[48:].zfill(16)
        phase_ffo_value = twos_comp.convert(int(phase_ffo_value, 2), len(phase_ffo_value))
        phase_ffo_bin_value = twos_comp.convert_return_binary_string(bin(phase_ffo_value),16)
        phase_ffo.append(phase_ffo_value)
        
        if variables['hex_format'] == True:
            GARBUS_HEX.append('0x' + hex(int(current_data[9:24],2))[2:].zfill(4))
            tcas_log_sum_HEX.append('0x' + hex(int(current_data[24:32],2))[2:].zfill(4))
            magnit_ffo_HEX.append('0x' + hex(int(current_data[32:48],2))[2:].zfill(4))
            phase_ffo_hex_value = hex(int(phase_ffo_bin_value,2))
            phase_ffo_hex_value = '0x' + phase_ffo_hex_value[2:].zfill(4)
            phase_ffo_HEX.append(phase_ffo_hex_value)
        
    if variables['dec_format'] == True:
        if variables['freq_sweep'] == True:
            xpdr_tx_brkt_non_delay.insert(0, 'xpdr_tx_brkt_non_delay_' + str(variables['frequency']))
            tx_rx_non_delay.insert(0, 'tx_rx_non_delay_' + str(variables['frequency']))
            PLOC.insert(0, 'PLOC_' + str(variables['frequency']))
            QV.insert(0, 'QV_' + str(variables['frequency']))
            COND.insert(0, 'COND_' + str(variables['frequency']))
            bp_mode_s_enable_adjusted.insert(0, 'bp_mode_s_enable_adjusted_' + str(variables['frequency']))
            SLE.insert(0, 'SLE_' + str(variables['frequency']))
            RPD.insert(0, 'RPD_' + str(variables['frequency']))
            LBD.insert(0, 'LBD_' + str(variables['frequency']))
            GARBUS.insert(0, 'GARBUS_' + str(variables['frequency']))
            tcas_log_sum.insert(0, 'tcas_log_sum_' + str(variables['frequency']))
            magnit_ffo.insert(0, 'magnit_ffo_' + str(variables['frequency']))
            phase_ffo.insert(0, 'phase_ffo_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            xpdr_tx_brkt_non_delay.insert(0, 'xpdr_tx_brkt_non_delay_' + str(variables['power_level']))
            tx_rx_non_delay.insert(0, 'tx_rx_non_delay_' + str(variables['power_level']))
            PLOC.insert(0, 'PLOC_' + str(variables['power_level']))
            QV.insert(0, 'QV_' + str(variables['power_level']))
            COND.insert(0, 'COND_' + str(variables['power_level']))
            bp_mode_s_enable_adjusted.insert(0, 'bp_mode_s_enable_adjusted_' + str(variables['power_level']))
            SLE.insert(0, 'SLE_' + str(variables['power_level']))
            RPD.insert(0, 'RPD_' + str(variables['power_level']))
            LBD.insert(0, 'LBD_' + str(variables['power_level']))
            GARBUS.insert(0, 'GARBUS_' + str(variables['power_level']))
            tcas_log_sum.insert(0, 'tcas_log_sum_' + str(variables['power_level']))
            magnit_ffo.insert(0, 'magnit_ffo_' + str(variables['power_level']))
            phase_ffo.insert(0, 'phase_ffo_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            xpdr_tx_brkt_non_delay.insert(0, 'xpdr_tx_brkt_non_delay_' + str(variables['loop']))
            tx_rx_non_delay.insert(0, 'tx_rx_non_delay_' + str(variables['loop']))
            PLOC.insert(0, 'PLOC_' + str(variables['loop']))
            QV.insert(0, 'QV_' + str(variables['loop']))
            COND.insert(0, 'COND_' + str(variables['loop']))
            bp_mode_s_enable_adjusted.insert(0, 'bp_mode_s_enable_adjusted_' + str(variables['loop']))
            SLE.insert(0, 'SLE_' + str(variables['loop']))
            RPD.insert(0, 'RPD_' + str(variables['loop']))
            LBD.insert(0, 'LBD_' + str(variables['loop']))
            GARBUS.insert(0, 'GARBUS_' + str(variables['loop']))
            tcas_log_sum.insert(0, 'tcas_log_sum_' + str(variables['loop']))
            magnit_ffo.insert(0, 'magnit_ffo_' + str(variables['loop']))
            phase_ffo.insert(0, 'phase_ffo_' + str(variables['loop']))
    
        # multiple bits
        variables['dec_csv'].tuple_add(GARBUS)
        variables['dec_csv'].tuple_add(tcas_log_sum)
        variables['dec_csv'].tuple_add(magnit_ffo)
        variables['dec_csv'].tuple_add(phase_ffo)
        
        # single bit
        variables['dec_csv'].tuple_add(xpdr_tx_brkt_non_delay)
        variables['dec_csv'].tuple_add(tx_rx_non_delay)
        variables['dec_csv'].tuple_add(PLOC)
        variables['dec_csv'].tuple_add(QV)
        variables['dec_csv'].tuple_add(COND)
        variables['dec_csv'].tuple_add(bp_mode_s_enable_adjusted)
        variables['dec_csv'].tuple_add(SLE)
        variables['dec_csv'].tuple_add(RPD)
        variables['dec_csv'].tuple_add(LBD)
        
    if variables['hex_format'] == True:
        if variables['freq_sweep'] == True:
            GARBUS_HEX.insert(0, 'GARBUS_' + str(variables['frequency']))
            tcas_log_sum_HEX.insert(0, 'tcas_log_sum_' + str(variables['frequency']))
            magnit_ffo_HEX.insert(0, 'magnit_ffo_' + str(variables['frequency']))
            phase_ffo_HEX.insert(0, 'phase_ffo_' + str(variables['frequency']))
        elif variables['power_sweep'] == True:
            GARBUS_HEX.insert(0, 'GARBUS_' + str(variables['power_level']))
            tcas_log_sum_HEX.insert(0, 'tcas_log_sum_' + str(variables['power_level']))
            magnit_ffo_HEX.insert(0, 'magnit_ffo_' + str(variables['power_level']))
            phase_ffo_HEX.insert(0, 'phase_ffo_' + str(variables['power_level']))
        elif variables['no_sweep'] == True:
            GARBUS_HEX.insert(0, 'GARBUS_' + str(variables['loop']))
            tcas_log_sum_HEX.insert(0, 'tcas_log_sum_' + str(variables['loop']))
            magnit_ffo_HEX.insert(0, 'magnit_ffo_' + str(variables['loop']))
            phase_ffo_HEX.insert(0, 'phase_ffo_' + str(variables['loop']))
    
        # multiple bits
        variables['hex_csv'].tuple_add(GARBUS_HEX)
        variables['hex_csv'].tuple_add(tcas_log_sum_HEX)
        variables['hex_csv'].tuple_add(magnit_ffo_HEX)
        variables['hex_csv'].tuple_add(phase_ffo_HEX)
        
        # single bit
        variables['hex_csv'].tuple_add(xpdr_tx_brkt_non_delay)
        variables['hex_csv'].tuple_add(tx_rx_non_delay)
        variables['hex_csv'].tuple_add(PLOC)
        variables['hex_csv'].tuple_add(QV)
        variables['hex_csv'].tuple_add(COND)
        variables['hex_csv'].tuple_add(bp_mode_s_enable_adjusted)
        variables['hex_csv'].tuple_add(SLE)
        variables['hex_csv'].tuple_add(RPD)
        variables['hex_csv'].tuple_add(LBD)
            
# end, updated for revision E044