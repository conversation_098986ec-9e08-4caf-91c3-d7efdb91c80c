# -*- coding: utf-8 -*-
"""

@author: <PERSON>282068
         <PERSON><PERSON><PERSON><PERSON>
         CNS QUALIFICATION TEST GROUP
         
Discription:
Requirement: This script implements the DO-181E MOPs requirement for
             Pulse Decoder Characterics, Section *******
             
             Step4: Pulse Position Tolerances, P4, ATCRBS Mode A/ModeS All-Calls.
             Vary the P3-P4 spacing with the required acceptance range.  Determine
             the performace requirements as in Step 3.
			 
             
INPUTS:      RM, ATC, PathLoss
OUTPUTS:     ModeAS_Replies - array of %replies for min,max and outof bounds P3/P4 position tolerances

HISTORY:

04/23/2020   MRS    Initial Release.
05/11/2020   MRS    Cleanup
03/03/2021   MRS    Updates for new Handlers and Lobster.
05/20/2024   CS     Edited for ATR Pre-qual
                                 
"""

#Required Libraries
import time

#Map to Common Resource Folder
from TXDLib.Handlers import ate_rm
from TXDLib.Handlers import ATC5000NG

##############################################################################
################# MAIN     ##################################################
##############################################################################

def Test_2_3_2_5_Step4(rm, atc,PathLoss):
    
    rm.logMessage(2,"*** DO-181E, Pulse Decoder Characterics: Sect *******_Step4 ***")
    
    
    #Results read by TestStand
    ModeAS_Replies = [0.0,0.0,0.0]                                #Values read by TestStand
    
    #Initialize ATC to Transponder mode
    atc.transponderMode()
       
    #Initialize Aircraft Position
    atc.init_own_aircraft_pos()
    
    #Set the Cable Loss
    #atc.set_cable_loss(str(top_loss), str(bot_loss))
    
    #Set Up Transponder -- MODE S All Call - changed from AS
    atc.transponderModeS()
    atc.gwrite(":ATC:XPDR:ANT:POW 3")  #Set Power Deviation for Bot Antenna     
    
    
    #Turn on RF
    atc.gwrite(":ATC:XPDR:RF ON")
    time.sleep(15)
    
    rm.logMessage(0,"Test_2_3_2_5_Step4a - ModeA/S P3P4 2.2usec")   
    atc.gwrite(":ATC:XPDR:PUL:P34SPACING 2.1")
    time.sleep(15)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
       
    ModeAS_Replies[0] = replyrate[3]   # %Bot Mode AS
    

    rm.logMessage(0,"Test_2_3_2_5_Step4b - ModeA/s P3P4 1.8usec")   
    atc.gwrite(":ATC:XPDR:PUL:P34SPACING 1.9")
    time.sleep(15)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    
    ModeAS_Replies[1] = replyrate[3]    # %Bot Mode AS
 
    rm.logMessage(0,"Test_2_3_2_5_Step4c - ModeA/S P3P4 3.0usec")   
    atc.gwrite(":ATC:XPDR:PUL:P34SPACING 3.0")
    time.sleep(15)
    atc.waitforstatus()

    replyrate = atc.getPercentReply(2)
    # fix for erroneous reply rate
    count = 0
    while replyrate[1] == -1.0 and count < 10:
        replyrate = atc.getPercentReply(2)
        count = count + 1
    
    ModeAS_Replies[2] = replyrate[3]    # %Bot Mode AS, should be <10% out of tolerance spacing
    

    #Turn Off RF
    atc.gwrite(":ATC:XPDR:RF OFF")
    rm.logMessage(0,"Test_2_3_2_5_Step4 - Done")    

    
    rm.logMessage(2,"Done, closing session")
    
    return ModeAS_Replies

##########################################################################################
#run as main from command line
if __name__ == "__main__":
    rm = ate_rm()

    #Initiazlie the ATC
    atc_obj = ATC5000NG(rm)
    atc_obj.Reset()    

     
    res = Test_2_3_2_5_Step4(rm,atc_obj,12.0)
    
    atc_obj.close()

